import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  HostListener,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  Type,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'

import {
  CaseInfoFacade,
  DocumentViewerLogModel,
  DocumentsFacade,
  FieldSelectorViewModel,
  FulltextFacade,
  RightModel,
  SearchFacade,
  SearchResponseModel,
  SearchResultFacade,
  StartupsFacade,
  UserRights,
  Viewer,
  TiffViewerPayload,
  ImageTypeExportDetail,
  ImageType,
  PdfViewerFacade,
  CompositeLayoutFacade,
  CompositeLayoutState,
  ReviewPanelType,
} from '@venio/data-access/review'
import {
  Subject,
  combineLatest,
  filter,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs'
import {
  LayoutModule,
  SelectEvent,
  TabStripComponent,
} from '@progress/kendo-angular-layout'
import {
  WindowMessengerService,
  WindowMessage,
  WindowMessageType,
  TiffAnnotationSavedFrom,
} from '../../../services/window.messenger.service'
import { UserGroupRightCheckDirective } from '@venio/feature/shared/directives'
import { Remote, releaseProxy, wrap } from 'comlink'
import { environment } from '@venio/shared/environments'
import { AuthStorageKeys } from '@venio/data-access/auth'
import { ControlSettingService } from '@venio/data-access/control-settings'
import { ActivatedRoute } from '@angular/router'
import {
  DocumentPrefetchWorkerModel,
  NativePrefetchDocumentModel,
} from '@venio/shared/models/interfaces'
import { LocalStorage } from '@venio/shared/storage'
import { LocalStorageKeys } from '@venio/shared/models/constants'

declare type NativeDocumentPrefetchAction = (
  data: DocumentPrefetchWorkerModel
) => Array<number>
@Component({
  selector: 'venio-viewer-container',
  standalone: true,
  imports: [CommonModule, LayoutModule, UserGroupRightCheckDirective],
  templateUrl: './viewer-container.component.html',
  styleUrls: ['./viewer-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [],
})
export class ViewerContainerComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  public pdfViewerComponent: Promise<Type<unknown>>

  public tiffViewerComponent: Promise<Type<unknown>>

  public nearNativeViewerComponent: Promise<Type<unknown>>

  public fulltextViewerComponent: Promise<Type<unknown>>

  public transcriptViewerComponent: Promise<Type<unknown>>

  private toDestroy$: Subject<void> = new Subject<void>()

  private isNearNativeLoaded: boolean

  private isFulltextViewerLoaded: boolean

  private isPdfViewerLoaded: boolean

  private isTiffViewerLoaded: boolean

  private isTranscriptViewerLoaded: boolean

  private activeViewerTab: string

  public currentFileId: number

  private previousFileId: number

  private imageTypeExportDetail: ImageTypeExportDetail

  public UserRights = UserRights

  private errorredFileIds: Set<number> = new Set()

  private isViewerClosed = false

  private selectedFulltextFields: Array<FieldSelectorViewModel> = []

  @ViewChild('tabstrip')
  public tabstripComponent: TabStripComponent

  public isImageTypePdf: boolean

  public hasImageViewerRight: boolean

  public isTranscriptViewerEnabled: boolean

  public hasTranscriptViewerRight: boolean

  public hasNativeViewerRight: boolean

  public hasFulltextViewerRight: boolean

  public ReviewPanelType = ReviewPanelType

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  private get defaultViewer(): string {
    return this.activatedRoute.snapshot.queryParams?.defaultViewer ?? ''
  }

  public get isTranscriptDefaultViewer(): boolean {
    return this.defaultViewer.toLowerCase() === 'transcript'
  }

  private layoutFacade: CompositeLayoutFacade = inject(CompositeLayoutFacade)

  public layoutState: CompositeLayoutState = inject(CompositeLayoutState)

  public get isReviewPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isReviewPanelPopout')
  }

  public selectedTabTitle = ''

  constructor(
    private documentFacade: DocumentsFacade,
    private searchFacade: SearchFacade,
    private startupFacade: StartupsFacade,
    private searchResultFacade: SearchResultFacade,
    private fulltextFacade: FulltextFacade,
    private pdfViewerFacade: PdfViewerFacade,
    private caseInfoFacade: CaseInfoFacade,
    private cdr: ChangeDetectorRef,
    private messengerService: WindowMessengerService,
    private controlSettingService: ControlSettingService,
    private activatedRoute: ActivatedRoute,
    private caseFacade: CaseInfoFacade
  ) {}

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  @HostListener('window:beforeunload')
  public readonly beforeUnload = (): boolean => {
    this.sendMessageToOpenerWindow(WindowMessageType.VIEWER_CLOSED, {
      fulltextFields: this.selectedFulltextFields,
    })
    const shouldLeave = LocalStorage.get<boolean>(
      LocalStorageKeys.ShouldCloseAllPopoutWindows
    )
    return shouldLeave || confirm('Are you sure you want to leave?')
  }

  public ngOnInit(): void {
    this.caseFacade.fetchCaseInfoById(this.projectId)
    this.documentFacade.onViewerComponentReady
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((viewer: Viewer) => {
        if (viewer === Viewer.PDF)
          this.documentFacade.loadPDF = {
            fileId: this.currentFileId,
            isRefresh: false,
          }
        else if (viewer === Viewer.Native)
          this.documentFacade.loadNearNative = this.currentFileId
        else if (viewer === Viewer.Fulltext)
          this.documentFacade.loadFulltextViewer = this.currentFileId
        else if (viewer === Viewer.Tiff) {
          this.documentFacade.loadTiffViewer = {
            fileId: this.currentFileId,
            projectId: this.projectId,
          } as TiffViewerPayload
        }
      })
    this.getExportId()
    this.getCurrentDocument()
    //this.handleFileIdChanged()
    this.handleMessageFromMessenger()
    this.#handleTranscriptRequestFromParentWindow()

    this.selectFullltextFields()
    this.selectIsImageTypePdf()
    this.selectViewerRights()
    this.selectIsTranscriptViewerEnabled()
  }

  public hasViewerPanelInLayout(panelName: string): boolean {
    return this.layoutState
      .userSelectedLayout()
      ?.layoutPanels?.some((f) => f.panelName === panelName && f.isSelected)
  }

  private selectIsImageTypePdf(): void {
    this.caseInfoFacade.selectIsImageTypePdfProject$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((isImageTypePdf) => {
        this.cdr.markForCheck()
        this.isImageTypePdf = isImageTypePdf
      })
  }

  private selectIsTranscriptViewerEnabled(): void {
    this.caseInfoFacade.selectIsTranscriptViewerEnabled$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((isTranscriptViewerEnabled) => {
        this.cdr.markForCheck()
        this.isTranscriptViewerEnabled = isTranscriptViewerEnabled
      })
  }

  private selectViewerRights(): void {
    combineLatest([
      this.startupFacade.hasGroupRight$(UserRights.ALLOW_TO_VIEW_NATIVE_VIEWER),
      this.startupFacade.hasGroupRight$(
        UserRights.ALLOW_TO_VIEW_FULLTEXT_VIEWER
      ),
      this.startupFacade.hasGroupRight$(UserRights.ALLOW_TO_VIEW_TIFF_VIEWER),
      this.startupFacade.hasGroupRight$(UserRights.ALLOW_TO_VIEW_TRANSCRIPT),
    ])
      .pipe(take(1))
      .subscribe(
        ([
          hasNativeViewerRight,
          hasFulltextViewerRight,
          hasImageViewerRight,
          hasTranscriptViewerRight,
        ]) => {
          this.hasNativeViewerRight = hasNativeViewerRight
          ;(this.hasFulltextViewerRight = hasFulltextViewerRight),
            (this.hasImageViewerRight = hasImageViewerRight)
          this.hasTranscriptViewerRight = hasTranscriptViewerRight
        }
      )
  }

  private selectFullltextFields(): void {
    this.fulltextFacade.fetchMetadata$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((fields: Array<FieldSelectorViewModel>) => {
        this.selectedFulltextFields = fields
      })
  }

  public ngAfterViewInit(): void {
    this.sendMessageToOpenerWindow(WindowMessageType.POPUP_VIEWER_READY)
    setTimeout(() => {
      this.cdr.markForCheck()
      const index: number = this.defaultViewer
        ? this.tabstripComponent?.tabs?.length - 1
        : 0
      this.tabstripComponent.selectTab(index)

      this.activeViewerTab = this.defaultViewer
        ? this.tabstripComponent?.tabs?.last?.title
        : this.tabstripComponent?.tabs?.first?.title

      if (!this.activeViewerTab) {
        this.activeViewerTab = this.selectedTabTitle
      }

      this.loadSelectedViewer()
      this.saveDocumentViewerLog()
    })
    this.documentFacade.viewerContainerLoaded = true
    this.selectInitialTab()
  }

  public selectInitialTab(): void {
    if (
      this.hasViewerPanelInLayout(ReviewPanelType.NearNativeViewer) &&
      this.hasNativeViewerRight &&
      (this.currentFileId ?? 0) > 0
    ) {
      this.selectedTabTitle = 'Native'
    } else if (
      this.hasViewerPanelInLayout(ReviewPanelType.TextViewer) &&
      this.hasFulltextViewerRight &&
      (this.currentFileId ?? 0) > 0
    ) {
      this.selectedTabTitle = 'Fulltext'
    } else if (
      this.hasViewerPanelInLayout(ReviewPanelType.ImageViewer) &&
      this.hasImageViewerRight &&
      (this.currentFileId ?? 0) > 0
    ) {
      this.selectedTabTitle = this.isImageTypePdf ? 'PDF' : 'Tiff'
    } else if (
      this.isTranscriptViewerEnabled &&
      this.hasTranscriptViewerRight &&
      (this.isTranscriptDefaultViewer || (this.currentFileId ?? 0) > 0)
    ) {
      this.selectedTabTitle = 'Transcript'
    }

    this.handleTabChange(this.selectedTabTitle)
    this.cdr.markForCheck()
  }

  private handleTabChange(title: string): void {
    if (this.activeViewerTab === 'Tiff') {
      this.saveTiffAnnotations(TiffAnnotationSavedFrom.TAB_SELECT)
    }

    this.activeViewerTab = title
    this.loadSelectedViewer()
  }

  private getCurrentDocument(): void {
    this.documentFacade.onLoadViewerContainer
      .pipe(
        filter((fileId) => !!fileId && !this.isPopupWindow()),
        tap((fileId) => {
          this.previousFileId = this.currentFileId
          this.currentFileId = fileId
        }),
        switchMap((fileId) => {
          const data: NativePrefetchDocumentModel =
            this.getHtmlConversionQueuePayload()
          return this.searchResultFacade.getHtmlConversionElegibleFileIds(data)
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe((fileIds: number[]) => {
        this.cdr.markForCheck()
        if (this.activeViewerTab === 'Tiff')
          this.saveTiffAnnotations(TiffAnnotationSavedFrom.DOCUMENT_LOAD)
        else {
          this.handleDocumentLoadAndNativePrefetch(fileIds)
        }
      })
  }

  private handleDocumentLoadAndNativePrefetch(fileIds: number[]): void {
    this.loadSelectedViewer()
    if (this.previousFileId > 0 && this.previousFileId !== this.currentFileId) {
      this.saveDocumentViewerLog()
      this.previousFileId = this.currentFileId
    }
    this.prefetchNativeDocuments(fileIds)
  }

  private getExportId(): void {
    this.documentFacade.onLoadExportDetails
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((imageTypeExportDetail: ImageTypeExportDetail) => {
        this.imageTypeExportDetail = imageTypeExportDetail
      })
  }

  private getHtmlConversionQueuePayload(): NativePrefetchDocumentModel {
    const thresholdFileSize =
      this.controlSettingService.getControlSetting
        .HTML_CONVERSION_MAX_FILE_SIZE_FROM_VIEWER
    const invalidExtensions =
      this.controlSettingService.getControlSetting
        .HTML_CONVERSION_EXTENSION_FILTER
    const documentPrefetchCount = 5
    const data: NativePrefetchDocumentModel = {
      currentDocumentId: this.currentFileId,
      documentPrefetchCount,
      thresholdFileSize: thresholdFileSize * 1024,
      invalidFileExtensions: invalidExtensions
        .split(',')
        .map((type) => type.trim()),
    }
    return data
  }

  private async prefetchNativeDocuments(fileIds: number[]): Promise<void> {
    const documentPrefetchHandler = new Worker(
      new URL(
        '../../../workers/native-document-prefetch.worker',
        import.meta.url
      ),
      { type: 'module' }
    )
    const obj: Remote<NativeDocumentPrefetchAction> =
      wrap<NativeDocumentPrefetchAction>(documentPrefetchHandler)
    const filterFileIds = fileIds.filter(
      (fileId) => !this.errorredFileIds.has(fileId)
    )
    const erroredFileIds = await obj({
      fileIds: filterFileIds,
      projectId: this.projectId,
      accessToken: localStorage.getItem(AuthStorageKeys.AccessToken),
      serviceUrl: environment.apiUrl,
    })
    this.errorredFileIds = new Set([...this.errorredFileIds, ...erroredFileIds])
    //release the worker
    obj[releaseProxy]()
  }

  // private getActiveTab(): void {
  //   combineLatest([
  //     this.startupFacade.hasGroupRight$(UserRights.ALLOW_TO_VIEW_NATIVE_VIEWER),
  //     this.startupFacade.hasGroupRight$(
  //       UserRights.ALLOW_TO_VIEW_FULLTEXT_VIEWER
  //     ),
  //     this.startupFacade.hasGroupRight$(UserRights.ALLOW_TO_VIEW_TIFF_VIEWER),
  //   ])
  //     .pipe(
  //       filter(() => !this.activeViewerTab),
  //       takeUntil(this.toDestroy$)
  //     )
  //     .subscribe(([hasNativeRight, hasFulltextRight, hasImageRight]) => {
  //       this.cdr.markForCheck()
  //       if (hasNativeRight) this.activeViewerTab = 'Native'
  //       else if (hasFulltextRight) this.activeViewerTab = 'Fulltext'
  //       else if (hasImageRight) this.activeViewerTab = 'PDF'
  //     })
  // }

  private handleMessageFromMessenger(): void {
    this.messengerService.messageReceived
      .pipe(
        filter((message: WindowMessage) => !!message?.payload?.type),
        takeUntil(this.toDestroy$)
      )
      .subscribe((message: WindowMessage) => {
        if (message.payload.type === WindowMessageType.FILEID_CHANGED) {
          this.previousFileId = this.currentFileId
          this.currentFileId = message.payload.content.fileId
          this.documentFacade.setSelectedDocuments([this.currentFileId])
          this.loadSelectedViewer()
          if (
            this.previousFileId > 0 &&
            this.previousFileId !== this.currentFileId
          )
            this.saveDocumentViewerLog()
        } else if (message.payload.type === WindowMessageType.VIEW_DOCUMENT) {
          this.previousFileId = this.currentFileId
          this.currentFileId = message.payload.content.fileId
          this.documentFacade.setSelectedDocuments([this.currentFileId])
          this.searchFacade.setSearchResponse(
            message.payload.content.searchResponse as SearchResponseModel
          )
          this.startupFacade.setUserRights(
            message.payload.content.userRights as RightModel
          )
          this.fulltextFacade.fetchMetadata$.next(
            message.payload.content.fulltextFields
          )
          this.layoutState.userSelectedLayout.set(
            message.payload.content.selectedlayout
          )
          this.selectInitialTab()

          if (
            this.previousFileId > 0 &&
            this.previousFileId !== this.currentFileId
          )
            this.saveDocumentViewerLog()
        } else if (message.payload.type === WindowMessageType.VIEWER_CLOSED) {
          this.isViewerClosed = true
          this.fulltextFacade.fetchMetadata$.next(
            message.payload.content.fulltextFields
          )
        } else if (
          message.payload.type === WindowMessageType.TIFF_ANNOTATION_SAVED
        ) {
          if (
            message?.payload?.content?.message ===
            TiffAnnotationSavedFrom.DOCUMENT_LOAD
          )
            this.handleDocumentLoadAndNativePrefetch([])
        }
      })
  }

  private sendMessageToOpenerWindow(
    messageType: WindowMessageType,
    data = null
  ): void {
    if (!window.opener) return
    this.messengerService.sendMessage(
      {
        payload: { type: messageType, content: data },
      },
      window.opener
    )
  }

  private isPopupWindow(): boolean {
    return location.href.indexOf('viewer') > -1
  }

  private saveTiffAnnotations(message: TiffAnnotationSavedFrom): void {
    const tiffviewer = document.getElementById(
      'tiffViewer'
    ) as HTMLIFrameElement
    this.messengerService.sendMessage(
      {
        payload: {
          type: WindowMessageType.SAVE_TIFF_ANNOTATION,
          content: message,
        },
      },
      tiffviewer?.contentWindow
    )
  }

  public onTabSelect(e: SelectEvent): void {
    if (e?.title) {
      this.handleTabChange(e.title)
    }
  }

  private loadSelectedViewer(): void {
    const handler = (): void => {
      switch (this.activeViewerTab) {
        case 'PDF':
          this.loadPdfViewer()
          break
        case 'Native':
          this.loadNearNativeViewer()
          break
        case 'Fulltext':
          this.loadFulltextViewer()
          break
        case 'Tiff':
          this.loadTiffViewer()
          break
        case 'Transcript':
          this.loadTranscriptViewer()
          break
        default:
          this.loadNearNativeViewer()
          break
      }
    }
    this.pdfViewerFacade.executeWithAnnotationCheck(handler)
  }

  private async loadPdfViewer(): Promise<void> {
    this.cdr.markForCheck()
    if (!this.isPdfViewerLoaded) {
      this.pdfViewerComponent = import('@venio/feature/pdf-viewer').then(
        ({ FeaturePdfViewerComponent }) => FeaturePdfViewerComponent
      )
      this.isPdfViewerLoaded = true
    } else {
      if (this.imageTypeExportDetail.imageType === ImageType.OriginalImage) {
        this.documentFacade.loadPDF = {
          fileId: this.currentFileId,
          isRefresh: false,
        }
      } else {
        this.documentFacade.loadProducedPDF = {
          fileId: this.currentFileId,
          exportId: this.imageTypeExportDetail.exportId,
          isRefresh: false,
        }
      }
    }
  }

  private loadTiffViewer(): void {
    this.cdr.markForCheck()
    if (!this.isTiffViewerLoaded) {
      this.tiffViewerComponent = import('@venio/feature/tiff-viewer').then(
        ({ FeatureTiffViewerComponent }) => FeatureTiffViewerComponent
      )
      this.isTiffViewerLoaded = true
    } else
      this.documentFacade.loadTiffViewer = {
        fileId: this.currentFileId,
        projectId: this.projectId,
      } as TiffViewerPayload
  }

  private loadNearNativeViewer(): void {
    this.cdr.markForCheck()
    if (!this.isNearNativeLoaded) {
      this.nearNativeViewerComponent = import(
        '@venio/feature/near-native'
      ).then(({ NearNativeComponent }) => NearNativeComponent)
      this.isNearNativeLoaded = true
    }

    this.documentFacade.loadNearNative = this.currentFileId
  }

  private loadTranscriptViewer(): void {
    this.cdr.markForCheck()
    if (!this.isTranscriptViewerLoaded) {
      this.transcriptViewerComponent = import(
        '@venio/feature/transcript-viewer'
      ).then(
        ({ FeatureTranscriptViewerComponent }) =>
          FeatureTranscriptViewerComponent
      )
      this.isTranscriptViewerLoaded = true
    }
  }

  private loadFulltextViewer(): void {
    this.cdr.markForCheck()
    if (!this.isFulltextViewerLoaded) {
      this.fulltextViewerComponent = import(
        '@venio/feature/fulltext-viewer'
      ).then(
        ({ FeatureFulltextViewerComponent }) => FeatureFulltextViewerComponent
      )
      this.isFulltextViewerLoaded = true
    }
    this.documentFacade.loadFulltextViewer = this.currentFileId
  }

  private saveDocumentViewerLog(): void {
    if (this.currentFileId < 0 || this.projectId <= 0) {
      return
    }

    if (this.isViewerClosed) {
      this.isViewerClosed = false
      return
    }

    const payload: DocumentViewerLogModel = {
      viewedDate: null,
      viewedFrom: 'Search',
      fileId: this.currentFileId,
      viewerType: null,
    }

    this.documentFacade.saveDocumentViewerLog(this.projectId, payload)
  }

  #loadTranscriptViewer(currentFileId: number): void {
    const index: number = this.tabstripComponent?.tabs?.length - 1
    this.tabstripComponent.selectTab(index)
    this.activeViewerTab = this.tabstripComponent?.tabs?.last?.title
    this.loadSelectedViewer()
    this.saveDocumentViewerLog()
    if (!currentFileId) {
      this.documentFacade.setCurrentDocument(-1)
      this.documentFacade.setSelectedDocuments([-1])
    }
    this.cdr.markForCheck()
  }

  #handleTranscriptRequestFromParentWindow(): void {
    if (!this.isReviewPanelPopout) return
    this.messengerService.messageReceived
      .pipe(
        filter(
          (message: WindowMessage) =>
            Boolean(message?.payload?.type) &&
            this.isReviewPanelPopout &&
            message.payload.type === WindowMessageType.DATA_UPDATE &&
            Boolean(message.payload.content?.isTranscriptRequest)
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((message: WindowMessage) => {
        const { currentFileId } = message.payload.content
        this.#loadTranscriptViewer(currentFileId)
      })
  }
}
