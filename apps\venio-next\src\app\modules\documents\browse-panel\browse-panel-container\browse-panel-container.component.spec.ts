import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BrowsePanelContainerComponent } from './browse-panel-container.component'
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'

import { provideMockStore } from '@ngrx/store/testing'
import {
  DynamicFolderFacade,
  FieldFacade,
  FolderFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('BrowserPanelContainerComponent', () => {
  let component: BrowsePanelContainerComponent
  let fixture: ComponentFixture<BrowsePanelContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BrowsePanelContainerComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DynamicFolderFacade,
        provideMockStore({}),
        SearchFacade,
        FieldFacade,
        FolderFacade,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(BrowsePanelContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
