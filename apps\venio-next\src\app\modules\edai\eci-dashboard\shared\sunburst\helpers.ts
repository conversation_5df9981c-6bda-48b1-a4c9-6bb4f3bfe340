export const createMain = (data: any, field: string): any[] => {
  return data.map((item: any) => item[field])
}

export const createSub = (
  data: any,
  subField: string,
  subFields: string
): any[] => {
  return data.map((item: any) =>
    item[subFields].map((subItem: any) => `${item.id}: ${subItem[subField]}`)
  )
}

export const calcChartTotal = (data: any): number => {
  return data.reduce((acc: number, item: any) => acc + item.count, 0)
}

export const calcChildDataTotal = (data: any): number[] => {
  return data.map((item: any) => item.count)
}

export const calcMainPercent = (data: any, totalCount: number): number[] => {
  return data.map(
    (item: { count: number }, i: number) => (item.count / totalCount) * 100
  )
}

export const calcVariousSubPercents = (
  data: any,
  subTotal: number[],
  mainPercents: number[],
  field: string
): number[][] => {
  return data.map((item: any, i: number) => {
    const sum = item[field].map((subItem: any) => {
      // console.log("subItem.count: ", subItem, 'subTotal[i]: ', subTotal[i]);
      return (subItem.count / subTotal[i]) * mainPercents[i]
    })
    return sum
  })
}
export const calcAllvals = (
  mainPercents: number[],
  subPercents: number[][]
): number[] => {
  const flattenedSubPercents = subPercents.flat()

  return [100].concat(mainPercents).concat(flattenedSubPercents)
}

export const calcAllCounts = (data: any, field: string): number[] => {
  const allParentCounts = data.map((item: any) => item.count)
  const childCounts = data.map((item: any, i: number) => {
    return item[field].map((subItem: any) => subItem.count)
  })
  return [allParentCounts.reduce((acc: number, curr: number) => acc + curr, 0)]
    .concat(allParentCounts)
    .concat(childCounts.flat())
}

export function calculateSunburstData(sortedDocuTypes: any[]): any {
  const parentLabels = createMain(sortedDocuTypes, 'category')
  const childrenLabels = createSub(
    sortedDocuTypes,
    'subcategory',
    'subcategories'
  )
  const chartOneTotal = calcChartTotal(sortedDocuTypes)
  const chartOneSubTotal = calcChildDataTotal(sortedDocuTypes)
  const chartOnePercents = calcMainPercent(sortedDocuTypes, chartOneTotal)
  const chartOneChildPercents = calcVariousSubPercents(
    sortedDocuTypes,
    chartOneSubTotal,
    chartOnePercents,
    'subcategories'
  )
  const allCountsOne = calcAllCounts(sortedDocuTypes, 'subcategories')
  const allValsOne = calcAllvals(chartOnePercents, chartOneChildPercents)
  const formattedCounts = allCountsOne.map((count) => count.toLocaleString())

  return {
    parentLabels,
    childrenLabels,
    chartOneTotal,
    chartOneSubTotal,
    chartOnePercents,
    chartOneChildPercents,
    allCountsOne,
    allValsOne,
    formattedCounts,
  }
}
