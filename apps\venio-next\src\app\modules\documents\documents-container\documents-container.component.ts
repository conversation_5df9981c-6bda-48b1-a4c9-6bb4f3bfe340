import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  Inject,
  On<PERSON><PERSON>roy,
  OnInit,
  inject,
  Signal,
  viewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  IframeMessengerFacade,
  MessageType,
  WINDOW,
} from '@venio/data-access/iframe-messenger'
import {
  SplitterComponent,
  SplitterModule,
} from '@progress/kendo-angular-layout'
import { SkeletonModule } from '@progress/kendo-angular-indicators'
import { BrowsePanelContainerComponent } from '../browse-panel/browse-panel-container/browse-panel-container.component'
import { DocumentTableContainerComponent } from '../../document-table/document-table-container/document-table-container.component'
import { DocumentActionsToolbarComponent } from '../document-actions/document-actions-toolbar/document-actions-toolbar.component'
import { filter, take } from 'rxjs/operators'
import { map, Subject, takeUntil } from 'rxjs'
import {
  ReviewSetStateService,
  SearchFacade,
  SearchResultFacade,
} from '@venio/data-access/review'
import { NotificationDialogComponent } from '@venio/feature/notification'
import { DialogService } from '@progress/kendo-angular-dialog'
import { BreadcrumbContainerComponent } from '../breadcrumbs/breadcrumb-container/breadcrumb-container.component'
import { toSignal } from '@angular/core/rxjs-interop'

@Component({
  selector: 'venio-document-container',
  standalone: true,
  imports: [
    CommonModule,
    BrowsePanelContainerComponent,
    DocumentTableContainerComponent,
    BreadcrumbContainerComponent,
    DocumentActionsToolbarComponent,
    SkeletonModule,
    SplitterModule,
  ],
  templateUrl: './documents-container.component.html',
  styleUrls: ['./documents-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentsContainerComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  public readonly toDestroy$ = new Subject<void>()

  private searchFacade = inject(SearchFacade)

  private searchResultFacade = inject(SearchResultFacade)

  private dialogService = inject(DialogService)

  public reviewSetState = inject(ReviewSetStateService)

  private mainSplitter: Signal<SplitterComponent> = viewChild('mainSplitter')

  /**
   * Indicates the default theme layout or needs to hide for certain micro app
   */
  public isDefaultLayout = toSignal(
    this.iframeMessengerFacade
      .selectIframeMessengerContent$(MessageType.LAYOUT_CHANGE)
      .pipe(
        filter((d) => !!d),
        map((d) => typeof d !== 'undefined' && d !== null)
      ),
    {
      initialValue: true,
    }
  )

  constructor(
    private iframeMessengerFacade: IframeMessengerFacade,
    @Inject(WINDOW) private windowRef: Window
  ) {}

  public ngOnInit(): void {
    this.#selectSearchFailureResponse()
  }

  public ngAfterViewInit(): void {
    this.#handleBatchReviewMode()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  #selectSearchFailureResponse(): void {
    this.searchFacade.getSearchFailureResponse$
      .pipe(
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res) => {
        this.#resetSearchResultState()
        this.#showNotificationMessage(res.message)
        this.searchFacade.IsSearchLoading(false)
      })
  }

  #resetSearchResultState(): void {
    // Resets the search failure, loading states and clears the current search results.
    this.searchFacade.resetSearchState([
      'searchFailureResponse',
      'isSearchLoading',
    ])
    this.searchResultFacade.resetSearchResults()
    // Resets the document table's total record count, page size, etc.,
    this.searchFacade.clearSearchResult$.next()
  }

  /**
   * This function shows a notification message.
   * @param {string} content The content of the notification message.
   * @returns {void}
   */
  #showNotificationMessage(content = ''): void {
    const notificationDialogRef = this.dialogService.open({
      content: NotificationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-warning',
      width: '35rem',
    })

    // Set the dialog input
    this.#setDialogInput(notificationDialogRef.content.instance, content)

    notificationDialogRef.result
      .pipe(
        filter((result) => typeof result === 'boolean' && result),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        notificationDialogRef.close()
      })
  }

  /**
   * This function sets the input for the dialog.
   * @param instance The instance of the NotificationDialogComponent.
   * @param content The content of the notification message.
   */
  #setDialogInput(instance: NotificationDialogComponent, content = ''): void {
    // Set the title of the dialog
    instance.title = 'VenioOne OnDemand'
    // Set the message of the dialog
    instance.message = content
  }

  /**
   * Handle panel visibility for batch review mode
   * @returns {void}
   */
  #handleBatchReviewMode(): void {
    if (this.reviewSetState.isBatchReview()) {
      const panes = this.mainSplitter().panes

      // Hide browse and breadcrumb panels in batch review mode
      // This can be implemented by setting splitter pane sizes to 0 for the browse and breadcrumb panels
      // Then finally, disable the resizing altogether.
      panes.first.size = '0%'
      panes.forEach((pane) => (pane.resizable = false))
      // The document table pane should be set to 100% as it will be always visible
      panes.last.size = '100%'
    }
  }
}
