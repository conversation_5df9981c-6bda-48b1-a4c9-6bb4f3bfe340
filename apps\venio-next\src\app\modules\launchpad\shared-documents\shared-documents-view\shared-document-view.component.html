<kendo-dialog-titlebar (close)="closeSharedDocViewDialog()">
  <div class="t-flex t-justify-between t-w-full t-items-center">
    <div class="t-block">
      <span
        class="t-bg-[#0000000C] t-w-[40px] t-h-[40px] t-flex t-items-center t-justify-center t-rounded-full">
        <img
          src="assets/svg/share-svgrepo.svg"
          alt="Share Icon"
          style="width: 12px; height: 14px" />
      </span>

      Shared Document {{ selectedSharedDocData()?.shareName }}

      <span
        class="t-inline-block t-rounded t-px-3 t-py-1 t-text-uppercase t-text-xs t-bg-[#9BD2A7] t-ml-2">
        <span class="t-text-[#0F4B1B] t-tracking-widest">
          LINK EXPIRES ON
        </span>

        <span class="t-text-[#FFFFFF] t-tracking-wide">
          {{
            selectedSharedDocData()?.sharedExpiryDate | date : 'MM dd yyyy'
          }}</span
        >
      </span>

      <span class="t-text-[#263238] t-text-base t-ml-2">
        Number of documents {{ selectedSharedDocData()?.fileCountInFolder }}
      </span>
    </div>
  </div>
</kendo-dialog-titlebar>

<div class="t-h-full t-p-2">
  <div
    class="t-h-full t-p-4 t-bg-[#F9FBF4] t-rounded-lg t-shadow-sm t-flex-1 t-font-sans">
    <div class="t-flex t-flex-1 t-flex-row t-gap-8 t-items-start">
      <div class="t-flex t-flex-col t-w-1/4">
        <h3
          class="t-font-bold t-text-[var(--v-custom-sky-blue)] t-text-sm t-mb-4">
          Internal Users
        </h3>
        @for(user of selectedSharedDocData()?.internalUsers; track user.userId;)
        {
        <p class="t-text-gray-700 t-text-sm t-mb-1">{{ user?.userName }}</p>
        }
      </div>

      <div class="t-flex t-flex-col t-w-1/4">
        <h3
          class="t-font-bold t-text-[var(--v-custom-sky-blue)] t-text-sm t-mb-4">
          External Users
        </h3>
        @for(user of selectedSharedDocData()?.externalUsers; track user.userId;)
        {
        <p class="t-text-gray-700 t-text-sm t-mb-1">{{ user?.userName }}</p>
        }
      </div>

      <div class="t-flex t-flex-col t-w-1/4">
        <h3
          class="t-font-bold t-text-[var(--v-custom-sky-blue)] t-text-sm t-mb-4">
          Permission for external users
        </h3>
        <ul class="t-space-y-2">
          <li class="t-flex t-items-center !t-items-start t-flex-col t-w-full">
            <span class="t-font-bold t-text-sm t-mr-1">
              All to create document notes visible to all users
            </span>
            @if(selectedSharedDocData()?.allowToAddDocumentNotes)
            {<kendo-svg-icon
              [icon]="icons.tickIcon"
              class="t-text-[var(--kendo-success-100)]"></kendo-svg-icon
            >}
          </li>
          <li class="t-flex t-items-center !t-items-start t-flex-col t-w-full">
            <span class="t-font-bold t-text-sm t-mr-1"> Allow Redaction </span>
            @if(selectedSharedDocData()?.allowToApplyRedaction) {
            <kendo-svg-icon
              [icon]="icons.tickIcon"
              class="t-text-[var(--kendo-success-100)]"></kendo-svg-icon
            >}
          </li>
          <li class="t-flex t-items-center !t-items-start t-flex-col t-w-full">
            <span class="t-font-bold t-text-sm t-mr-1">
              Allow Tag / Untag
            </span>
            @if(selectedSharedDocData()?.allowToTagUntag){

            <kendo-svg-icon
              [icon]="icons.tickIcon"
              class="t-text-[var(--kendo-success-100)]"></kendo-svg-icon>
            }
          </li>
          <li class="t-flex t-items-center !t-items-start t-flex-col t-w-full">
            <span class="t-font-bold t-text-sm t-mr-1">
              View Analyze Page
            </span>
            @if(selectedSharedDocData()?.isWrite){
            <kendo-svg-icon
              [icon]="icons.tickIcon"
              class="t-text-[var(--kendo-success-100)]"></kendo-svg-icon
            >}
          </li>
        </ul>
      </div>

      <div
        class="t-flex t-flex-col t-w-1/4"
        venioDynamicHeight
        [isKendoDialog]="true">
        <h3
          class="t-font-bold t-text-[var(--v-custom-sky-blue)] t-text-sm t-mb-4">
          Description
        </h3>

        <div class="t-text-[#666666] t-text-sm t-leading-6 t-overflow-auto">
          {{ stripHtmlTags(selectedSharedDocData()?.shareInstruction) }}
        </div>
      </div>
    </div>
  </div>
</div>
