import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentMenuComponent } from './document-menu.component'
import { provideMockStore } from '@ngrx/store/testing'
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import {
  CaseInfoFacade,
  DocumentsFacade,
  FieldFacade,
  ReviewFacade,
  SearchFacade,
  ReviewSetStateService,
  ReviewsetFacade,
  SearchResultFacade,
  ReviewParamService,
} from '@venio/data-access/review'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { ActivatedRoute } from '@angular/router'
import { of, Observable } from 'rxjs'
import { ControlSettingService } from '@venio/data-access/control-settings'
import { VenioNotificationService } from '@venio/feature/notification'
import { DocumentTagUtilityService } from '../../document-utility/utility-services/document-tag-utility'
import { ConfirmationDialogService } from '../../../services/confirmation-dialog-service'
import { IframeMessengerService } from '@venio/data-access/iframe-messenger'

describe('DocumentMenuComponent', () => {
  let component: DocumentMenuComponent
  let fixture: ComponentFixture<DocumentMenuComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DocumentMenuComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: DocumentsFacade,
          useValue: {
            getSelectedDocuments$: of([]),
            getUnselectedDocuments$: of([]),
            getIsBatchSelected$: of(false),
            isDocumentMenuLoading$: of(false),
          },
        },
        {
          provide: ReviewFacade,
          useValue: {
            getReviewViewType$: of('list'),
            getVisibleEmailType$: of('all'),
            getInclusiveEmailStatus$: of('all'),
          },
        },
        {
          provide: CaseInfoFacade,
          useValue: {
            selectIsTranscriptViewerEnabled$: of(false),
            selectIsImageTypePdfProject$: of(false),
          },
        },
        {
          provide: SearchFacade,
          useValue: {
            getSearchResponse$: of({
              tempTables: { searchResultTempTable: 'test' },
            }),
          },
        },
        {
          provide: FieldFacade,
          useValue: {
            getAllFields$: of([]),
          },
        },
        // Add all missing dependencies
        {
          provide: ReviewSetStateService,
          useValue: {
            reviewsetBatchInfo: (): null => null,
            reviewSetId: (): number => 1,
            batchId: (): number => 1,
            isBatchReview: (): boolean => false,
          },
        },
        {
          provide: ReviewsetFacade,
          useValue: {
            markAsReviewedBulk$: (): Observable<{ status: string }> =>
              of({ status: 'Success' }),
            fetchReviewSetBatchInfo$: (): Observable<{ data: null }> =>
              of({ data: null }),
            checkInReviewBatch$: (): Observable<{ message: string }> =>
              of({ message: 'Success' }),
          },
        },
        {
          provide: SearchResultFacade,
          useValue: {
            updateIsReviewedFlag$: {
              next: (): void => {
                /* mock implementation */
              },
            },
          },
        },
        {
          provide: ReviewParamService,
          useValue: {
            projectId: of(1),
          },
        },
        {
          provide: ControlSettingService,
          useValue: {
            getControlSettings$: (): Observable<Record<string, unknown>> =>
              of({}),
          },
        },
        {
          provide: VenioNotificationService,
          useValue: {
            showSuccess: (): void => {
              /* mock implementation */
            },
            showError: (): void => {
              /* mock implementation */
            },
          },
        },
        {
          provide: DocumentTagUtilityService,
          useValue: {
            updateIsReviewedFlag: (): void => {
              /* mock implementation */
            },
          },
        },
        {
          provide: ConfirmationDialogService,
          useValue: {
            showConfirmationDialog: (): Observable<boolean> => of(true),
          },
        },
        {
          provide: IframeMessengerService,
          useValue: {
            sendMessage: (): void => {
              /* mock implementation */
            },
          },
        },
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({ projectId: '2' }),
            snapshot: {
              queryParams: {
                projectId: '2',
              },
            },
          },
        },
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentMenuComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
