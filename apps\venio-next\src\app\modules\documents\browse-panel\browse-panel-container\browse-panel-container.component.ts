import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule, NgComponentOutlet } from '@angular/common'
import { CommandsFacade } from '@venio/data-access/common'
import { FolderTabType } from '@venio/shared/models/constants'
import { BrowsePanelAutoFolderComponent } from '../browse-panel-auto-folder/browse-panel-auto-folder.component'
import { BrowsePanelCustomFolderComponent } from '../browse-panel-custom-folder/browse-panel-custom-folder.component'
import { BrowsePanelSystemFolderComponent } from '../browse-panel-system-folder/browse-panel-system-folder.component'
import { BrowsePanelActionControlsComponent } from '../browse-panel-action-controls/browse-panel-action-controls.component'
import { toSignal } from '@angular/core/rxjs-interop'

@Component({
  selector: 'venio-browse-panel-container',
  standalone: true,
  imports: [
    CommonModule,
    NgComponentOutlet,
    BrowsePanelSystemFolderComponent,
    BrowsePanelAutoFolderComponent,
    BrowsePanelCustomFolderComponent,
    BrowsePanelActionControlsComponent,
  ],
  templateUrl: './browse-panel-container.component.html',
  styleUrls: ['./browse-panel-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BrowsePanelContainerComponent {
  public allFolderTypes = FolderTabType

  public selectedFolderType = toSignal(
    this.commandsFacade.selectFolderTabType$,
    {
      initialValue: FolderTabType.SYSTEM,
    }
  )

  constructor(private commandsFacade: CommandsFacade) {}
}
