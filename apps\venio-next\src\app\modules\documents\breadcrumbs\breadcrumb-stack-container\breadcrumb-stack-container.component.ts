import {
  ChangeDetectionStrategy,
  Component,
  OnD<PERSON>roy,
  OnInit,
  inject,
  signal,
  computed,
} from '@angular/core'
import { CommonModule } from '@angular/common'

import { BreadcrumbStackContentComponent } from '../breadcrumb-stack-content/breadcrumb-stack-content.component'
import { BreadcrumbBottomComponent } from '../breadcrumb-bottom-actions/breadcrumb-bottom.component'
import { BreadcrumbStackGroupOperatorComponent } from '../breadcrumb-stack-group-operator/breadcrumb-stack-group-operator.component'
import { debounceTime, from, of, Subject, switchMap, takeUntil } from 'rxjs'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import { ConditionGroup, ConditionType } from '@venio/shared/models/interfaces'
import { ConditionStackManagerWorkerService } from '@venio/util/utilities'

@Component({
  selector: 'venio-breadcrumb-stack-container',
  standalone: true,
  imports: [
    CommonModule,
    BreadcrumbStackContentComponent,
    BreadcrumbStackGroupOperatorComponent,
    BreadcrumbBottomComponent,
  ],
  templateUrl: './breadcrumb-stack-container.component.html',
  styleUrl: './breadcrumb-stack-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BreadcrumbStackContainerComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  public readonly conditionType = ConditionType

  private breadcrumbFacade = inject(BreadcrumbFacade)

  public breadcrumbStacks = signal<ConditionGroup[]>([])

  public hasAnyBreadcrumbStacks = computed(() =>
    this.breadcrumbStacks()?.some((stack) => Boolean(stack))
  )

  public ngOnInit(): void {
    this.#selectBreadcrumbStack()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public trackBySyntax(item: ConditionGroup): string {
    return item?.conditions?.map((c) => c.conditionSyntax).join('')
  }

  #selectBreadcrumbStack(): void {
    let managerWorkerService: ConditionStackManagerWorkerService
    this.breadcrumbFacade.selectBreadcrumbStack$
      .pipe(
        debounceTime(100),
        switchMap((breadcrumbStack) => {
          // if no breadcrumb stack, return an empty array as no need to process
          if (!breadcrumbStack?.[0]) return of([] as ConditionGroup[])

          managerWorkerService = new ConditionStackManagerWorkerService()
          return from(
            managerWorkerService.getFilteredConditions(breadcrumbStack)
          )
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe((breadcrumbStack) => {
        this.breadcrumbStacks.set(breadcrumbStack)
        managerWorkerService?.terminate()
      })
  }
}
