import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ControlNumberContainerComponent } from './control-number-container.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import {
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
  PLATFORM_ID,
} from '@angular/core'
import {
  AppIdentitiesTypes,
  MESSAGE_SERVICE_CONFIG,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { environment } from '@venio/shared/environments'
import { ControlNumberConfigFacade } from '@venio/data-access/review'

describe('ControlNumberContainerComponent', () => {
  let component: ControlNumberContainerComponent
  let fixture: ComponentFixture<ControlNumberContainerComponent>

  const mockControlNumberConfigFacade: Partial<
    jest.Mocked<ControlNumberConfigFacade>
  > = {
    generateControlNumber: jest.fn(),
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ControlNumberContainerComponent],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
        {
          provide: MESSAGE_SERVICE_CONFIG,
          useValue: {
            origin: environment.allowedOrigin,
            iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
          },
        },
        {
          provide: ControlNumberConfigFacade,
          useValue: mockControlNumberConfigFacade,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ControlNumberContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
