<form [formGroup]="layoutFormGroup">
  <div
    class="t-flex t-flex-col t-gap-3 t-w-full t-overflow-x-hidden t-overflow-y-auto"
    venioDynamicHeight
    [extraSpacing]="70">
    <div class="t-flex t-mb-1 t-w-full t-items-start t-min-h-[58px]">
      <button
        kendoButton
        fillMode="outline"
        (click)="goBack()"
        *ngIf="layoutState.showLayoutListing()"
        class="t-w-[74px] t-p-0 t-text-[16px] hover:t-text-[#000000] t-flex t-items-center t-mr-4">
        <span class="t-flex">
          <kendo-svg-icon
            class="t-text-[var(--v-custom-sky-blue)] hover:t-text-[var(--v-custom-sky-blue)]"
            [icon]="icons.chevronLeftIcon"
            size="large"></kendo-svg-icon>
          <span>Back</span>
        </span>
      </button>
      <span class="t-ml-0 t-mt-[5px] t-font-semibold t-text-[16px]"
        >Create New</span
      >

      <div class="t-flex t-justify-between t-flex-1 t-items-start t-ml-4">
        <div class="t-flex t-flex-col t-items-start">
          <div class="t-flex t-items-start">
            <kendo-textbox
              placeholder="Name"
              formControlName="layoutName"
              class="t-w-44"></kendo-textbox>
            <span class="t-text-error t-ml-1">*</span>
          </div>
          <div
            class="t-text-error t-m-1 t-text-xs"
            *ngIf="
              (!formControls.layoutName.untouched &&
                formControls.layoutName.dirty &&
                formControls.layoutName.invalid) ||
              (!formControls.layoutName.untouched &&
                formControls.layoutName.dirty &&
                formControls.layoutName.value?.trim() === '')
            ">
            {{
              formControls.layoutName.hasError('required')
                ? 'Layout name is required'
                : formControls.layoutName.hasError('layoutNameTaken')
                ? 'Layout name already exist '
                : ''
            }}
          </div>
        </div>

        <div class="t-flex t-gap-2 t-item-start">
          <!-- Button -->
          <!-- <button
            (click)="onMakingPrivateLayout()"
            kendoButton
            fillMode="outline"
            class="!t-text-[var(--v-custom-sky-blue)] t-p-0 t-w-12">
            ME
          </button> -->
          <div class="t-mt-[5px]">
            <kendo-label
              class="t-justify-between t-flex t-font-semibold t-items-center t-text-sm t-text-[#000000]"
              text="Me">
              <kendo-switch
                formControlName="isPrivate"
                offLabel=""
                onLabel=""
                class="t-mx-1"
                size="large"></kendo-switch>
            </kendo-label>
          </div>
          <!-- Dropdowns -->
          <div class="t-flex t-gap-4 t-items-start">
            <!-- Select Client Dropdown -->
            <div class="t-flex t-flex-col t-items-start">
              <div class="t-relative">
                <kendo-multiselecttree
                  #multiSelectClientTree
                  kendoMultiSelectTreeSummaryTag
                  formControlName="client"
                  [popupSettings]="{
                    popupClass: 'custom-multiselecttree-popup'
                  }"
                  [filterable]="true"
                  (filterChange)="onFilterChange('client', $event)"
                  placeholder="Select Client"
                  [kendoMultiSelectTreeFlatBinding]="clients()"
                  idField="clientId"
                  parentIdField="parentId"
                  textField="clientName"
                  valueField="clientId"
                  class="!t-w-56 custom-multiselecttree">
                  <ng-template kendoMultiSelectTreeHeaderTemplate>
                    <label
                      class="t-flex t-items-center t-gap-2 t-pl-2"
                      [ngClass]="{
                        't-text-primary t-font-medium': isAllChecked(
                          'client',
                          multiSelectClientTree
                        )
                      }"
                      [for]="'checkAllClient'"
                      (click)="$event.stopImmediatePropagation()">
                      <input
                        kendoCheckBox
                        id="checkAllClient"
                        type="checkbox"
                        [checked]="
                          isAllChecked('client', multiSelectClientTree)
                        "
                        [indeterminate]="
                          isIndeterminate('client', multiSelectClientTree)
                        "
                        (change)="
                          toggleCheckAll(
                            'client',
                            multiSelectClientTree,
                            $event
                          )
                        " />
                      Check all
                    </label>
                  </ng-template>
                </kendo-multiselecttree>
                <button
                  kendoButton
                  *ngIf="formControls?.client?.value?.length === 0"
                  [disabled]="formControls?.client?.disabled"
                  [svgIcon]="icons.chevronDownIcon"
                  fillMode="link"
                  (click)="toggleDropdown(multiSelectClientTree, $event)"
                  class="t-absolute t-right-2 t-top-1/2 t-transform t--translate-y-1/2 t-z-10 t-select-none t-cursor-pointer t-bg-transparent"></button>

                <label class="t-text-sm t-ml-1 t-align-super">
                  <span class="t-text-error">*</span>
                </label>
              </div>

              <div
                class="t-text-error t-m-1 t-text-xs"
                *ngIf="
                  (!formControls.client.untouched &&
                    formControls.client.dirty &&
                    formControls.client.invalid) ||
                  (!formControls.client.untouched &&
                    formControls.client.dirty &&
                    formControls.client.value?.length === 0)
                ">
                {{
                  formControls.client.hasError('required')
                    ? 'Client is required'
                    : ''
                }}
              </div>
            </div>

            <!-- Select Case Dropdown -->
            <div class="t-flex t-flex-col t-items-start">
              <div class="t-relative">
                <kendo-multiselecttree
                  #multiSelectCaseTree
                  kendoMultiSelectTreeSummaryTag
                  formControlName="case"
                  [popupSettings]="{
                    popupClass: 'custom-multiselecttree-popup'
                  }"
                  [filterable]="true"
                  (filterChange)="onFilterChange('case', $event)"
                  placeholder="Select Case"
                  [kendoMultiSelectTreeFlatBinding]="cases()"
                  idField="projectId"
                  parentIdField="parentId"
                  textField="projectName"
                  valueField="projectId"
                  class="!t-w-56 custom-multiselecttree">
                  <ng-template kendoMultiSelectTreeHeaderTemplate>
                    <label
                      class="t-flex t-items-center t-gap-2 t-pl-2"
                      [ngClass]="{
                        't-text-primary t-font-medium': isAllChecked(
                          'case',
                          multiSelectCaseTree
                        )
                      }"
                      [for]="'checkAllCase'"
                      (click)="$event.stopImmediatePropagation()">
                      <input
                        kendoCheckBox
                        id="checkAllCase"
                        type="checkbox"
                        [checked]="isAllChecked('case', multiSelectCaseTree)"
                        [indeterminate]="
                          isIndeterminate('case', multiSelectCaseTree)
                        "
                        (change)="
                          toggleCheckAll('case', multiSelectCaseTree, $event)
                        " />
                      Check all
                    </label>
                  </ng-template>
                </kendo-multiselecttree>
                <button
                  kendoButton
                  *ngIf="formControls?.case?.value?.length === 0"
                  [disabled]="formControls?.case?.disabled"
                  [svgIcon]="icons.chevronDownIcon"
                  fillMode="link"
                  (click)="toggleDropdown(multiSelectCaseTree, $event)"
                  class="t-absolute t-right-2 t-top-1/2 t-transform t--translate-y-1/2 t-z-10 t-select-none t-cursor-pointer t-bg-transparent"></button>
                <label class="t-text-sm t-ml-1 t-align-super">
                  <span class="t-text-error">*</span>
                </label>
              </div>
              <div
                class="t-text-error t-m-1 t-text-xs"
                *ngIf="
                  (!formControls.case.untouched &&
                    formControls.case.dirty &&
                    formControls.case.invalid) ||
                  (!formControls.case.untouched &&
                    formControls.case.dirty &&
                    formControls.case.value?.length === 0)
                ">
                {{
                  formControls.case.hasError('required')
                    ? 'Case is required'
                    : ''
                }}
              </div>
            </div>

            <!-- Select Group Dropdown -->
            <div class="t-flex t-flex-col t-items-start">
              <div class="t-relative">
                <kendo-multiselecttree
                  #multiSelectUserGroupTree
                  kendoMultiSelectTreeSummaryTag
                  placeholder="Select Group"
                  formControlName="userGroup"
                  [popupSettings]="{
                    popupClass: 'custom-multiselecttree-popup'
                  }"
                  [filterable]="true"
                  (filterChange)="onFilterChange('userGroup', $event)"
                  [kendoMultiSelectTreeFlatBinding]="userGroups()"
                  idField="groupName"
                  parentIdField="parentId"
                  textField="groupName"
                  valueField="groupName"
                  class="!t-w-56 custom-multiselecttree">
                  <ng-template kendoMultiSelectTreeHeaderTemplate>
                    <label
                      class="t-flex t-items-center t-gap-2 t-pl-2"
                      [ngClass]="{
                        't-text-primary t-font-medium': isAllChecked(
                          'userGroup',
                          multiSelectUserGroupTree
                        )
                      }"
                      [for]="'checkAllUserGroup'"
                      (click)="$event.stopImmediatePropagation()">
                      <input
                        kendoCheckBox
                        id="checkAllUserGroup"
                        type="checkbox"
                        [checked]="
                          isAllChecked('userGroup', multiSelectUserGroupTree)
                        "
                        [indeterminate]="
                          isIndeterminate('userGroup', multiSelectUserGroupTree)
                        "
                        (change)="
                          toggleCheckAll(
                            'userGroup',
                            multiSelectUserGroupTree,
                            $event
                          )
                        " />
                      Check all
                    </label>
                  </ng-template>
                </kendo-multiselecttree>
                <button
                  kendoButton
                  *ngIf="formControls?.userGroup?.value?.length === 0"
                  [disabled]="formControls?.userGroup?.disabled"
                  [svgIcon]="icons.chevronDownIcon"
                  fillMode="link"
                  (click)="toggleDropdown(multiSelectUserGroupTree, $event)"
                  class="t-absolute t-right-2 t-top-1/2 t-transform t--translate-y-1/2 t-z-10 t-select-none t-cursor-pointer t-bg-transparent"></button>
                <label class="t-text-sm t-ml-1 t-align-super">
                  <span class="t-text-error">*</span>
                </label>
              </div>
              <div
                class="t-text-error t-m-1 t-text-xs"
                *ngIf="
                  (!formControls.userGroup.untouched &&
                    formControls.userGroup.dirty &&
                    formControls.userGroup.invalid) ||
                  (!formControls.userGroup.untouched &&
                    formControls.userGroup.dirty &&
                    formControls.userGroup.value?.length === 0)
                ">
                {{
                  formControls.userGroup.hasError('required')
                    ? 'User group is required'
                    : ''
                }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      [ngClass]="{ 'k-disabled t-opacity-60': !enablePanelControls() }"
      class="t-flex t-mt-[5px] t-border-[#e7e7e7] t-gap-2 t-pt-3 t-border-t-[1px]">
      <div class="t-flex t-flex-col t-w-1/3 t-gap-1">
        <h3 class="t-font-semibold t-w-full t-text-base t-mb-3 t-w-full">
          Viewable Panels
        </h3>
        <div
          class="t-text-error t-m-1 t-text-xs"
          *ngIf="formControls.isPanelSelected.hasError('required')">
          At least one panel is required
        </div>
        <kendo-grid
          [kendoGridBinding]="viewablePanels()"
          venioDynamicHeight
          [resizable]="true"
          [extraSpacing]="70"
          [selectable]="{ checkboxOnly: true, mode: 'multiple' }"
          (selectionChange)="onSelectionChange($event)"
          kendoGridSelectBy="panelId"
          [(selectedKeys)]="selectedPanelIds">
          <kendo-grid-column
            title="#"
            [width]="53"
            [minResizableWidth]="53"
            headerClass="t-text-primary">
            <ng-template kendoGridCellTemplate let-rowIndex="rowIndex">
              {{ rowIndex + 1 }}
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-checkbox-column
            [showSelectAll]="true"
            [minResizableWidth]="40"
            [width]="40"></kendo-grid-checkbox-column>
          <kendo-grid-column
            headerClass="t-text-primary"
            field="panelName"
            [width]="220"
            title="Right Panel">
            <ng-template kendoGridCellTemplate let-dataItem>
              {{ dataItem.panelName }}
            </ng-template>
          </kendo-grid-column>

          <kendo-grid-column
            field="fields"
            title="Fields"
            [width]="100"
            headerClass="t-text-primary">
            <ng-template kendoGridCellTemplate let-dataItem>
              <button
                *ngIf="dataItem.hasField"
                kendoButton
                (click)="openFieldSelectionDialog(dataItem)"
                fillMode="clear"
                size="none"
                [disabled]="!dataItem.isSelected"
                class="t-p-1 t-bg-[#F4F4F4] t-cursor-pointer t-pl-3.5 t-rounded !t-h-[24px] t-leading-none t-overflow-hidden t-group">
                <span
                  class="t-inline-block t-absolute t-left-0 t-w-3 t-top-0 t-bg-[#6C6C6C] t-h-full"></span>
                <kendo-svgicon
                  class="t-text-[#6C6C6C] group-hover:t-text-[#1EBADC]"
                  [icon]="icons.eyeIcon"></kendo-svgicon>
              </button>
            </ng-template>
          </kendo-grid-column>
        </kendo-grid>
      </div>
      <div class="t-flex">
        <div
          class="t-h-full t-bg-green-200 t-w-10 t-h-full custom-svg-line t-bg-no-repeat t-bg-center"></div>
      </div>

      <div class="t-flex t-w-1/3 t-flex-col">
        <h3 class="t-font-semibold t-w-full t-text-base t-mb-3 t-w-full">
          Non-viewable Panels
        </h3>
        <kendo-grid
          [kendoGridBinding]="nonViewablePanels()"
          venioDynamicHeight
          [resizable]="true"
          [extraSpacing]="70"
          kendoGridSelectBy="panelId">
          <kendo-grid-column
            title="#"
            [width]="53"
            [minResizableWidth]="53"
            headerClass="t-text-primary">
            <ng-template kendoGridCellTemplate let-rowIndex="rowIndex">
              {{ rowIndex + 1 }}
            </ng-template>
          </kendo-grid-column>

          <kendo-grid-column
            field="panelName"
            title="Right Panel"
            [width]="200"
            headerClass="t-text-primary">
            <ng-template kendoGridCellTemplate let-dataItem>
              {{ dataItem.panelName | pascalToSpace }}
            </ng-template>
          </kendo-grid-column>
        </kendo-grid>
      </div>
      <div class="t-flex">
        <div
          class="t-h-full t-bg-green-200 t-w-10 t-h-full custom-svg-line t-bg-no-repeat t-bg-center"></div>
      </div>
      <div class="t-flex t-w-1/3 t-flex-col">
        <h3 class="t-font-semibold t-w-full t-text-base t-mb-3 t-w-full">
          Select Viewer Tabs
        </h3>
        <div
          class="t-text-error t-m-1 t-text-xs"
          *ngIf="formControls.isViewerSelected.hasError('required')">
          At least one viewer panel is required
        </div>
        <kendo-listview
          [data]="viewerPanels()"
          class="t-w-full !t-border-l-[0px] !t-border-r-[0px] !t-border-b-[0px] t-pt-2 v-custom-viewer-tabs"
          venioDynamicHeight
          [extraSpacing]="70">
          <ng-template kendoListViewItemTemplate let-dataItem>
            <div class="t-flex t-items-center t-py-1">
              <!-- Checkbox -->
              <div class="t-w-6 t-flex t-items-center">
                <input
                  type="checkbox"
                  class="k-checkbox"
                  (change)="oncheckChanged(dataItem, $event)"
                  [checked]="dataItem.isSelected" />
              </div>

              <!-- Name -->
              <div class="t-w-40 t-text-sm">
                <span
                  class="t-font-medium"
                  [class.t-text-primary]="dataItem.isSelected">
                  {{ dataItem.panelName | pascalToSpace }}
                </span>
              </div>

              <!-- Action -->
              <div class="t-w-10 t-flex t-justify-end">
                <button
                  *ngIf="dataItem.hasField"
                  (click)="openFieldSelectionDialog(dataItem)"
                  kendoButton
                  fillMode="clear"
                  size="none"
                  [disabled]="!dataItem.isSelected"
                  class="t-p-1 t-bg-[#F4F4F4] t-cursor-pointer t-pl-3.5 t-rounded !t-h-[24px] t-leading-none t-overflow-hidden t-group">
                  <span
                    class="t-inline-block t-absolute t-left-0 t-w-3 t-top-0 t-bg-[#6C6C6C] t-h-full"></span>
                  <kendo-svgicon
                    class="t-text-[#6C6C6C] group-hover:t-text-[#1EBADC]"
                    [icon]="icons.eyeIcon"></kendo-svgicon>
                </button>
              </div>
            </div>
          </ng-template>
        </kendo-listview>
      </div>
    </div>
  </div>

  <div kendoDialogContainer></div>

  <div class="t-flex t-w-full t-gap-4 t-justify-end t-pt-4">
    <button
      kendoButton
      class="v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      (click)="saveLayout()"
      data-qa="save">
      SAVE
    </button>
    <button
      data-qa="cancel"
      kendoButton
      themeColor="dark"
      fillMode="outline"
      (click)="closeWindow()">
      CANCEL
    </button>
  </div>
</form>
