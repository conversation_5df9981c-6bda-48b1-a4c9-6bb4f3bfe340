<kendo-dialog-titlebar (close)="onClose()" class="v-custom-titlebar-closebtn">
  <div class="t-flex t-flex-row t-gap-2">
    <div
      class="t-bg-[#0000000C] t-w-[40px] t-h-[40px] t-flex t-items-center t-justify-center t-rounded-full">
      <img
        src="assets/svg/convert.svg"
        alt="Control Number"
        style="width: 20px; height: 20px" />
    </div>
    <div
      class="t-flex t-text-[#2F3080DE] t-text-[16px] t-font-medium t-relative t-top-[10px] t-ml-2">
      Control Number
    </div>
  </div>
</kendo-dialog-titlebar>
<kendo-tabstrip #tabstrip (tabSelect)="tabSelected($event)" class="t-px-6">
  <kendo-tabstrip-tab
    title="Control Number"
    *ngIf="isControlNumberConfigEnabled"
    #controlNumberTab>
    <ng-template kendoTabContent>
      @defer{
      <venio-control-number-config
        [projectId]="projectId"
        #controlNumberConfig />
      } @placeholder {
      <div class="t-flex t-grow t-mt-4 t-gap-3">
        @for(n of [1,2,3,4,5]; track n){
        <kendo-skeleton class="t-w-1/5" height="30px" shape="rectangle" />
        }
      </div>
      }
    </ng-template>
  </kendo-tabstrip-tab>

  <kendo-tabstrip-tab title="Status" #controlNumberStatusTab>
    <ng-template kendoTabContent>
      @defer{
      <venio-control-number-status [projectId]="projectId" />
      } @placeholder {
      <div class="t-flex t-grow t-mt-4 t-gap-3">
        @for(n of [1,2,3,4,5]; track n){
        <kendo-skeleton class="t-w-1/5" height="30px" shape="rectangle" />
        }
      </div>
      }
    </ng-template>
  </kendo-tabstrip-tab>
</kendo-tabstrip>

<kendo-dialog-actions
  class="v-dialog-actions"
  *ngIf="currentTabName === 'Control Number'">
  <div class="t-flex t-flex-row t-justify-end t-w-auto">
    <div class="flex justify-end gap-4">
      <button
        kendoButton
        (click)="generate()"
        class="t-m-1 v-custom-secondary-button"
        fillMode="outline"
        themeColor="secondary">
        GENERATE
        <kendo-loader
          *ngIf="showSpinner()"
          size="small"
          type="pulsing"
          class="-t-ml-[0.5rem] -t-mr-[1rem] t-pl-[0.5rem]"></kendo-loader>
      </button>
    </div>
  </div>
</kendo-dialog-actions>
