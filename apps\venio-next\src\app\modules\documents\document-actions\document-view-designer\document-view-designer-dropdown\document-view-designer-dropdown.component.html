<ng-template #designerContainerTpl></ng-template>
<button
  kendoButton
  #dropdownAnchor
  (click)="toggleDropdown()"
  themeColor="none"
  fillMode="outline"
  class="t-w-[18.75rem]">
  <div
    class="t-flex t-justify-between t-capitalize t-items-center"
    [ngStyle]="{ 'width.px': dropdownAnchor.element.clientWidth - 16 }">
    <span>{{ selectedDocumentViewDesignerDropdownItem()?.viewName }}</span>
    <kendo-svg-icon [icon]="downIcon"></kendo-svg-icon>
  </div>
</button>
@if (dropdownContentVisibility) {
<kendo-popup
  #itemPopup
  [popupClass]="'!t-rounded-none v-document-menu-popup-container !t-shadow-md'"
  [anchor]="dropdownAnchor.element">
  <div
    class="t-flex t-flex-col t-whitespace-nowrap t-max-h-[calc(100vh_-_10rem)] t-overflow-auto"
    [ngStyle]="{ 'width.px': dropdownAnchor.element.clientWidth - 20 }">
    <div
      *ngIf="!isExternalUser()"
      class="t-flex t-justify-center t-items-center">
      <button
        kendoButton
        fillMode="clear"
        themeColor="none"
        size="none"
        class="t-text-[#4476FF] t-text-[0.875rem] t-flex t-items-center t-mb-3"
        (click)="listItemClickAction(undefined, commonActionTypes.ADD, $event)">
        <kendo-svg-icon [icon]="iconPlus"></kendo-svg-icon>
        <span class="t-capitalize"> Add New View</span>
      </button>
    </div>
    <kendo-listview
      class="t-border-0 v-document-view-designer-list t-max-h-96"
      itemClass="v-document-menu-popup-item t-relative t-cursor-pointer t-px-2"
      [navigable]="true"
      [data]="loadedItems()"
      (scrollBottom)="loadMoreItems()"
      containerClass="k-d-flex k-flex-col k-flex-nowrap t-mt-2">
      <ng-template kendoListViewHeaderTemplate>
        <div class="t-w-full t-relative t-flex flex-grow justify-between">
          <span class="t-w-full">
            {{ selectedDocumentViewDesignerDropdownItem()?.viewName }}
          </span>

          <ng-container
            *ngIf="!selectedDocumentViewDesignerDropdownItem()?.isDefault"
            [ngTemplateOutlet]="pencilIconTpl"
            [ngTemplateOutletContext]="{
              $implicit: false,
              dataItem: selectedDocumentViewDesignerDropdownItem()
            }" />
        </div>
        <kendo-textbox
          #searchInput
          class="t-w-full t-mt-2 t-mb-2"
          size="small"
          (valueChange)="updateViewBasedOnSearch()"
          placeholder="Search..."
          [clearButton]="true"></kendo-textbox>
      </ng-template>
      <ng-template kendoListViewItemTemplate let-dataItem="dataItem">
        <div
          (click)="
            listItemClickAction(dataItem, commonActionTypes.SELECT, $event)
          "
          class="t-flex t-justify-between t-items-center t-py-1 v-document-view-item"
          (mouseleave)="toggleIconContainerOpaque(actionItem, false)"
          (mouseenter)="toggleIconContainerOpaque(actionItem, true)">
          <span> {{ dataItem.viewName }}</span>
          <span
            (click)="$event.stopPropagation()"
            class="v-document-view-designer-list-action-container t-min-w-4"
            #actionItem>
            <ng-container
              *ngIf="!dataItem?.isDefault"
              [ngTemplateOutlet]="pencilIconTpl"
              [ngTemplateOutletContext]="{
                $implicit: actionItem.style.opacity === '1',
                dataItem
              }" />
          </span>
        </div>
      </ng-template>
    </kendo-listview>
  </div>
</kendo-popup>
}

<ng-template #pencilIconTpl let-isWhite let-dataItem="dataItem">
  <kendo-svg-icon
    [title]="'Edit ' + dataItem?.viewName"
    (click)="listItemClickAction(dataItem, commonActionTypes.EDIT, $event)"
    [id]="isWhite"
    [ngClass]="isWhite ? 't-text-[#FFFFFF]' : 't-text-[#4476FF]'"
    class="t-cursor-pointer"
    [icon]="iconPencil"></kendo-svg-icon>
</ng-template>
