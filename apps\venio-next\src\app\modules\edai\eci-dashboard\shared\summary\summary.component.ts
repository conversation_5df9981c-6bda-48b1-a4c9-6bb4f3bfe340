import { Component, inject } from '@angular/core'
import { DecimalPipe } from '@angular/common'
import { KENDO_BUTTONS } from '@progress/kendo-angular-buttons'
import { toSignal } from '@angular/core/rxjs-interop'
import { map, combineLatest } from 'rxjs'
import { AiFacade } from '@venio/data-access/ai'
import { DocumentsFacade, SearchFacade } from '@venio/data-access/review'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-summary',
  standalone: true,
  imports: [KENDO_BUTTONS, DecimalPipe, SvgLoaderDirective],
  templateUrl: './summary.component.html',
  styleUrl: './summary.component.scss',
})
export class SummaryComponent {
  private readonly aiFacade = inject(AiFacade)

  private readonly documentsFacade = inject(DocumentsFacade)

  private readonly searchFacade = inject(SearchFacade)

  // Real selected documents count derived from document facade state
  public readonly selectedDocuments = toSignal(
    combineLatest([
      this.documentsFacade.getIsBatchSelected$,
      this.documentsFacade.getSelectedDocuments$,
      this.documentsFacade.getUnselectedDocuments$,
      this.searchFacade.getTotalHitCount$,
    ]).pipe(
      map(([isBatchSelected, selectedDocs, unselectedDocs, totalHitCount]) => {
        if (isBatchSelected) {
          return (totalHitCount || 0) - (unselectedDocs?.length || 0)
        }
        return selectedDocs?.length || 0
      })
    ),
    { initialValue: 0 }
  )

  // Real total documents count from search facade
  public readonly totalDocuments = toSignal(
    this.searchFacade.getTotalHitCount$,
    { initialValue: 0 }
  )

  // ECA API data for calculating totals
  public readonly ecaRelevanceData = toSignal(
    this.aiFacade.selectEcaRelevanceSuccess$,
    { initialValue: null }
  )

  // Computed total from ECA relevance data
  public readonly computedTotalDocuments = toSignal(
    this.aiFacade.selectEcaRelevanceSuccess$.pipe(
      map((ecaData) => {
        try {
          if (ecaData?.data && Array.isArray(ecaData.data)) {
            const total = ecaData.data.reduce((total: number, item: any) => {
              const count = item?.docCount || 0
              return total + count
            }, 0)
            return total > 0 ? total : this.totalDocuments() || 0
          }
          return this.totalDocuments() || 0
        } catch (error) {
          console.error('Error computing total documents:', error)
          return this.totalDocuments() || 0
        }
      })
    ),
    { initialValue: 0 }
  )

  public onCaseButtonClick(): void {
    console.log('Case Info button clicked!')
  }

  public onNarrativeButtonClick(): void {
    console.log('Button clicked!')
  }
}
