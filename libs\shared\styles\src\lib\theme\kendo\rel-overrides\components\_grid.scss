/**  Overrides the default filter menu icon of grid  */
// Define common variables
$svg-size: 1.25rem;
$svg-bg-size: $svg-size - 0.125rem;
$funnel-normal: url('~apps/venio-next/src/assets/svg/Funnel-normal.svg');
$funnel-normal-hover: url('~apps/venio-next/src/assets/svg/Funnel-normal-hover.svg');

// Base styles for k-icon-button and SVG icons
//.k-filtercell-wrapper .k-icon-button {
// padding-bottom: 0;
//}

.k-grid-filter-menu.k-grid-header-menu {
  background-color: white !important;
  box-shadow: none !important;
  align-items: flex-end !important;
}

// Mixin for SVG icons
@mixin svg-icon($bg-image) {
  position: absolute;
  width: $svg-size;
  height: $svg-size;
  background-size: $svg-bg-size;
  background-repeat: no-repeat;
  content: ' ';
  background-image: $bg-image;
}

// Common styles for .k-grid-filter-menu and .k-i-filter
@mixin common-icon-styles {
  svg {
    display: none;
  }

  &:hover {
    background-color: transparent;
  }

  &:active,
  &:focus {
    outline: none;
    border: 0;
    box-shadow: none;
  }
}

.k-grid-header {
  .k-grid-header-menu.k-active {
    background-color: white;
    box-shadow: none;

    .k-svg-i-filter {
      position: relative;

      &::after,
      &:hover::before {
        @include svg-icon($funnel-normal-hover);
      }

      &:hover::before {
        background-image: $funnel-normal-hover;
        z-index: 1;
      }
    }

    &[aria-expanded='true'] {
      .k-svg-i-filter {
        position: relative;
        &::after,
        &:hover::before {
          background-image: $funnel-normal-hover !important;
          z-index: 1;
        }
      }
    }
  }
}

.k-grid-filter-menu {
  @include common-icon-styles;

  .k-svg-i-filter {
    position: relative;

    &::after,
    &:hover::before {
      @include svg-icon($funnel-normal);
    }

    &:hover::before {
      background-image: $funnel-normal-hover;
      z-index: 1;
    }
  }

  &[aria-expanded='true'] {
    .k-svg-i-filter {
      position: relative;
      &::after,
      &:hover::before {
        background-image: $funnel-normal-hover !important;
        z-index: 1;
      }
    }
    &::after {
      content: ' ';
      width: 0;
      height: 0;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-bottom: 7px solid #979797;
      bottom: 3px;
      position: absolute;
      margin-left: -13px;
    }
  }
}

.k-i-filter {
  position: relative;
  @include common-icon-styles;

  &::after,
  &::before {
    @include svg-icon($funnel-normal);
  }

  &:hover::before {
    background-image: $funnel-normal-hover;
    z-index: 1;
  }
}

.k-grid td.k-selected,
.k-grid .k-table-row.k-selected > td,
.k-grid .k-table-td.k-selected,
.k-grid .k-table-row.k-selected > .k-table-td {
  color: white;
}

.k-grid .k-table-row.k-selected {
  &:hover {
    > td {
      color: white !important;
    }
  }
}

$grid-root: ':is(kendo-grid, kendo-treelist, .k-grid)';

@layer {
  #{$grid-root}:not(.v-ignore-global-overrides) {
    /* ─────────────── header cells ─────────────── */
    .k-table-th,
    thead th {
      @apply t-py-2.5 #{!important};
    }

    /* ─────────────── body cells ─────────────── */
    .k-table-td, /* v6+ */
    tbody td {
      @apply t-py-1 #{!important};
    }
  }
  tbody {
    .k-table-row {
      color: $grid-text-color #{!important};
      &.k-selected {
        td {
          background-color: $grid-selected-hover #{!important};
          color: $grid-text-color #{!important};
        }
      }
    }
  }

  .v-tag-history-grid {
    .k-table-td {
      @apply t-whitespace-pre-wrap t-align-top #{!important};
    }
  }
  .v-customgrid-newview {
    @apply t-border-0 #{!important};
    .k-grid-header {
      @apply t-hidden #{!important};
    }
    .k-drag-cell {
      .k-svg-i-reorder {
        svg {
          @apply t-hidden #{!important};
        }
        &::after {
          content: ' ';
          width: 20px;
          height: 20px;
          background: url('~apps/venio-next/src/assets/svg/icon-drag-and-drop-universal.svg')
            no-repeat center/20px 20px;
        }
      }
    }
  }
  .v-customgrid-metafield {
    .k-drag-cell {
      .k-svg-i-reorder {
        svg {
          @apply t-hidden #{!important};
        }
        &::after {
          content: ' ';
          width: 20px;
          height: 20px;
          background: url('~apps/venio-next/src/assets/svg/icon-drag-and-drop-universal.svg')
            no-repeat center/20px 20px;
        }
      }
    }
  }

  .v-custom-grid-print {
    .k-table-td {
      @apply t-whitespace-normal t-break-words #{!important};
    }
    .k-grid-header,
    .k-table-td,
    .k-grid td {
      @apply t-border-0 #{!important};
    }
  }

  .v-custom-grid-upload {
    .k-grid-header {
      @apply t-p-0 #{!important};
    }
  }

  // set the  scrollbar of the grid to auto
  .k-grid-content {
    @apply t-overflow-auto #{!important};
    &::-webkit-scrollbar {
      @apply t-h-[8px] #{!important};
    }
  }
  // remove the extra padding of the grid header
  .v-redaction-table {
    .k-grid-header {
      // this is to remove the padding of the grid header which is added by default by kendo
      @apply t-p-0 #{!important};
    }
  }

  .v-custom-grid-table {
    .k-grid-header {
      // this is to remove the padding of the grid header which is added by default by kendo
      @apply t-p-0 #{!important};
    }
  }

  kendo-treelist {
    &.v-custom-grid-table {
      .k-grid-header {
        // this is to remove the padding of the grid header which is added by default by kendo
        @apply t-p-0 #{!important};
      }
    }

    // don't apply the alternate background color to the treelist if it has the class v-custom-treelist-no-alt-bg
    &:not(.v-custom-treelist-no-alt-bg) {
      .k-grid-content {
        .k-table {
          tr:nth-child(even) {
            td {
              @apply t-bg-[#F7F7F7];
            }
          }
        }
      }
    }
  }

  kendo-grid {
    .k-grid-pager {
      @apply t-bg-white #{!important};
    }
    &.v-custom-no-border-bg-grid {
      .k-table {
        tr {
          td {
            @apply t-bg-[transparent];
          }
        }
      }
    }
    .k-grid-content {
      .k-table {
        tr:nth-child(even) {
          td {
            @apply t-bg-[#F7F7F7];
          }
        }
      }
    }
    &.v-custom-reprocessing-update-grid {
      kendo-grid-filter-menu {
        @apply t-absolute t-left-[105px] t-grid t-place-content-center  t-z-10 t-h-[32px] t-w-[32px] #{!important};
        .k-grid-header-menu {
          &:hover {
            background: transparent !important;
          }
        }
      }
    }

    &.v-custom-repro-setting {
      .k-grid-header {
        @apply t-bg-[#F9F9F9] #{!important};
        .k-table-thead {
          @apply t-bg-[#F9F9F9] #{!important};
        }
      }
    }
    &.v-custom-panel-filter {
      .k-grid-header {
        @apply t-border-0 t-p-0 #{!important};
      }
      .k-table {
        tr:nth-child(even) {
          td {
            @apply t-bg-[#FFFFFF];
          }
        }
      }
      .k-master-row {
        &.k-selected {
          td {
            @apply t-text-primary t-font-medium t-bg-[#FFFFFF] #{!important};
          }
        }
      }
    }
    // column resize hover effects for better UX
    .k-column-resizer:hover {
      background: linear-gradient(
        to right,
        #ffffff 0%,
        #f5f5f5 48%,
        #ffffff 100%
      );
    }

    .k-sort-icon {
      @apply t-flex t-place-content-center;
      .k-svg-i-sort-asc-small {
        @apply t-relative t-w-[5px] #{!important};
        &::after {
          content: ' ';
          position: absolute;
          width: 8px !important;
          height: 13px !important;
          background: url('~apps/venio-next/src/assets/svg/Icon-sort-asc.svg')
            no-repeat center/8px 13px;
        }
        svg {
          @apply t-invisible #{!important};
        }
      }

      .k-svg-i-sort-desc-small {
        @apply t-relative t-w-[5px] #{!important};
        &::after {
          content: ' ';
          position: absolute;
          width: 8px !important;
          height: 13px !important;
          background: url('~apps/venio-next/src/assets/svg/Icon-sort-desc.svg')
            no-repeat center/8px 13px;
        }
        svg {
          @apply t-invisible #{!important};
        }
      }
    }

    .k-grid-filter-menu.k-grid-header-menu {
      &:focus {
        @apply t-shadow-none #{!important};
      }
    }

    &.v-case-launchpad-grid {
      .k-grid-content {
        @apply t-flex #{!important};
      }
    }

    &.v-discovery-status-grid {
      td {
        @apply t-border-b-[0px] #{!important};
      }
      .k-table-td {
        @apply t-overflow-visible #{!important};
      }
    }
  }
}

.k-grid .k-grid-header .k-table-th {
  font-size: 1rem;
  letter-spacing: 0.046rem;

  .k-checkbox:indeterminate,
  .k-checkbox.k-indeterminate {
    &::before {
      content: '';
      height: 0.59rem;
      width: 0.59rem;
      background: var(--kendo-primary-100);
      z-index: 99;
      display: flex;
      position: relative;
      justify-content: center;
      align-items: center;
      margin: 1.5px;
      border-radius: 2px;
    }
  }
}
