import { createAction, props } from '@ngrx/store'
import {
  GenerateControlNumberRequestModel,
  ControlNumberConfigStatus,
  CustodianMediaControlNumberConfig,
} from '../../models/interfaces'
import { ResponseModel } from '@venio/shared/models/interfaces'

export const fetchCustodianMedia$ = createAction(
  '[ControlNumberConfig] Fetch Custodian Media',
  props<{ projectId: number }>()
)

export const fetchCustodianMediaSuccess$ = createAction(
  '[ControlNumberConfig] Fetch Custodian Media Success',
  props<{ medias: CustodianMediaControlNumberConfig[] }>()
)

export const fetchCustodianMediaFailure$ = createAction(
  '[ControlNumberConfig] Fetch Custodian Media Failure',
  props<{ error: any }>()
)

export const fetchCustodianMediaStatus$ = createAction(
  '[ControlNumberConfig] Fetch Custodian Media Status',
  props<{ projectId: number }>()
)

export const fetchCustodianMediaStatusSuccess$ = createAction(
  '[ControlNumberConfig] Fetch Custodian Media Status Success',
  props<{ status: ControlNumberConfigStatus[] }>()
)

export const fetchCustodianMediaStatusFailure$ = createAction(
  '[ControlNumberConfig] Fetch Custodian Media Status Failure',
  props<{ error: any }>()
)

export const generateControlNumber = createAction(
  '[ControlNumberConfig] Generate Control Number',
  props<{
    projectId: number
    payload: GenerateControlNumberRequestModel
  }>()
)

export const generateControlNumberSuccess = createAction(
  '[ControlNumberConfig] Generate Control Number Success',
  props<{ response: ResponseModel }>()
)

export const generateControlNumberFailure = createAction(
  '[ControlNumberConfig] Generate Control Number Failure',
  props<{ error: any }>()
)

export const clearControlNumberConfigError = createAction(
  '[ControlNumberConfig] Clear Control Number Config Error'
)

export const clearControlNumberStatusError = createAction(
  '[ControlNumberConfig] Clear Control Number Status Error'
)

export const resetControlNumberConfigState = createAction(
  '[ControlNumberConfig] Reset Control Number Config'
)

export const resetControlNumberStatusState = createAction(
  '[ControlNumberConfig] Reset Control Number Status'
)
