import { Component, inject } from '@angular/core'
import { GridModule } from '@progress/kendo-angular-grid'
import { UiPaginationModule, PageArgs } from '@venio/ui/pagination'
import { toSignal } from '@angular/core/rxjs-interop'
import { AiFacade } from '@venio/data-access/ai'

@Component({
  selector: 'venio-list-of-files',
  standalone: true,
  imports: [GridModule, UiPaginationModule],
  templateUrl: './list-of-files.component.html',
  styleUrls: ['./list-of-files.component.scss'],
})
export class ListOfFilesComponent {
  private readonly aiFacade = inject(AiFacade)

  public readonly files = toSignal(this.aiFacade.selectEciFileData$, {
    initialValue: [],
  })

  public readonly pagedFiles = toSignal(this.aiFacade.selectEciPagedFileData$, {
    initialValue: [],
  })

  public readonly paginationState = toSignal(
    this.aiFacade.selectEciPaginationState$,
    { initialValue: { currentPage: 1, pageSize: 15, totalRecords: 0 } }
  )

  public pageChanged(event: PageArgs): void {
    this.aiFacade.updateEciPagination({ currentPage: event.pageNumber })
  }

  public pageSizeChanged(event: PageArgs): void {
    this.aiFacade.updateEciPagination({
      currentPage: event.pageNumber,
      pageSize: event.pageSize,
    })
  }
}
