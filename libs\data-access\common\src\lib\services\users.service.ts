import { Injectable } from '@angular/core'
import { HttpClient, HttpParams } from '@angular/common/http'
import { environment } from '@venio/shared/environments'
import { Observable } from 'rxjs'
import {
  InviteUserModel,
  ResponseModel,
  UsersListModel,
} from '@venio/shared/models/interfaces'

@Injectable({ providedIn: 'root' })
export class UserService {
  constructor(private http: HttpClient) {}

  private get _apiUrl(): string {
    return environment.apiUrl
  }

  public fetchUserList(): Observable<ResponseModel> {
    return this.http.get<ResponseModel>(`${this._apiUrl}User`)
  }

  public fetchCurrentUser(): Observable<ResponseModel> {
    return this.http.get<ResponseModel>(`${this._apiUrl}User/details`)
  }

  public fetchUserListToInvite(
    projectId: number
  ): Observable<UsersListModel[]> {
    return this.http.get<UsersListModel[]>(`${this._apiUrl}UploadInvitation`, {
      params: new HttpParams().set('projectId', projectId),
    })
  }

  public fetchExternalUserListToInvite(): Observable<UsersListModel[]> {
    return this.http.get<UsersListModel[]>(`${this._apiUrl}UploadInvitation`)
  }

  public sendInvitation(payload: InviteUserModel): Observable<boolean> {
    return this.http.post<boolean>(
      this._apiUrl + '/UploadInvitation',
      payload,
      {}
    )
  }
}
