import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentsContainerComponent } from './documents-container.component'
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'

import { provideMockStore } from '@ngrx/store/testing'
import {
  DocumentsFacade,
  FieldFacade,
  IndexedDBHandlerService,
  SearchFacade,
  SearchResultFacade,
} from '@venio/data-access/review'
import {
  DialogContainerService,
  DialogService,
} from '@progress/kendo-angular-dialog'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import {
  IframeMessengerFacade,
  WINDOW,
} from '@venio/data-access/iframe-messenger'
import { of } from 'rxjs'
import { ActivatedRoute, ActivatedRouteSnapshot } from '@angular/router'
import { StoreModule } from '@ngrx/store'
import { EffectsModule } from '@ngrx/effects'

describe('DocumentsContainerComponent', () => {
  let component: DocumentsContainerComponent
  let fixture: ComponentFixture<DocumentsContainerComponent>
  let indexedDBHandlerService: IndexedDBHandlerService

  beforeEach(() => {
    ;(global as any).DOMRect = jest
      .fn()
      .mockImplementation((x, y, width, height) => ({
        x,
        y,
        width,
        height,
      }))
    global.ResizeObserver = jest
      .fn()
      .mockImplementation((callback: () => void) => ({
        observe: (): void => callback(),
        unobserve: (): void => {
          /* placeholder */
        },
        disconnect: (): void => {
          /* placeholder */
        },
      }))
  })

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      teardown: { destroyAfterEach: true },
      imports: [
        DocumentsContainerComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DialogService,
        DialogContainerService,
        SearchFacade,
        FieldFacade,
        provideMockStore({}),
        IndexedDBHandlerService,
        {
          provide: IframeMessengerFacade,
          useValue: {
            selectIframeMessengerContent$: jest.fn().mockReturnValue(of({})),
          },
        },
        {
          provide: SearchResultFacade,
          useValue: {
            resetSearchResults: jest.fn(),
          },
        },
        {
          provide: WINDOW,
          useValue: {
            addEventListener: jest.fn(),
            removeEventListener: jest.fn(),
          },
        },
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({ projectId: '1' }),
            snapshot: {
              queryParams: {
                projectId: 1,
              },
            } as unknown as ActivatedRouteSnapshot,
          } satisfies Partial<ActivatedRoute>,
        },
        {
          provide: DocumentsFacade,
          useValue: {} satisfies Partial<DocumentsFacade>,
        },
      ],
    }).compileComponents()
    indexedDBHandlerService = TestBed.inject(IndexedDBHandlerService)
    jest.spyOn(indexedDBHandlerService, 'getParts').mockResolvedValue([])
    jest.spyOn(indexedDBHandlerService, 'deleteOldRecords').mockResolvedValue()
    fixture = TestBed.createComponent(DocumentsContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
