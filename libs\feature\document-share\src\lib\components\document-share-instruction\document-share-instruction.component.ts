import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  signal,
  SimpleChanges,
  ViewChild,
} from '@angular/core'
import { DocumentShareFormService } from '../../services/document-share-form.service'
import { FormGroup } from '@angular/forms'
import { DocumentShareFacade } from '@venio/data-access/review'
import { Subject, takeUntil } from 'rxjs'
import {
  EditorComponent,
  EditorPasteEvent,
} from '@progress/kendo-angular-editor'
import { SharedDocInjectModel } from '../../models/document-share.models'

@Component({
  selector: 'venio-document-share-instruction',
  templateUrl: './document-share-instruction.component.html',
  styleUrl: './document-share-instruction.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentShareInstructionComponent
  implements OnInit, OnDestroy, AfterViewInit, OnChanges
{
  @Input() public sharedDocData: SharedDocInjectModel | null

  private readonly toDestroy$ = new Subject<void>()

  public isReadOnly = signal<boolean>(true)

  @ViewChild('editor', { static: true }) public editor!: EditorComponent

  public invitationInProgress$ =
    this.documentShareFacade.getInvitationInProgressFlag$

  public get documentShareForm(): FormGroup {
    return this.documentShareFormService.documentShareForm
  }

  constructor(
    private documentShareFacade: DocumentShareFacade,
    private documentShareFormService: DocumentShareFormService,
    private cdr: ChangeDetectorRef
  ) {}

  public ngOnChanges(changes: SimpleChanges): void {
    if (changes['sharedDocData'] && this.sharedDocData) {
      this.isReadOnly.set(!!this.sharedDocData.isDocShareEdit)
    }
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public ngOnInit(): void {
    if (!this.sharedDocData?.isDocShareEdit) {
      this.isReadOnly.set(false)

      this.invitationInProgress$
        .pipe(takeUntil(this.toDestroy$))
        .subscribe((inProgress) => {
          if (inProgress) {
            this.documentShareForm.get('instruction').disable()
          } else {
            this.documentShareForm.get('instruction').enable()
          }
          this.cdr.detectChanges()
        })
    }
  }

  public ngAfterViewInit(): void {
    const interval = setInterval(() => {
      if (this.editor.view) {
        clearInterval(interval)
        this.editor?.exec('fontSize', '14px')
      }
    }, 100)
  }

  /** Handles the paste event in the editor to allow only plain text pasting */
  public onPaste(e: EditorPasteEvent): void {
    const types = e?.originalEvent?.clipboardData?.types

    if (
      types?.length &&
      ((types instanceof DOMStringList && types.contains('text/plain')) ||
        (types.indexOf && types.indexOf('text/plain') !== -1))
    ) {
      // get the plain text from the clipboard and set it as cleanedHtml
      const pastedData = e.originalEvent.clipboardData.getData('text/plain')
      if (pastedData) {
        e.cleanedHtml = pastedData
        return
      }
    }

    // If the pasted data is not plain text, prevent the default paste action
    e.preventDefault()
    return
  }
}
