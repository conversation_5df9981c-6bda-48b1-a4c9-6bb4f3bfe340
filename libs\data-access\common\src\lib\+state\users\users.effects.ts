import { Injectable } from '@angular/core'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { fetch } from '@ngrx/router-store/data-persistence'
import { catchError, from, map, switchMap } from 'rxjs'
import * as UserActions from './users.actions'
import { HttpErrorResponse } from '@angular/common/http'
import { UserService } from '../../services'
import { CaseConvertorService } from '@venio/util/utilities'
import { UsersListModel } from '@venio/shared/models/interfaces'
import { UuidGenerator } from '@venio/util/uuid'

@Injectable()
export class UserEffects {
  public fetchUserList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(UserActions.fetchUserList),
      fetch({
        run: () => {
          return this.userService.fetchUserList().pipe(
            map((userListSuccessResponse) =>
              UserActions.fetchUserListSuccess({
                userListSuccessResponse,
              })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const userListErrorResponse = error.error
          return UserActions.fetchUserListFailure({
            userListErrorResponse,
          })
        },
      })
    )
  )

  public fetchCurrentUser$ = createEffect(() =>
    this.actions$.pipe(
      ofType(UserActions.fetchCurrentUser),
      fetch({
        // This ensures the concurrent requests are always ran and takes the last
        id: () => UuidGenerator.uuid,
        run: () => {
          return this.userService.fetchCurrentUser().pipe(
            switchMap((response) => {
              const camelCaseConvertorService = new CaseConvertorService()
              const convertedData =
                camelCaseConvertorService.convertToCase<any>(
                  response,
                  'camelCase'
                )
              return from(convertedData)
            }),
            map((currentUserSuccessResponse) => {
              return UserActions.fetchCurrentUserSuccess({
                currentUserSuccessResponse,
              })
            }),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const currentUserErrorResponse = error.error
          return UserActions.fetchCurrentUserFailure({
            currentUserErrorResponse,
          })
        },
      })
    )
  )

  public fetchUserListToInvite$ = createEffect(() =>
    this.actions$.pipe(
      ofType(UserActions.fetchUserListToInvite),
      fetch({
        run: (action) => {
          return this.userService.fetchUserListToInvite(action.projectId).pipe(
            switchMap((response) =>
              new CaseConvertorService()
                .convertToCase<UsersListModel[]>(response, 'camelCase')
                .then((userListToInviteSuccessResponse) =>
                  UserActions.fetchUserListToInviteSuccess({
                    userListToInviteSuccessResponse,
                  })
                )
            )
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const userListToInviteErrorResponse = error.error
          return UserActions.fetchUserListToInviteFailure({
            userListToInviteErrorResponse,
          })
        },
      })
    )
  )

  public fetchExternalUserList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(UserActions.fetchExternalUserListToInvite),
      fetch({
        run: (action) => {
          return this.userService.fetchExternalUserListToInvite().pipe(
            switchMap((response) =>
              new CaseConvertorService()
                .convertToCase<UsersListModel[]>(response, 'camelCase')
                .then((externalUserToInviteSuccessResponse) =>
                  UserActions.fetchExternalUserListToInviteSuccess({
                    externalUserToInviteSuccessResponse,
                  })
                )
            )
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const externalUserToInviteErrorResponse = error.error
          return UserActions.fetchExternalUserListToInviteFailure({
            externalUserToInviteErrorResponse,
          })
        },
      })
    )
  )

  public sendInvitation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(UserActions.sendInvitation),
      fetch({
        run: (action) => {
          return this.userService.sendInvitation(action.payload).pipe(
            map((result) => {
              return UserActions.setSendInvitationResponseMessage({
                sendInvitationResponseMessage: {
                  message: 'Invite successfully sent to upload the files!',
                  success: true,
                },
              })
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const errorResponse = error.error
          return UserActions.setSendInvitationResponseMessage({
            sendInvitationResponseMessage: {
              message: errorResponse.ExceptionMessage,
              success: false,
            },
          })
        },
      })
    )
  )

  constructor(
    private readonly actions$: Actions,
    private userService: UserService
  ) {}
}
