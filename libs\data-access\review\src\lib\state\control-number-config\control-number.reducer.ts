import { createReducer, on } from '@ngrx/store'
import * as ControlNumberConfigActions from './control-number.actions'
import {
  ControlNumberConfigStatus,
  CustodianMediaControlNumberConfig,
} from '../../models/interfaces'
import { ResponseModel } from '@venio/shared/models/interfaces'

export const CONTROL_NUMBER_CONFIG_FEATURE_KEY = 'controlNumberConfig'

export interface ControlNumberConfigState {
  custodianMedia?: CustodianMediaControlNumberConfig[]
  custodianMediaStatus?: ControlNumberConfigStatus[]
  controlNumberConfigError?: ResponseModel
  controlNumberStatusError?: ResponseModel
}

export const initialControlNumberConfigState: ControlNumberConfigState = {
  custodianMedia: [],
  custodianMediaStatus: [],
  controlNumberConfigError: undefined,
  controlNumberStatusError: undefined,
}

export const ControlNumberConfigReducer = createReducer(
  initialControlNumberConfigState,
  on(
    ControlNumberConfigActions.fetchCustodianMediaSuccess$,
    (state, { medias }) => ({
      ...state,
      custodianMedia: medias,
    })
  ),
  on(
    ControlNumberConfigActions.fetchCustodianMediaStatusSuccess$,
    (state, { status }) => ({
      ...state,
      custodianMediaStatus: status,
    })
  ),
  on(
    ControlNumberConfigActions.fetchCustodianMediaFailure$,
    (state, { error }) => {
      return { ...state, controlNumberConfigError: error }
    }
  ),
  on(
    ControlNumberConfigActions.fetchCustodianMediaStatusFailure$,
    (state, { error }) => {
      return { ...state, controlNumberStatusError: error }
    }
  ),
  on(ControlNumberConfigActions.clearControlNumberConfigError, (state) => ({
    ...state,
    controlNumberConfigError: undefined,
  })),
  on(ControlNumberConfigActions.clearControlNumberStatusError, (state) => ({
    ...state,
    controlNumberStatusError: undefined,
  })),
  on(ControlNumberConfigActions.resetControlNumberConfigState, (state) => ({
    ...state,
    custodianMedia: [],
    controlNumberConfigError: undefined,
  })),
  on(ControlNumberConfigActions.resetControlNumberStatusState, (state) => ({
    ...state,
    custodianMediaStatus: [],
    controlNumberStatusError: undefined,
  }))
)
