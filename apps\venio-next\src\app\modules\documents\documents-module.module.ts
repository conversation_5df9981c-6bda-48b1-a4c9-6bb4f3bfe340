import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { DocumentsModuleRoutingModule } from './documents-module-routing.module'
import { DocumentsContainerComponent } from './documents-container/documents-container.component'
import {
  ButtonModule,
  DropDownButtonModule,
} from '@progress/kendo-angular-buttons'
import { BrowsePanelContainerComponent } from './browse-panel/browse-panel-container/browse-panel-container.component'
import { SplitterModule } from '@progress/kendo-angular-layout'
import { GridModule } from '@progress/kendo-angular-grid'
import { SkeletonModule } from '@progress/kendo-angular-indicators'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import { DocumentActionsToolbarComponent } from './document-actions/document-actions-toolbar/document-actions-toolbar.component'

@NgModule({
  declarations: [],
  imports: [
    DocumentsContainerComponent,
    CommonModule,
    DocumentsModuleRoutingModule,
    DropDownButtonModule,
    ButtonModule,
    BrowsePanelContainerComponent,
    SplitterModule,
    GridModule,
    SkeletonModule,
    DialogsModule,
    DocumentActionsToolbarComponent,
  ],
})
export class DocumentsModuleModule {}
