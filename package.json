{"name": "venio-systems", "version": "1.0.0", "license": "MIT", "scripts": {"start": "nx serve", "build": "nx build", "build:with:app-shell": "nx build venio-next:app-shell:production", "analyze:venio-next:bundle": "nx build venio-next --stats-json && webpack-bundle-analyzer dist/venio-next/browser/stats.json", "set:app:version": "node tools/tasks/set-app-version-on-feature-flag.js", "build:ci:with:iis:config": "npm run build:with:app-shell --parallel=6 && node tools/tasks/copy-web-config-to-build.js", "test": "nx test", "test:ci": "nx run-many --target=test --parallel=3 --ci --colors --collectCoverage --code-coverage --with-deps", "lint": "nx run-many --target=lint", "lint:styles": "npx stylelint \"apps/**/*.scss\" \"libs/**/*.scss\" --config .stylelintrc --cache --cache-location=./.stylelint/cache/stylelintcache", "lint:styles:fix": "npx stylelint \"apps/**/*.scss\" \"libs/**/*.scss\" --config .stylelintrc --fix --cache --cache-location=./.stylelint/cache/stylelintcache", "lint:fix": "npm run lint -- --fix --quiet", "lint:quiet": "npm run lint -- --quiet", "e2e": "nx e2e", "affected:apps": "nx affected:apps", "affected:libs": "nx affected:libs", "affected:build": "nx affected:build", "affected:e2e": "nx affected:e2e", "affected:test": "nx affected:test", "affected:lint": "nx affected:lint", "affected:dep-graph": "nx affected:dep-graph", "affected": "nx affected", "format": "nx format", "format:write": "nx format:write --cache --libs-and-apps", "format:check": "nx format:check --libs-and-apps", "update:kendo:ui": "npx npm-check-updates --upgrade --filter \"/@progress.*/\"\n", "activate:kendo:license": "npx kendo-ui-license activate", "nx:update": "npx nx migrate latest && node ./tools/tasks/auto-migrate-dependencies.js", "nx:run:migration": "npx nx migrate --run-migrations", "nx:reset:cache": "nx reset", "nx:repair": "npx nx repair", "dep-graph": "nx dep-graph", "help": "nx help", "nx": "nx", "workspace-generator": "nx workspace-generator", "prepare": "if not defined CI husky install", "create:nx:dir": "node tools/tasks/create-nx-cache-dir.js ", "auto:migrate:lib:deps": "node tools/tasks/auto-migrate-dependencies.js", "postinstall": "npm run create:nx:dir"}, "private": true, "dependencies": {"@angular/animations": "18.2.1", "@angular/cdk": "18.2.1", "@angular/common": "18.2.1", "@angular/compiler": "18.2.1", "@angular/core": "18.2.1", "@angular/forms": "18.2.1", "@angular/localize": "18.2.1", "@angular/platform-browser": "18.2.1", "@angular/platform-browser-dynamic": "18.2.1", "@angular/platform-server": "18.2.1", "@angular/router": "18.2.1", "@angular/service-worker": "18.2.1", "@elastic/apm-rum-angular": "^3.0.4", "@fontsource/roboto": "^5.0.14", "@ngrx/component-store": "18.0.2", "@ngrx/effects": "18.0.2", "@ngrx/entity": "18.0.2", "@ngrx/operators": "^18.0.0", "@ngrx/router-store": "18.0.2", "@ngrx/store": "18.0.2", "@nx/angular": "19.6.4", "@progress/kendo-angular-buttons": "^16.8.0", "@progress/kendo-angular-charts": "^16.8.0", "@progress/kendo-angular-common": "^16.8.0", "@progress/kendo-angular-conversational-ui": "^16.8.0", "@progress/kendo-angular-dateinputs": "^16.8.0", "@progress/kendo-angular-dialog": "^16.8.0", "@progress/kendo-angular-dropdowns": "^16.8.0", "@progress/kendo-angular-editor": "^16.8.0", "@progress/kendo-angular-excel-export": "^16.8.0", "@progress/kendo-angular-filter": "^16.8.0", "@progress/kendo-angular-grid": "^16.8.0", "@progress/kendo-angular-icons": "^16.8.0", "@progress/kendo-angular-indicators": "^16.8.0", "@progress/kendo-angular-inputs": "^16.8.0", "@progress/kendo-angular-intl": "^16.8.0", "@progress/kendo-angular-l10n": "^16.8.0", "@progress/kendo-angular-label": "^16.8.0", "@progress/kendo-angular-layout": "^16.8.0", "@progress/kendo-angular-listbox": "^16.8.0", "@progress/kendo-angular-listview": "^16.8.0", "@progress/kendo-angular-menu": "^16.8.0", "@progress/kendo-angular-navigation": "^16.8.0", "@progress/kendo-angular-notification": "^16.8.0", "@progress/kendo-angular-pager": "^16.8.0", "@progress/kendo-angular-pdf-export": "^16.8.0", "@progress/kendo-angular-pivotgrid": "^16.8.0", "@progress/kendo-angular-popup": "^16.8.0", "@progress/kendo-angular-progressbar": "^16.8.0", "@progress/kendo-angular-sortable": "^16.8.0", "@progress/kendo-angular-toolbar": "^16.8.0", "@progress/kendo-angular-tooltip": "^16.8.0", "@progress/kendo-angular-treelist": "^16.8.0", "@progress/kendo-angular-treeview": "^16.8.0", "@progress/kendo-angular-typography": "^16.8.0", "@progress/kendo-angular-upload": "^16.8.0", "@progress/kendo-angular-utils": "^16.8.0", "@progress/kendo-data-query": "^1.7.0", "@progress/kendo-drawing": "^1.20.2", "@progress/kendo-file-saver": "^1.1.1", "@progress/kendo-licensing": "^1.3.5", "@progress/kendo-svg-icons": "^3.0.0", "@progress/kendo-theme-default": "^9.0.0", "@progress/kendo-theme-fluent": "^9.0.0", "@syncfusion/ej2-angular-spreadsheet": "^24.1.44", "@xmldom/xmldom": "^0.8.10", "angular-plotly.js": "^20.0.0", "angular-tag-cloud-module": "^19.0.0", "comlink": "^4.4.1", "crypto-js": "^4.2.0", "dexie": "^4.0.8", "gridstack": "^10.3.1", "jsencrypt": "^3.3.2", "lodash": "^4.17.21", "marked": "^14.1.0", "ngx-cookie-service": "^19.0.0", "ngx-infinite-scroll": "^17.0.0", "plotly.js-dist-min": "^3.0.1", "pspdfkit": "^2023.4.4", "rxjs": "7.8.1", "tslib": "^2.3.0", "zone.js": "0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "18.2.1", "@angular-devkit/core": "18.2.1", "@angular-devkit/schematics": "18.2.1", "@angular-eslint/eslint-plugin": "18.0.1", "@angular-eslint/eslint-plugin-template": "18.0.1", "@angular-eslint/template-parser": "18.0.1", "@angular/cli": "~18.2.0", "@angular/compiler-cli": "18.2.1", "@angular/language-service": "18.2.1", "@angular/pwa": "18.2.1", "@commitlint/cli": "^17.0.3", "@commitlint/config-conventional": "^17.0.3", "@commitlint/config-nx-scopes": "^17.0.0", "@inrupt/jest-jsdom-polyfills": "^2.1.3", "@ngrx/schematics": "18.0.2", "@ngrx/store-devtools": "18.0.2", "@nx/cypress": "19.6.4", "@nx/devkit": "19.6.4", "@nx/eslint": "19.6.4", "@nx/eslint-plugin": "19.6.4", "@nx/jest": "19.6.4", "@nx/js": "19.6.4", "@nx/web": "19.6.4", "@nx/webpack": "19.6.4", "@nx/workspace": "19.6.4", "@schematics/angular": "18.2.1", "@swc-node/register": "1.9.1", "@swc/core": "1.5.7", "@swc/helpers": "0.5.11", "@types/crypto-js": "^4.2.1", "@types/jasmine": "4.0.3", "@types/jest": "29.5.12", "@types/lodash": "^4.14.200", "@types/node": "^18.16.9", "@typescript-eslint/eslint-plugin": "7.16.0", "@typescript-eslint/parser": "7.16.0", "@typescript-eslint/utils": "^7.16.0", "autoprefixer": "^10.4.0", "chalk": "^4.1.2", "cypress": "13.13.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-cypress": "2.13.4", "eslint-plugin-jest": "^26.0.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-rxjs": "^5.0.2", "eslint-plugin-unused-imports": "^3.2.0", "fake-indexeddb": "^4.0.1", "husky": "^8.0.1", "jasmine-core": "4.2.0", "jasmine-marbles": "~0.9.1", "jasmine-spec-reporter": "7.0.0", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-junit": "^14.0.1", "jest-preset-angular": "14.1.0", "jsonc-eslint-parser": "^2.1.0", "karma": "6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "5.1.0", "karma-jasmine-html-reporter": "2.0.0", "lint-staged": "^15.2.10", "ng-packagr": "18.2.1", "nx": "19.6.4", "nx-cloud": "19.0.0", "postcss": "^8.4.5", "postcss-import": "14.1.0", "postcss-preset-env": "7.5.0", "postcss-scss": "^4.0.5", "postcss-url": "10.1.3", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.2.8", "stylelint": "^14.16.1", "stylelint-checkstyle-formatter": "~0.1.2", "stylelint-config-prettier": "^9.0.5", "stylelint-config-standard-scss": "^11.0.0", "stylelint-order": "~6.0.3", "stylelint-prettier": "^3.0.0", "tailwindcss": "~3.3.3", "ts-jest": "29.1.1", "ts-node": "10.9.1", "typescript": "5.5.4", "webpack-bundle-analyzer": "^4.7.0"}}