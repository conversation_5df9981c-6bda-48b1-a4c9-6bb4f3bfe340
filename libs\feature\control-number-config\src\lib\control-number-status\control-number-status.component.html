<div class="t-h-full t-pt-4">
  <div class="t-flex t-flex-row t-gap-2">
    <kendo-dropdownlist
      id="field-to-update"
      class="!t-w-52 t-ml-4"
      [data]="controlNumberStatusList"
      (valueChange)="onFilterValueChange($event)"
      textField="label"
      valueField="value"
      [defaultItem]="{ value: null, label: 'Filter by Status' }"
      data-qa="filter-control-number-status">
    </kendo-dropdownlist>
    <button
      #parentRefresh
      class="!t-p-1.5 t-h-fit !t-border-[#9AD3A6] t-leading-[0.8] hover:!t-bg-[#9AD3A6]"
      kendoButton
      size="none"
      fillMode="outline"
      (click)="refreshStatus()"
      title="Refresh">
      <span
        [parentElement]="parentRefresh.element"
        venioSvgLoader
        svgUrl="assets/svg/refresh.svg"
        color="#9AD3A6"
        hoverColor="#FFFFFF"
        height="1rem"
        width="1rem"></span>
    </button>
  </div>
  <kendo-treelist
    [kendoTreeListFlatBinding]="filteredTreeData()"
    [parentIdField]="'parentId'"
    [idField]="'id'"
    childrenField="children"
    [loading]="isTreeLoading()"
    kendoTreeListExpandable
    expandBy="id"
    [(expandedKeys)]="expandedIds"
    [trackBy]="treeViewTrackByFn"
    [pageSize]="15"
    [autoSize]="true"
    [navigable]="true"
    [hideHeader]="true"
    venioDynamicHeight
    [extraSpacing]="80"
    class="control-number-tree t-pt-4 !t-border-0 !t-w-full v-custom-treelist-no-alt-bg">
    <kendo-treelist-column
      [expandable]="true"
      field="custodianName"
      title="Custodian Name"
      class="t-flex t-items-baseline !t-border-0">
      <ng-template kendoTreeListCellTemplate let-dataItem>
        <div *ngIf="dataItem.isJobRoot; else nonJobRow" class="t-text-lg">
          <span class="t-font-bold">Job ID </span>{{ dataItem.jobId }}
        </div>

        <ng-template #nonJobRow>
          <div *ngIf="dataItem.isCustodian; else mediaRow">
            <div class="t-flex t-space-x-2">
              <!-- custodian name -->
              <span class="t-font-bold">Custodian Name</span>
              <span>{{ dataItem.custodianName }}</span>
              <span class="t-flex t-items-center" *ngIf="dataItem.hasWarning">
                <img
                  src="assets/svg/icon-warning.svg"
                  height="18"
                  width="15"
                  alt="Warning"
                  [popover]="controlNumberWarning"
                  showOn="hover"
                  kendoPopoverAnchor />
              </span>
            </div>
          </div>
        </ng-template>

        <ng-template #mediaRow>
          <div class="t-flex t-flex-row t-gap-2 t-text-xs">
            <div class="t-flex t-flex-col t-gap-2 t-w-48">
              <div class="t-flex t-space-x-2">
                <!-- lock icon -->
                <ng-container *ngIf="dataItem.isEditable; else lockedIcon">
                  <!-- media level checkbox-->
                  <span class="t-w-4"></span>
                </ng-container>
                <ng-template #lockedIcon>
                  <span
                    venioSvgLoader
                    applyEffectsTo="fill"
                    color="#FD696C"
                    hoverColor="#FFBB12"
                    svgUrl="assets/svg/icon-lock.svg"
                    height="1rem"
                    width="1rem">
                    <kendo-loader size="small"></kendo-loader>
                  </span>
                </ng-template>

                <!-- Media name -->
                <div
                  class="t-max-w-[100%] t-overflow-hidden t-text-ellipsis t-whitespace-no-wrap">
                  {{ dataItem.mediaName }}
                </div>
              </div>

              <!-- Prefix -->
              <div
                class="t-flex t-items-center t-pl-6 t-space-x-1 t-p-[0.3rem]">
                <span class="t-font-bold t-text-[#8F8F8F]">Prefix</span>
                <span
                  class="t-max-w-sm t-overflow-hidden t-text-ellipsis t-text-[#8F8F8F]"
                  >{{ dataItem.prefix }}</span
                >
              </div>
            </div>

            <!-- Start Date -->
            <div
              class="t-flex t-flex-col t-gap-2 t-w-48"
              *ngIf="dataItem.parentId">
              <div>&nbsp;</div>
              <div class="t-p-[0.3rem] t-space-x-1 t-text-[#8F8F8F]">
                <span class="t-font-bold">Start Date</span>
                <span>{{
                  dataItem.startDate | date : 'MM dd yyyy hh:mm:ss a'
                }}</span>
              </div>
            </div>

            <!-- End Date -->
            <div
              class="t-flex t-flex-col t-gap-2 t-w-48"
              *ngIf="dataItem.parentId">
              <div>&nbsp;</div>
              <div class="t-p-[0.3rem] t-space-x-1 t-text-[#8F8F8F]">
                <span class="t-font-bold">End Date</span>
                <span>{{
                  dataItem.endDate | date : 'MM dd yyyy hh:mm:ss a'
                }}</span>
              </div>
            </div>

            <!-- Status and download button -->
            <div class="t-flex t-flex-col t-gap-2" *ngIf="dataItem.parentId">
              <div>&nbsp;</div>
              <div class="t-flex t-p-[0.3rem] t-space-x-1 t-text-[#8F8F8F]">
                <span [class]="getStatusColor(dataItem.status)">{{
                  getStatusText(dataItem.status)
                }}</span>

                <!-- Download button -->
                <button
                  class="t-flex t-items-center"
                  *ngIf="getStatusText(dataItem.status) === 'COMPLETED'">
                  <img
                    src="assets/svg/download.svg"
                    height="18"
                    width="15"
                    (click)="download()"
                    alt="Download" />
                </button>

                <!--warning-->
                <span class="t-flex t-items-center" *ngIf="dataItem.hasWarning">
                  <img
                    src="assets/svg/icon-warning.svg"
                    height="18"
                    width="15"
                    alt="Warning"
                    [popover]="controlNumberWarning"
                    showOn="hover"
                    kendoPopoverAnchor />
                </span>
              </div>
            </div>
          </div>
        </ng-template>
      </ng-template>
    </kendo-treelist-column>

    <ng-template kendoTreeListNoRecordsTemplate>
      <div *ngIf="!isTreeLoading()">No data</div>
    </ng-template>
  </kendo-treelist>
</div>

<kendo-popover #controlNumberWarning [width]="300" position="right">
  <ng-template kendoPopoverBodyTemplate>
    <div class="t-text-[#ED7428] t-text-sm">{{ custodianWarningMsg }}</div>
  </ng-template>
</kendo-popover>
