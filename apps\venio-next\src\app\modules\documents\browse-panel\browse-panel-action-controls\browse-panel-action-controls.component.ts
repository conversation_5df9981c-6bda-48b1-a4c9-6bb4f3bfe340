import {
  ChangeDetectionStrategy,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { IconsModule } from '@progress/kendo-angular-icons'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { CommandsFacade } from '@venio/data-access/common'
import {
  CommandEventTypes,
  FolderTabType,
  CommonActionTypes,
} from '@venio/shared/models/constants'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { Subject, takeUntil } from 'rxjs'

@Component({
  selector: 'venio-browser-panel-action-controls',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    ButtonsModule,
    SvgLoaderDirective,
    TooltipsModule,
    IndicatorsModule,
  ],
  templateUrl: './browse-panel-action-controls.component.html',
  styleUrls: ['./browse-panel-action-controls.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BrowsePanelActionControlsComponent implements OnInit, OnDestroy {
  public selectedAction = FolderTabType.SYSTEM

  private unsubscribed$ = new Subject<void>()

  constructor(private commandsFacade: CommandsFacade) {}

  public ngOnInit(): void {
    this.#selectFolderTabType()
  }

  #selectFolderTabType(): void {
    this.commandsFacade.selectFolderTabType$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((folderTab) => {
        this.selectedAction = folderTab as FolderTabType
      })
  }

  public ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }

  public svgIconForControls = [
    {
      actionType: FolderTabType.SYSTEM,
      iconPath: 'assets/svg/icon-document-folder.svg',
      applyEffectTo: 'fill' as any,
    },
    {
      actionType: FolderTabType.AUTO,
      iconPath: 'assets/svg/icon-tree-view.svg',
      applyEffectTo: 'fill' as any,
    },
    {
      actionType: FolderTabType.CUSTOM,
      iconPath: 'assets/svg/icon-save-search.svg',
      applyEffectTo: 'fill' as any,
    },
  ]

  public browseActionClicked(folderTabType: FolderTabType): void {
    this.selectedAction = folderTabType
    this.commandsFacade.dispatchCommand({
      type: CommandEventTypes.StoreFolderTabType,
      data: folderTabType,
    })
  }

  protected readonly commonActionTypes = CommonActionTypes
}
