import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { environment } from '@venio/shared/environments'
import { Observable } from 'rxjs'
import {
  ClientProjectRequestModel,
  LayoutCreateRequestModel,
  LayoutDeleteRequestModel,
  MarkAsFavoriteRequestModel,
} from '../../models/interfaces/composite-layout/composite-layout.model'

@Injectable({
  providedIn: 'root',
})
export class CompositeLayoutService {
  private get _apiUrl(): string {
    return environment.apiUrl
  }

  constructor(private http: HttpClient) {}

  public readonly fetchLayouts$ = <T>(projectId: number): Observable<T> =>
    this.http.get<T>(
      `${this._apiUrl}composite-layout/project/${projectId}/layouts`
    )

  public readonly fetchLayoutPanels$ = <T>(layoutId: number): Observable<T> =>
    this.http.get<T>(
      `${this._apiUrl}composite-layout/layout/${layoutId}/layout-panels`
    )

  public readonly fetchLayoutPanelFields$ = <T>(
    layoutId: number,
    panelId: number
  ): Observable<T> =>
    this.http.get<T>(
      `${this._apiUrl}composite-layout/layout/${layoutId}/panel/${panelId}/panel-fields`
    )

  public readonly createLayout$ = <T>(
    payload: LayoutCreateRequestModel
  ): Observable<T> =>
    this.http.post<T>(`${this._apiUrl}composite-layout/create`, payload)

  public readonly cloneLayout$ = <T>(
    payload: LayoutCreateRequestModel
  ): Observable<T> =>
    this.http.post<T>(`${this._apiUrl}composite-layout/clone`, payload)

  public readonly updateLayout$ = <T>(
    layoutId: number,
    payload: LayoutCreateRequestModel
  ): Observable<T> =>
    this.http.post<T>(
      `${this._apiUrl}composite-layout/layout/${layoutId}/update`,
      payload
    )

  public readonly deleteLayout$ = <T>(
    payload: LayoutDeleteRequestModel
  ): Observable<T> =>
    this.http.post<T>(`${this._apiUrl}composite-layout/delete`, payload)

  public readonly fetchDefaultLayout$ = <T>(projectId: number): Observable<T> =>
    this.http.get<T>(
      `${this._apiUrl}composite-layout/project/${projectId}/default-layout`
    )

  public readonly fetchLayout$ = <T>(layoutId: number): Observable<T> =>
    this.http.get<T>(`${this._apiUrl}composite-layout/layout/${layoutId}`)

  public readonly fetchClients$ = <T>(): Observable<T> =>
    this.http.get<T>(`${this._apiUrl}client/clients`)

  public readonly fetchCases$ = <T>(): Observable<T> =>
    this.http.get<T>(`${this._apiUrl}cases`)

  public readonly fetchCasesByClientIds$ = <T>(
    payload: ClientProjectRequestModel
  ): Observable<T> =>
    this.http.post<T>(`${this._apiUrl}cases/client-project-list`, payload)

  public readonly fetchUserGroups$ = <T>(projectId: number): Observable<T> =>
    this.http.get<T>(`${this._apiUrl}project/${projectId}/group`)

  public readonly fetchProjectUserGroups$ = <T>(
    projectIds: number[]
  ): Observable<T> =>
    this.http.post<T>(`${this._apiUrl}cases/project-user-groups`, projectIds)

  public readonly fetchLayoutClientProjectUsergroups$ = <T>(
    layoutId: number
  ): Observable<T> =>
    this.http.get<T>(
      `${this._apiUrl}composite-layout/layout/${layoutId}/client-project-groups`
    )

  public readonly isLayoutNameTaken$ = <T>(
    layoutId: number,
    layoutName: string
  ): Observable<T> =>
    this.http.get<T>(
      `${this._apiUrl}composite-layout/layout/${layoutId}/layout-name/${layoutName}`
    )

  public readonly markLayoutasFavorite$ = <T>(
    projectId: number,
    layoutId: number,
    model: MarkAsFavoriteRequestModel
  ): Observable<T> =>
    this.http.post<T>(
      `${this._apiUrl}composite-layout/project/${projectId}/layout/${layoutId}/favorite`,
      model
    )

  public readonly fetchReviewSetDefaultLayout$ = <T>(
    projectId: number,
    reviewSetId: number
  ): Observable<T> => {
    return this.http.get<T>(
      `${this._apiUrl}project/${projectId}/reviewset/${reviewSetId}/defaultLayout`
    )
  }
}
