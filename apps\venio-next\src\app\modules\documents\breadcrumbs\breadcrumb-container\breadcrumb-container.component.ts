import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { SkeletonModule } from '@progress/kendo-angular-indicators'
import { BreadcrumbStackContainerComponent } from '../breadcrumb-stack-container/breadcrumb-stack-container.component'
import { BreadcrumbTopBarActionsComponent } from '../breadcrumb-top-bar-actions/breadcrumb-top-bar-actions.component'

@Component({
  selector: 'venio-breadcrumb-container',
  standalone: true,
  imports: [
    CommonModule,
    SkeletonModule,
    BreadcrumbStackContainerComponent,
    BreadcrumbTopBarActionsComponent,
  ],
  templateUrl: './breadcrumb-container.component.html',
  styleUrl: './breadcrumb-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BreadcrumbContainerComponent {}
