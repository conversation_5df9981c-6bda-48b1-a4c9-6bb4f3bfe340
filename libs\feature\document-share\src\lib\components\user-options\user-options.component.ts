import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostListener,
  input,
  On<PERSON><PERSON><PERSON>,
  OnInit,
  ViewChild,
} from '@angular/core'
import {
  DocumentShareFacade,
  DocumentShareUserModel,
  SharedDocumentUserModel,
} from '@venio/data-access/review'
import { Subject, combineLatest, debounceTime, filter, takeUntil } from 'rxjs'
import { plusIcon } from '@progress/kendo-svg-icons'
import { FormGroup } from '@angular/forms'
import { DocumentShareFormService } from '../../services/document-share-form.service'
import _ from 'lodash'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { SharedDocInjectModel } from '../../models/document-share.models'

@Component({
  selector: 'venio-user-options',
  templateUrl: './user-options.component.html',
  styleUrl: './user-options.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UserOptionsComponent implements On<PERSON><PERSON><PERSON>, OnD<PERSON>roy {
  @ViewChild('treeListContainer', { static: true })
  private treelistContainerDivRef!: ElementRef

  public sharedDocData = input<SharedDocInjectModel | null>(null)

  private readonly toDestroy$ = new Subject<void>()

  public invitationInProgress$ =
    this.documentShareFacade.getInvitationInProgressFlag$

  public internalUserSelectionKey: string | any

  public externalUserSelectionKey: string | any

  public get documentShareForm(): FormGroup {
    return this.documentShareFormService.documentShareForm
  }

  public selectedExternalUsers: string[] = []

  public selectedInternalUsers: string[] = []

  public plusIcon = plusIcon

  public infoSvgUrl = 'assets/svg/material_info_outline.svg'

  public internalUsers: (DocumentShareUserModel | SharedDocumentUserModel)[] =
    []

  public externalUsers: (DocumentShareUserModel | SharedDocumentUserModel)[] =
    []

  public shareToExternalUsers = false

  public filteredInternalUsers: (
    | DocumentShareUserModel
    | SharedDocumentUserModel
  )[] = []

  public filteredExternalUsers: (
    | DocumentShareUserModel
    | SharedDocumentUserModel
  )[] = []

  public fileTypeGridHeight!: number

  private resizeEvent = new Subject<Event>()

  constructor(
    private readonly documentShareFacade: DocumentShareFacade,
    private readonly documentShareFormService: DocumentShareFormService,
    private readonly cdr: ChangeDetectorRef
  ) {}

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public ngOnInit(): void {
    this.resizeEvent
      .pipe(debounceTime(200), takeUntil(this.toDestroy$))
      .subscribe((): void => {
        this.resizeFileTypeGrid()
        this.cdr.markForCheck()
      })

    if (
      (this.sharedDocData()?.isDocShareEdit &&
        !_.isUndefined(this.sharedDocData()?.shareDocData) &&
        !_.isNull(this.sharedDocData()?.shareDocData)) ||
      this.documentShareFacade.isEditMode
    ) {
      this.documentShareFacade.selectSharedDocumentDetail$
        .pipe(
          filter((res: ResponseModel) => !!res?.data),
          takeUntil(this.toDestroy$)
        )
        .subscribe((res) => {
          this.internalUsers = [...(res?.data.internalUsers || [])]
          this.externalUsers = [...(res?.data.externalUsers || [])]
          this.filteredInternalUsers = [...this.internalUsers]
          this.filteredExternalUsers = [...this.externalUsers]
          this.internalUserSelectionKey = (item: any): any => item?.dataItem
          this.externalUserSelectionKey = (item: any): any => item?.dataItem
          const externalUsersCount = this.externalUsers.length || 0
          this.documentShareFormService.updateExternalSelectedRowsCount(
            externalUsersCount
          )
          const internalUsersCount = this.internalUsers.length || 0
          this.documentShareFormService.updateInternalSelectedRowsCount(
            internalUsersCount
          )

          this.documentShareForm.get('allowToTag')?.disable()
          this.documentShareForm.get('allowToAddNotes')?.disable()
          this.documentShareForm.get('allowToViewAnalyzePage')?.disable()
          this.documentShareForm.get('allowRedaction')?.disable()

          this.cdr.markForCheck()
        })
    } else {
      this.internalUserSelectionKey = 'userInfoString'
      this.externalUserSelectionKey = 'email'
      this.initSlices()
    }
  }

  @HostListener('window:resize', ['$event'])
  public onContainerResize(event: Event): void {
    this.resizeEvent.next(event)
  }

  private resizeFileTypeGrid(): void {
    this.fileTypeGridHeight = 0
    setTimeout((): void => {
      const divElement = this.treelistContainerDivRef.nativeElement
      this.fileTypeGridHeight =
        divElement.offsetHeight - 8 < 140 ? 140 : divElement.offsetHeight - 8
      this.cdr.markForCheck()
    }, 30)
  }

  private initSlices(): void {
    combineLatest([
      this.documentShareFacade.getInternalUsers$,
      this.documentShareFacade.getExternalUsers$,
    ])
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(
        ([internalUsers, externalUsers]: [
          DocumentShareUserModel[],
          DocumentShareUserModel[]
        ]): void => {
          this.externalUsers = this.trimAndRemoveEmptyUserEntries(externalUsers)
          this.internalUsers = this.trimAndRemoveEmptyUserEntries(internalUsers)
          this.internalUsers = this.internalUsers.map(
            (user: DocumentShareUserModel) => ({
              ...user,
              userInfoString:
                user.userId +
                ':' +
                user.email +
                ':' +
                user.userRole +
                ':' +
                user.fullName,
            })
          )

          this.filteredInternalUsers = [...this.internalUsers]
          this.filteredExternalUsers = [...this.externalUsers]
          this.cdr.detectChanges()
        }
      )

    if (this.documentShareForm.get('shareToExternalUsers')) {
      this.documentShareForm
        .get('shareToExternalUsers')
        ?.valueChanges.pipe(takeUntil(this.toDestroy$))
        .subscribe((value: boolean): void => {
          this.shareToExternalUsers = value
          if (value) {
            this.documentShareForm.get('newEmail')?.enable()
            this.documentShareForm.get('allowToTag')?.enable()
            this.documentShareForm.get('allowToAddNotes')?.enable()
            this.documentShareForm.get('allowToViewAnalyzePage')?.enable()
            this.documentShareForm.get('allowRedaction')?.enable()
          } else {
            this.documentShareForm.get('newEmail')?.disable()
            this.documentShareForm.get('allowToTag')?.disable()
            this.documentShareForm.get('allowToAddNotes')?.disable()
            this.documentShareForm.get('allowToViewAnalyzePage')?.disable()
            this.documentShareForm.get('allowRedaction')?.disable()
          }
          this.cdr.detectChanges()
        })
    }
    this.invitationInProgress$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((inProgress: boolean) => {
        const possibleControls = [
          'shareToExternalUsers',
          'newEmail',
          'allowToTag',
          'allowToAddNotes',
          'allowToViewAnalyzePage',
          'allowRedaction',
        ]

        possibleControls.forEach((controlName) => {
          const control = this.documentShareForm.get(controlName)
          if (control) {
            inProgress ? control.disable() : control.enable()
          }
        })
        this.cdr.detectChanges()
      })
  }

  public onFilter(searchInput: string): void {
    const filterValue = searchInput.toLowerCase().trim()
    if (!this.sharedDocData()?.isDocShareEdit) {
      this.filteredInternalUsers = this.internalUsers.filter(
        (user: DocumentShareUserModel) =>
          user.email.toLowerCase().includes(filterValue)
      )
    } else {
      this.filteredInternalUsers = this.internalUsers.filter(
        (user: SharedDocumentUserModel) =>
          user.userName.toLowerCase().includes(filterValue)
      )
    }
    this.cdr.detectChanges()
  }

  public onFilterExternalUser(searchInput: string): void {
    const filterValue = searchInput.toLowerCase().trim()
    if (!this.sharedDocData()?.isDocShareEdit) {
      this.filteredExternalUsers = this.externalUsers.filter(
        (user: DocumentShareUserModel) =>
          user.email.toLowerCase().includes(filterValue)
      )
    } else {
      this.filteredExternalUsers = this.externalUsers.filter(
        (user: SharedDocumentUserModel) =>
          user.userName.toLowerCase().includes(filterValue)
      )
    }
    this.cdr.detectChanges()
  }

  private trimAndRemoveEmptyUserEntries(
    users: DocumentShareUserModel[]
  ): DocumentShareUserModel[] {
    return (
      users
        ?.map((user) => ({
          ...user,
          email: user.email?.trim().toLowerCase(),
        }))
        .filter((user) => !!user.email) ?? []
    )
  }

  public addExternalUser(): void {
    const emailControl = this.documentShareForm.get('newEmail')
    const newEmail = emailControl?.value?.trim().toLowerCase() ?? ''
    emailControl?.setValue(newEmail)

    if (!newEmail || !this.validateEmail(newEmail)) {
      this.documentShareFacade.setUserMessage(
        'Please enter a valid email address.',
        false
      )
      return
    } else if (this.externalUserExists(newEmail)) {
      this.documentShareFacade.setUserMessage(
        'The provided email already exists in the list.',
        false
      )
      return
    }

    const newUser: DocumentShareUserModel = {
      email: newEmail,
      fullName: newEmail,
      userId: 0,
      userRole: '',
      userInfoString: '',
    }
    this.externalUsers = [...this.externalUsers, newUser]
    this.filteredExternalUsers = [...this.externalUsers]
    emailControl?.setValue('')
    this.cdr.detectChanges()
  }

  private validateEmail(email: string): boolean {
    const emailRegex =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
    const isEmailFormatOk = emailRegex.test(email)
    if (!isEmailFormatOk) {
      return false
    }
    const emailParts = email.split('@')
    if (
      emailParts[0]?.length > 64 ||
      (emailParts?.length > 1 && emailParts[1].length > 255) ||
      email?.length > 320
    ) {
      return false
    }
    return true
  }

  private externalUserExists(email: string): boolean {
    return this.filteredExternalUsers?.some(
      (user: DocumentShareUserModel) => user?.email === email
    )
  }

  public onExternalUserSelectionChange(): void {
    if (this.sharedDocData()?.isDocShareEdit) {
      this.documentShareFormService.setExternalUser(
        this.selectedExternalUsers,
        true
      )
    } else {
      this.documentShareFormService.setExternalUser(this.selectedExternalUsers)
    }
  }

  public onInternalUserSelectionChange(): void {
    if (this.sharedDocData()?.isDocShareEdit) {
      this.documentShareFormService.setInternalUser(
        this.selectedInternalUsers,
        true
      )
    } else {
      this.documentShareFormService.setInternalUser(this.selectedInternalUsers)
    }
  }
}
