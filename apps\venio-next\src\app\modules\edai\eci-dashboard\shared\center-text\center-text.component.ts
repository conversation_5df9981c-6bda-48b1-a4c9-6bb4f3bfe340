import { Component, inject, input } from '@angular/core'
import { Ng<PERSON>lass, NgIf } from '@angular/common'
import { hyperlinkOpenSmIcon, SVGIcon } from '@progress/kendo-svg-icons'
import { KENDO_BUTTONS } from '@progress/kendo-angular-buttons'
import { AiFacade, SunburstChartType } from '@venio/data-access/ai'

@Component({
  selector: 'venio-center-text',
  standalone: true,
  imports: [KENDO_BUTTONS, NgIf, NgClass],
  templateUrl: './center-text.component.html',
  styleUrl: './center-text.component.scss',
})
export class CenterTextComponent {
  public readonly centerText = input.required<SunburstChartType>()

  public readonly showViewDetails = input<boolean>(true)

  public readonly isExpanded = input<boolean>(false) // New input to track if chart is expanded

  private readonly aiFacade = inject(AiFacade)

  public readonly sunburstChartType = SunburstChartType

  public readonly svgOpenNew: SVGIcon = hyperlinkOpenSmIcon

  public openFocusedSection(): void {
    // Determine chart type based on centerText
    let chartType: SunburstChartType
    const centerText = this.centerText()
    switch (centerText) {
      case 'Document Types':
        chartType = SunburstChartType.DocumentTypes
        break
      case 'Relevant Documents':
        chartType = SunburstChartType.RelevantDocuments
        break
      case 'Not Relevant Documents':
        chartType = SunburstChartType.NotRelevantDocuments
        break
      case 'Relevance':
        chartType = SunburstChartType.Relevance
        break
    }

    // Set the active chart type and open focused section
    this.aiFacade.setActiveChartType(chartType)
    this.aiFacade.setEciFocusedSectionOpened(true)
  }
}
