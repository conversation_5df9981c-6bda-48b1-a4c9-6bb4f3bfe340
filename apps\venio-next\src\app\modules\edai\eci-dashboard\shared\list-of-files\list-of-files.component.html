<div class="t-relative t-p-4 t-bg-white t-border t-border-[#dcdcdc] t-rounded">
  <div class="t-flex t-justify-end t-mb-4">
    <venio-pagination
      [disabled]="files().length === 0"
      [totalRecords]="paginationState().totalRecords"
      [pageSize]="paginationState().pageSize"
      [currentPage]="paginationState().currentPage"
      [showPageJumper]="true"
      [showPageSize]="true"
      [showRowNumberInputBox]="true"
      (pageChanged)="pageChanged($event)"
      (pageSizeChanged)="pageSizeChanged($event)">
    </venio-pagination>
  </div>
  <kendo-grid [data]="pagedFiles()" [height]="300" [pageable]="false">
    <kendo-grid-column field="rowNumber" title="#" [width]="40">
      <ng-template kendoGridCellTemplate let-rowIndex="rowIndex">
        {{
          paginationState().pageSize * (paginationState().currentPage - 1) +
            (rowIndex + 1)
        }}
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-checkbox-column [width]="40"> </kendo-grid-checkbox-column>
    <kendo-grid-column field="filename" title="File Name" [width]="200">
      <ng-template kendoGridCellTemplate let-dataItem>
        <span class="t-font-medium t-text-[#1ebadc]" (click)="loadFulltextViewer(dataItem)">{{
          dataItem.filename
        }}</span>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column field="summary" title="Summary">
      <ng-template kendoGridCellTemplate let-dataItem>
        <div class="t-break-words t-whitespace-normal t-text-gray-600">
          {{ dataItem.summary }}
        </div>
      </ng-template>
    </kendo-grid-column>
  </kendo-grid>
</div>
