import { Action, createReducer, on } from '@ngrx/store'
import { ResponseModel, UsersListModel } from '@venio/shared/models/interfaces'
import * as UserActions from './users.actions'
import { resetStateProperty } from '@venio/util/utilities'
import { UserMessageModel } from '@venio/data-access/review'

export const USERS_VIEW_FEATURE_KEY = 'usersStore'

export interface UsersState {
  // Loading State Indicators: Flags to indicate if a certain operation is currently in progress
  isUserListLoading: boolean | undefined
  isCurrentUserLoading: boolean | undefined
  invitationInProgress: boolean | undefined

  // User List Operation Responses: Success and error responses for User List operations
  userListSuccessResponse: ResponseModel | undefined
  userListErrorResponse: ResponseModel | undefined

  // Current User Operation Responses: Success and error responses for Current User operations
  currentUserSuccessResponse: ResponseModel | undefined
  currentUserErrorResponse: ResponseModel | undefined

  // User List To Invite Operation Responses: Success and error responses for User List operations
  userListToInviteSuccessResponse: UsersListModel[] | undefined
  userListToInviteErrorResponse: ResponseModel | undefined

  // external user
  externalUserToInviteSuccessResponse: UsersListModel[] | undefined
  externalUserToInviteErrorResponse: ResponseModel | undefined

  sendInvitationResponseMessage: UserMessageModel | undefined
}

export const usersState: UsersState = {
  isUserListLoading: undefined,
  isCurrentUserLoading: undefined,
  invitationInProgress: undefined,
  userListErrorResponse: undefined,
  userListSuccessResponse: undefined,
  currentUserErrorResponse: undefined,
  currentUserSuccessResponse: undefined,
  userListToInviteSuccessResponse: undefined,
  userListToInviteErrorResponse: undefined,
  sendInvitationResponseMessage: undefined,
  externalUserToInviteSuccessResponse: undefined,
  externalUserToInviteErrorResponse: undefined,
}

export interface UsersPartialState {
  readonly [USERS_VIEW_FEATURE_KEY]: UsersState
}

const reducer = createReducer<UsersState>(
  usersState,
  on(UserActions.resetUserState, (state, { stateKey }) =>
    resetStateProperty<UsersState>(state, usersState, stateKey)
  ),
  on(UserActions.fetchUserList, (state) => ({
    ...state,
    isUserListLoading: true,
  })),
  on(
    UserActions.fetchUserListSuccess,
    (state, { userListSuccessResponse }) => ({
      ...state,
      userListSuccessResponse,
      isUserListLoading: false,
    })
  ),
  on(UserActions.fetchUserListFailure, (state, { userListErrorResponse }) => ({
    ...state,
    userListErrorResponse,
    isUserListLoading: false,
  })),
  on(UserActions.fetchCurrentUser, (state) => ({
    ...state,
    isCurrentUserLoading: true,
  })),
  on(
    UserActions.fetchCurrentUserSuccess,
    (state, { currentUserSuccessResponse }) => ({
      ...state,
      currentUserSuccessResponse,
      isCurrentUserLoading: false,
    })
  ),
  on(
    UserActions.fetchCurrentUserFailure,
    (state, { currentUserErrorResponse }) => ({
      ...state,
      currentUserErrorResponse,
      isCurrentUserLoading: false,
    })
  ),
  on(UserActions.fetchUserListToInvite, (state) => ({
    ...state,
    invitationInProgress: true,
  })),
  on(
    UserActions.fetchUserListToInviteSuccess,
    (state, { userListToInviteSuccessResponse }) => ({
      ...state,
      userListToInviteSuccessResponse,
      invitationInProgress: false,
    })
  ),
  on(
    UserActions.fetchUserListToInviteFailure,
    (state, { userListToInviteErrorResponse }) => ({
      ...state,
      userListToInviteErrorResponse,
      invitationInProgress: false,
    })
  ),
  on(UserActions.sendInvitation, (state) => ({
    ...state,
    invitationInProgress: true,
  })),
  on(
    UserActions.setSendInvitationResponseMessage,
    (state, { sendInvitationResponseMessage }) => ({
      ...state,
      sendInvitationResponseMessage,
      invitationInProgress: false,
    })
  ),
  on(UserActions.fetchExternalUserListToInvite, (state) => ({
    ...state,
    invitationInProgress: true,
  })),
  on(
    UserActions.fetchExternalUserListToInviteSuccess,
    (state, { externalUserToInviteSuccessResponse }) => ({
      ...state,
      externalUserToInviteSuccessResponse,
      invitationInProgress: false,
    })
  ),
  on(
    UserActions.fetchExternalUserListToInviteFailure,
    (state, { externalUserToInviteErrorResponse }) => ({
      ...state,
      externalUserToInviteErrorResponse,
      invitationInProgress: false,
    })
  ),
  on(UserActions.sendInvitation, (state) => ({
    ...state,
    invitationInProgress: true,
  })),
  on(
    UserActions.setSendInvitationResponseMessage,
    (state, { sendInvitationResponseMessage }) => ({
      ...state,
      sendInvitationResponseMessage,
      invitationInProgress: false,
    })
  ),
  on(UserActions.clearInternalUserFetchError, (state) => ({
    ...state,
    userListToInviteErrorResponse: undefined,
  })),
  on(UserActions.clearExternalUserFetchError, (state) => ({
    ...state,
    externalUserToInviteErrorResponse: undefined,
  }))
)

export function usersReducer(
  state: UsersState | undefined,
  action: Action
): UsersState {
  return reducer(state, action)
}
