<div class="t-flex t-justify-between t-items-center t-w-full">
  <div
    class="t-flex t-flex-col md:t-flex-row t-items-start md:t-items-center t-gap-0 md:t-gap-6">
    <h3
      class="t-text-lg t-font-semibold t-text-primary t-max-h-[36px] t-overflow-visible t-z-10">
      {{ title }}
    </h3>
    @if(dashboardType === ECADashboardType.Inappropriate_Words) {
    <div
      class="t-flex t-items-center t-cursor-pointer t-text-secondary"
      (click)="openFocusedSection()">
      <span class="t-text-xs t-font-medium">View Details</span>
      <button
        kendoButton
        [svgIcon]="svgOpenNew"
        title="Open in new tab"
        fillMode="flat"
        class="t-text-secondary"></button>
    </div>
    }
  </div>

  <div class="t-flex t-space-x-2 t-items-center">
    <button
      kendoButton
      title="Download"
      fillMode="flat"
      (click)="downloadCSV()">
      <span
        venioSvgLoader
        svgUrl="assets/svg/icon-preview-download.svg"
        width="1rem"
        height="1rem"></span>
    </button>
  </div>
</div>
