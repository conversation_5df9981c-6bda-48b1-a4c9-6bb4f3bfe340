import { InjectionToken, PLATFORM_ID, inject } from '@angular/core'
import { isPlatformBrowser } from '@angular/common'

// PlotlyJS injection token
export const PLOTLY_JS = new InjectionToken<unknown>('PLOTLY_JS')

// Single reusable function to create PlotlyJS mock - DRY principle
function createPlotlyMock(): unknown {
  return {
    version: '2.35.0',
    plot: (): Promise<void> => Promise.resolve(),
    newPlot: (): Promise<void> => Promise.resolve(),
    restyle: (): Promise<void> => Promise.resolve(),
    relayout: (): Promise<void> => Promise.resolve(),
    redraw: (): Promise<void> => Promise.resolve(),
    purge: (): Promise<void> => Promise.resolve(),
    react: (): Promise<void> => Promise.resolve(),
    animate: (): Promise<void> => Promise.resolve(),
    addTraces: (): Promise<void> => Promise.resolve(),
    deleteTraces: (): Promise<void> => Promise.resolve(),
    moveTraces: (): Promise<void> => Promise.resolve(),
    extendTraces: (): Promise<void> => Promise.resolve(),
    prependTraces: (): Promise<void> => Promise.resolve(),
    update: (): Promise<void> => Promise.resolve(),
    downloadImage: (): Promise<void> => Promise.resolve(),
    toImage: (): Promise<void> => Promise.resolve(),
    validate: (): Promise<void> => Promise.resolve(),
    on: (): void => {
      // Mock implementation
    },
    removeListener: (): void => {
      // Mock implementation
    },
    removeAllListeners: (): void => {
      // Mock implementation
    },
    Plots: {
      resize: (): Promise<void> => Promise.resolve(),
    },
    d3: {},
  }
}

// Single reusable function to load real PlotlyJS - DRY principle
function loadRealPlotlyJS(): Promise<unknown> {
  return import('plotly.js-dist-min')
    .then((plotly) => plotly.default || plotly)
    .catch((error: unknown) => {
      // eslint-disable-next-line no-console
      console.warn('Failed to load PlotlyJS:', error)
      return createPlotlyMock()
    })
}

// Create a PlotlyJS proxy that loads the real library in browser
export const PlotlyJS: unknown = createPlotlyMock()

// In browser environment, replace with real PlotlyJS
if (typeof window !== 'undefined') {
  loadRealPlotlyJS().then((realPlotly) => {
    // Replace all methods with real ones
    Object.assign(PlotlyJS as Record<string, unknown>, realPlotly)
  })
}

// Factory function to provide PlotlyJS conditionally
export function plotlyJsFactory(): Promise<unknown> {
  const platformId = inject(PLATFORM_ID)

  if (isPlatformBrowser(platformId)) {
    // Browser environment - use reusable function
    return loadRealPlotlyJS()
  }
  // SSR environment - return mock
  return Promise.resolve(createPlotlyMock())
}
