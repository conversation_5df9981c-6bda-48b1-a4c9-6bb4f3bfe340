<div class="t-flex t-flex-col t-gap-2 t-h-full t-justify-center">
  <ng-container *ngFor="let legend of legends(); let i = index">
    <div
      class="t-flex t-items-center t-flex-row t-mb-1 t-cursor-pointer t-hover:bg-gray-50 t-p-1 t-rounded"
      (click)="onLegendClick(legend, i)"
      title="Click to drill down">
      <div
        class="t-h-3 t-w-3 t-rounded-sm t-mr-2"
        [ngStyle]="{ background: chartColors()[i + 1] }"></div>
      <p class="t-font-medium t-text-xs">
        {{ legend }}
      </p>
    </div>
  </ng-container>
</div>
