import { ComponentFixture, TestBed } from '@angular/core/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { AiFacade } from '@venio/data-access/ai'
import { BehaviorSubject } from 'rxjs'

import { DataTableForFocusedSectionComponent } from './data-table-for-focused-section.component'

describe('DataTableForFocusedSectionComponent', () => {
  let component: DataTableForFocusedSectionComponent
  let fixture: ComponentFixture<DataTableForFocusedSectionComponent>

  const mockAiFacade = {
    selectActiveChartType$: new BehaviorSubject(null),
    selectEciTableData$: new BehaviorSubject([]),
    selectEciIsParentData$: new BehaviorSubject(true),
    selectEciDocumentTypes$: new BehaviorSubject([]),
    selectChartTableData$: jest.fn().mockReturnValue(new BehaviorSubject([])),
  } satisfies Partial<AiFacade>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DataTableForFocusedSectionComponent],
      providers: [
        provideMockStore({}),
        { provide: AiFacade, useValue: mockAiFacade },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DataTableForFocusedSectionComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
