/**
  Reference: https://github.com/telerik/kendo-themes/tree/develop/packages/fluent
 */

@import 'variables';

// Define your custom colors
$white-color: #ffffff;
$black-color: #000000;
$custom-secondary-100: var(--kendo-custom-secondary-100);
$custom-secondary-90: var(--kendo-custom-secondary-90);
$custom-secondary-70: var(--kendo-custom-secondary-70);
$custom-secondary-110: var(--kendo-custom-secondary-110);

@mixin k-button-outline-secondary-styles {
  --kendo-button-text: #{$custom-secondary-100} !important;
  --kendo-button-bg: transparent !important;
  --kendo-button-border: #{$custom-secondary-100} !important;

  --kendo-button-hover-text: #{$white-color} !important;
  --kendo-button-hover-bg: #{$custom-secondary-100} !important;
  --kendo-button-hover-border: #{$custom-secondary-100} !important;

  --kendo-button-focus-text: #{$white-color} !important;
  --kendo-button-focus-bg: #{$custom-secondary-100} !important;
  --kendo-button-focus-border: #{$custom-secondary-100} !important;

  --kendo-button-focus-hover-text: #{$white-color} !important;
  --kendo-button-focus-hover-bg: #{$custom-secondary-100} !important;
  --kendo-button-focus-hover-border: #{$custom-secondary-100} !important;

  --kendo-button-active-text: #{$white-color} !important;
  --kendo-button-active-bg: #{$custom-secondary-100} !important;
  --kendo-button-active-border: #{$custom-secondary-100} !important;

  --kendo-button-active-hover-text: #{$white-color};
  --kendo-button-active-hover-bg: #{$custom-secondary-100} !important;
  --kendo-button-active-hover-border: #{$custom-secondary-100} !important;

  --kendo-button-selected-bg: #{$custom-secondary-100} !important;
}

// Custom mixin for Kendo buttons
@mixin custom-kendo-button--theme() {
  .k-button {
    text-transform: capitalize;
    // Common styles
    letter-spacing: 0.034rem;
    font-weight: normal;
    --kendo-button-focus-bg: transparent !important;

    // Custom solid secondary button

    &.k-button-solid-secondary {
      --kendo-button-text: #{$white-color};
      --kendo-button-bg: #{$custom-secondary-100};
      --kendo-button-border: #{$custom-secondary-100};

      --kendo-button-hover-text: #{$white-color};
      --kendo-button-hover-bg: #{$custom-secondary-70} !important;
      --kendo-button-hover-border: #{$custom-secondary-70} !important;

      --kendo-button-focus-text: #{$white-color};
      --kendo-button-focus-border: #{$custom-secondary-100} !important;

      --kendo-button-focus-hover-text: #{$white-color};
      --kendo-button-focus-hover-bg: #{$custom-secondary-100} !important;
      --kendo-button-focus-hover-border: #{$custom-secondary-100} !important;

      --kendo-button-active-text: #{$white-color};
      --kendo-button-active-bg: #{$custom-secondary-100} !important;
      --kendo-button-active-border: #{$custom-secondary-100} !important;

      --kendo-button-active-hover-text: #{$white-color};
      --kendo-button-active-hover-bg: #{$custom-secondary-100} !important;
      --kendo-button-active-hover-border: #{$custom-secondary-100} !important;
    }

    // Custom outline secondary button

    &.k-button-outline-secondary {
      @include k-button-outline-secondary-styles;
    }

    &.k-button-outline-success {
      &.k-selected {
        background: #{$custom-secondary-100} !important;

        &:hover {
          @apply t-text-white #{!important};
        }
      }

      &:hover {
        color: #{$custom-secondary-100} !important;
      }
    }

    // remove the default border
    &:focus {
      --kendo-button-focus-outline: transparent !important;
    }

    // Revamp 2.0
    &.k-button-outline {
      @apply t-border-[#BEBEBE] t-rounded-[4px];
      @apply t-bg-white t-capitalize t-min-h-[33.6px] t-min-w-[33.6px] #{!important};

      &:hover {
        @apply t-text-[#212121];
      }
    }
  }
}

// Include the custom mixin
@include custom-kendo-button--theme();

// for radio button groups
@layer {
  .k-button-group {
    .k-button {
      // border-color: $icon-grey-color !important;
      @apply t-capitalize #{!important};
    }
  }

  .k-button-group {
    &.v-custom-button-group {
      border: 1px solid #263238;
      border-radius: 8px;

      .k-button {
        border: 1px solid #263238;
      }
    }
  }

  .k-button {
    @apply t-rounded-[4px];
    @apply t-capitalize #{!important};

    // for info colors default styles

    &.v-custom-show-title-disabled-btn {
      &:disabled {
        @apply t-pointer-events-auto #{!important};
      }

      &.k-disabled {
        @apply t-pointer-events-auto #{!important};

        // for showing border on a disabled kendo button
        border: 1px solid var(--kendo-button-border) !important;
      }
    }

    // for global close icon button
    &.v-dialog-close {
      background: var(--tb-kendo-error-100) !important;
      color: var(--tb-kendo-body-bg) !important;
      @apply t-p-1;

      .k-button-text {
        @apply t-grid #{!important};
      }
    }

    // for disable button by reducting opacity #{$white-color}
    &.k-disabled {
      @apply t-opacity-30 #{!important};
    }

    &.v-custom-ai-search-btn {
      &.k-disabled {
        @apply t-bg-white t-grayscale #{!important};
      }
    }

    &.v-custom-secondary-button {
      &.k-button-outline-secondary {
        @apply t-border-[var(--kendo-custom-secondary-100)] #{!important};

        .k-button-text {
          @apply t-uppercase;
        }

        &.k-focus {
          .k-button-text {
            @apply t-text-[var(--kendo-custom-secondary-100)];
          }
        }

        &:hover {
          @apply t-bg-[var(--kendo-custom-secondary-100)] t-text-[#FFFFFF] #{!important};

          .k-button-text {
            @apply t-text-[#FFFFFF] #{!important};
          }
        }
      }
    }

    &.k-button-outline-info {
      @apply t-border-[var(--kendo-info-100)] #{!important};

      .k-button-text {
        @apply t-text-[var(--kendo-info-100)] t-uppercase;
      }

      &:hover {
        @apply t-bg-[var(--kendo-info-100)] t-text-[#FFFFFF] #{!important};

        .k-button-text {
          @apply t-text-[#FFFFFF];
        }
      }
    }

    &.k-button-outline-dark {
      @apply t-border-[var(--v-custom-primary-dark-color)] #{!important};

      .k-button-text {
        @apply t-uppercase;
      }

      &:hover {
        @apply t-bg-[var(--v-custom-primary-dark-color)] t-text-[#FFFFFF] #{!important};

        .k-button-text {
          @apply t-text-[#FFFFFF];
        }
      }
    }
    &.k-button-outline-primary {
      @apply t-border-[var(--v-custom-sky-blue-2)] #{!important};

      .k-button-text {
        @apply t-text-[var(--v-custom-sky-blue-2)] t-uppercase #{!important};
      }

      &:hover {
        @apply t-border-[var(--v-custom-sky-blue-2)] t-bg-[var(--v-custom-sky-blue-2)] t-text-[#FFFFFF] #{!important};

        .k-button-text {
          @apply t-text-[#FFFFFF] #{!important};
        }
      }
    }

    &.k-button-outline-error {
      @apply t-border-[var(--v-custom-primary-error-color)] #{!important};

      .k-button-text {
        @apply t-text-[var(--v-custom-primary-error-color)] t-uppercase;
      }

      &:hover {
        @apply t-bg-[var(--v-custom-primary-error-color)] t-text-[#FFFFFF] #{!important};

        .k-button-text {
          @apply t-text-[#FFFFFF];
        }
      }
    }

    &.v-heart-selected-btn {
      span {
        animation: beat cubic-bezier(0.04, 0.4, 0.5, 0.95) 450ms forwards 1;
      }

      &:hover {
        @apply t-bg-white #{!important};
      }
    }

    @keyframes beat {
      30% {
        opacity: 1;
        transform: scale(1.4);
      }

      50% {
        transform: scale(0.8);
      }

      100% {
        transform: scale(1);
      }
    }

    &.v-custom-caselaunchpad-directexport {
      &.v-custom-btn-group:hover {
        &:not(:disabled) .invert-button {
          filter: invert(1);
        }
      }
    }

    &:not(:disabled) .v-invert-btn-color {
      :hover {
        filter: brightness(0) invert(1) !important;
      }
    }
  }

  // for custom button inside grid filter menu
  .k-filter-menu-container {
    .k-button {
      @apply t-max-w-[44px] t-relative;

      &:focus {
        outline: 0 !important;
        color: transparent;
      }

      &:hover {
        outline: 0 !important;
        color: transparent;
      }
    }

    .k-actions {
      @apply t-justify-end t-flex-row #{!important};

      .k-button-solid-primary {
        @apply t-text-[#ffffff] t-border-0 t-px-0 #{!important};

        &:hover {
          color: transparent !important;
          background-color: transparent !important;
        }

        &::after {
          content: ' ';
          background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='30' height='23' viewBox='0 0 30 23'%3E%3Cg id='Group_495' data-name='Group 495' transform='translate(-952.333 -530.033)'%3E%3Crect id='Rectangle_376' data-name='Rectangle 376' width='30' height='23' rx='4' transform='translate(952.333 530.033)' fill='rgba(186,227,110,0.24)'/%3E%3Cpath id='Icon_material-check' data-name='Icon material-check' d='M8.789,15.338,6.048,12.6l-.933.927L8.789,17.2l7.886-7.886-.927-.927Z' transform='translate(956.416 528.759)' fill='%2388b13f'/%3E%3C/g%3E%3C/svg%3E");

          position: absolute;
          left: 0;
          width: 40px;
          height: 30px;
          background-size: contain;
          background-repeat: no-repeat;
          display: inline-block;
          cursor: pointer;
        }

        &[disabled] {
          &::after {
            background-color: rgba(255, 255, 255, 0.5);
            filter: grayscale(100%);
            pointer-events: none;
            cursor: none;
          }
        }
      }

      .k-button-solid-primary {
        background-color: transparent !important;
      }

      .k-button-solid-base {
        @apply t-border-0 t-text-[#ffffff] t-px-0 #{!important};

        &:hover {
          color: #ffefed !important;
        }
        &:focus {
          outline: 0 !important;
          color: transparent !important;
        }

        &::after {
          position: absolute;
          left: 0;
          content: ' ';
          background: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='30.667' height='23' viewBox='0 0 30.667 23'><g id='Group_494' data-name='Group 494' transform='translate(-952 -530)'><rect id='Rectangle_376' data-name='Rectangle 376' width='30.667' height='23' rx='4' transform='translate(952 530)' fill='rgba(255,95,82,0.1)'/><path id='Icon_material-close' data-name='Icon material-close' d='M16.325,8.389,15.437,7.5l-3.524,3.524L8.389,7.5,7.5,8.389l3.524,3.524L7.5,15.437l.889.889L11.913,12.8l3.524,3.524.889-.889L12.8,11.913Z' transform='translate(955.421 529.587)' fill='%23ec3737'/></g></svg>");

          cursor: pointer;
          width: 40px;
          height: 30px;
          background-size: contain;
          background-repeat: no-repeat;
          display: inline-block;
        }
      }
    }
  }

  // for custom button inside kendo date range picker
  kendo-daterange {
    .k-button {
      @apply t-rounded-[4px] t-border-[#BEBEBE] t-rounded-[4px] t-shadow-none t-capitalize #{!important};
    }
  }

  kendo-dropdownbutton {
    @apply t-h-[33.5px] t-inline-block #{!important};

    .k-button {
      .k-button-text {
        @apply t-flex t-items-center t-justify-between t-gap-1 #{!important};
      }

      @apply t-capitalize t-shadow-none t-bg-white t-outline-none #{!important};
    }

    &.v-custom-filed-operator-btn {
      .k-button {
        @apply t-h-[33.5px] t-w-11 t-p-0 t-m-0 t-border-[#CCCCCC] t-border-l-[1px] t-rounded-l-none t-bg-[transparent] t-outline-0 #{!important};

        &:focus {
          @apply t-outline-0 #{!important};
        }
      }

      &.v-custom-filed-operator-btn-active {
        .k-button {
          @apply t-bg-[var(--v-custom-sky-blue)] #{!important};
        }
      }
    }

    &.v-custom-dropdown-tags-btn {
      @apply t-flex t-text-[#FFFFFF] #{!important};

      .k-button {
        @apply t-p-0 t-m-0 t-border-none t-outline-none t-bg-[transparent] #{!important};

        .k-button-text {
          &::after {
            @apply t-hidden #{!important};
          }
        }
      }
    }

    &.v-custom-dropdown-settings-btn {
      @apply t-flex t-rounded t-tracking-tight t-w-full #{!important};
      .k-button {
        @apply t-flex t-px-[8px] t-mx-0 t-items-center t-justify-between #{!important};
      }
    }

    // for custom operation dropdown buttons
    &.v-custom-operator-menu {
      .k-button {
        @apply t-p-0 t-m-0 t-border-0 t-bg-[transparent] t-outline-0 #{!important};

        &:focus {
          @apply t-outline-0 #{!important};
        }
      }
    }

    &.v-custom-dropdown-tags-btn {
      @apply t-flex t-text-[#ffffff] #{!important};

      .k-button {
        @apply t-p-0 t-m-0 t-border-none t-outline-none t-bg-[transparent] #{!important};

        .k-button-text {
          &::after {
            @apply t-hidden #{!important};
          }
        }
      }
    }

    &.v-custom-dropdown-case-btn {
      &:hover {
        .k-button {
          @apply t-bg-[#1EBADC] t-border-[#1EBADC] #{!important};

          .k-button-text {
            &::after {
              @apply t-text-[#ffffff] #{!important};
            }
          }
        }
      }

      .k-button {
        border-radius: 0 !important;
        border-left: 0 !important;

        @apply t-w-full t-h-full #{!important};

        .k-image {
          @apply t-scale-110 -t-translate-x-[4px] t-translate-y-px #{!important};
        }

        .k-button-text {
          &::after {
            content: '>';
            transform: rotate(90deg) scaleY(1.75);
            font-weight: 600;
            color: #979797;
            padding: 0;
            left: 18px;
            top: 9px;
            // display: grid;
            // place-content: center;
            position: absolute;
            align-content: end;
            top: 9px;
            left: 18px;
            margin-left: 2px;
            margin-right: 2px;
            margin-top: 1px;
            margin-bottom: 1px;
            font-size: 5px;
            font-family: 'Courier New', Courier, monospace;
          }
        }
      }

      &:hover {
        @apply t-text-[var(--v-custom-primary-error-color)] #{!important};
      }

      &.k-focus {
        @apply t-text-[var(--kendo-custom-secondary-100)] #{!important};
      }
    }

    &.v-custom-dropdown-case-btn2 {
      .k-button {
        border-radius: 2px !important;
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
      }

      &:hover {
        .k-button {
          @apply t-bg-[#FFBB12] t-border-[#FFBB12] #{!important};
        }
      }
    }

    &.v-custom-dropdown-caselaunchpad-report {
      &:not(:disabled) .k-button {
        &:hover {
          @apply t-bg-[#1EBADC] t-border-[#1EBADC] #{!important};
        }
      }
      &.v-custom-btn-group:hover {
        &:not(:disabled) .invert-button {
          filter: brightness(0) invert(1) !important;
        }
      }
    }

    &.v-custom-dropdownbtn-invert {
      &:not(:disabled) .k-button {
        &:hover {
          @apply t-bg-[#1EBADC] t-border-[#1EBADC] #{!important};
        }
      }
      &.v-custom-btn-group:hover {
        &:not(:disabled) .invert-button {
          filter: brightness(0) invert(1) !important;
        }
      }
    }

    &.v-custom-clear-dropdown-btn {
      .k-button {
        background-color: transparent !important;
        @apply t-border-0 #{!important};

        &:hover {
          background-color: transparent !important;
          @apply t-border-0 #{!important};
        }
      }
    }
  }

  kendo-multiselect {
    @apply t-rounded-[4px] t-shadow-none #{!important};

    &.v-custom-multiselect-auto-w {
      .k-input-values {
        @apply t-flex-nowrap #{!important};

        .k-input-inner {
          @apply t-min-w-[2.2rem];
        }
      }
    }
  }
}
