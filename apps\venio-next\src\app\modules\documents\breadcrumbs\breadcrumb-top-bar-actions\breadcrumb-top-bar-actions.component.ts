import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnDestroy,
  OnInit,
  signal,
  Type,
  ViewChild,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { chevronDownIcon, searchIcon } from '@progress/kendo-svg-icons'
import {
  ButtonsModule,
  DropDownButtonComponent,
  DropDownButtonModule,
} from '@progress/kendo-angular-buttons'
import { LabelModule } from '@progress/kendo-angular-label'
import { SwitchModule } from '@progress/kendo-angular-inputs'
import { SkeletonModule } from '@progress/kendo-angular-indicators'

import {
  GroupStackType,
  SearchTriggerSource,
} from '@venio/shared/models/interfaces'
import { SVGIconModule } from '@progress/kendo-angular-icons'
import { DialogRef, DialogService } from '@progress/kendo-angular-dialog'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  Subject,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs'
import { SearchFacade } from '@venio/data-access/review'
import { FormsModule } from '@angular/forms'

@Component({
  selector: 'venio-breadcrumb-top-bar-actions',
  standalone: true,
  imports: [
    CommonModule,
    DropDownButtonModule,
    SVGIconModule,
    ButtonsModule,
    LabelModule,
    SwitchModule,
    SkeletonModule,
    FormsModule,
  ],
  templateUrl: './breadcrumb-top-bar-actions.component.html',
  styleUrl: './breadcrumb-top-bar-actions.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BreadcrumbTopBarActionsComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  @ViewChild('conditionDropdown', { read: DropDownButtonComponent })
  private conditionDropdown: DropDownButtonComponent

  private readonly viewContainerRef = inject(ViewContainerRef)

  private readonly dialogService = inject(DialogService)

  private readonly breadcrumbFacade = inject(BreadcrumbFacade)

  private searchFacade = inject(SearchFacade)

  protected readonly searchIconSvg = searchIcon

  protected readonly chevronDown = chevronDownIcon

  private dialogRef: DialogRef

  protected readonly breadcrumbConditionDropdown = [
    GroupStackType.FIELDS,
    GroupStackType.TAGS,
    GroupStackType.FOLDERS,
  ]

  public get dropdownButtonWidth(): number {
    // 70 is that the items are having paddings, so we need to subtract it
    return this.conditionDropdown?.button.nativeElement.clientWidth - 70
  }

  private breadcrumbConditionDialogContainer: Type<unknown>

  public completeBreadcrumbSyntax = signal<string>('')

  private breadcrumbStackCount = signal<number>(0)

  public shouldAutoRunSearch = signal<boolean>(false)

  public ngOnInit(): void {
    this.#selectShouldAutoRunSearch()
    this.#selectBreadcrumbCompleteSyntax()
    this.#initLazyLoadedComponents()
    this.#performAutoRunSearch()
    this.#handleBreadcrumbBeingEmpty()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public conditionItemClick(item: GroupStackType): void {
    this.#launchConditionDialog(item)
    this.#performDialogDisposalCleanup()
  }

  public performBreadcrumbSearch(): void {
    if (!this.completeBreadcrumbSyntax()) return
    this.searchFacade.search({
      searchExpression: this.completeBreadcrumbSyntax(),
      isResetBaseGuid: true,
    })

    // TODO: perform a search including the search related parameter
  }

  public toggleAutoRunSearch(shouldAutoRun: boolean): void {
    this.breadcrumbFacade.toggleAutoRunSearch(shouldAutoRun)
  }

  #selectShouldAutoRunSearch(): void {
    this.breadcrumbFacade.selectShouldAutoRunSearch$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((shouldAutoRun) => {
        this.shouldAutoRunSearch.set(shouldAutoRun)
      })
  }

  #launchConditionDialog(item: GroupStackType): void {
    this.dialogRef = this.dialogService.open({
      content: this.breadcrumbConditionDialogContainer,
      appendTo: this.viewContainerRef,
      width: 'calc(100vw - 20rem)',
      height: 'calc(100vh - 4rem)',
      title: 'Conditions',
    })

    this.dialogRef.content.setInput('groupStackType', item)
  }

  #performDialogDisposalCleanup(): void {
    this.dialogRef.dialog.onDestroy(() => {
      // TODO: perform a cleanup operation
    })
  }

  #initLazyLoadedComponents(): void {
    import(
      '../breadcrumb-conditions-dialog-container/breadcrumb-conditions-dialog-container.component'
    ).then(
      (m) =>
        (this.breadcrumbConditionDialogContainer =
          m.BreadcrumbConditionsDialogContainerComponent)
    )
  }

  #selectBreadcrumbCompleteSyntax(): void {
    this.breadcrumbFacade.selectCompleteBreadcrumbSyntax$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((syntax) => this.completeBreadcrumbSyntax.set(syntax))
  }

  #performBreadcrumbSearch(completeSyntax: string): void {
    this.searchFacade.search({
      // Either we can use the complete syntax or we can use the default one
      searchExpression: completeSyntax?.trim() || 'FileId>0',
      isResetBaseGuid: true,
    })
    this.breadcrumbFacade.resetBreadcrumbState('autoSearchControllerFlag')
  }

  /**
   * Trigger the search when we have no breadcrumb though 'Auto Run' switch is not enabled.
   */
  #handleBreadcrumbBeingEmpty(): void {
    this.breadcrumbFacade.selectBreadcrumbStack$
      .pipe(
        tap((breadcrumbstacks) =>
          this.breadcrumbStackCount.set(breadcrumbstacks?.length ?? 0)
        ),
        filter(
          (breadcrumbs) =>
            typeof breadcrumbs !== 'undefined' && breadcrumbs?.length === 0
        ),
        switchMap(() => this.breadcrumbFacade.selectCompleteBreadcrumbSyntax$),
        filter((syntax) => syntax === ''),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.#performBreadcrumbSearch(this.completeBreadcrumbSyntax())
      })
  }

  #performAutoRunSearch(): void {
    this.breadcrumbFacade.selectCompleteBreadcrumbSyntax$
      .pipe(
        filter((syntax) => typeof syntax !== 'undefined' && syntax !== ''),
        tap((syntax) => {
          this.completeBreadcrumbSyntax.set(syntax)
        }),
        distinctUntilChanged(),
        switchMap(() =>
          this.breadcrumbFacade.selectAutoSearchControllerFlag$.pipe(
            /**
             * debounceTime added to wait for this.breadcrumbStackCount() value is being updated.
             */
            debounceTime(100),
            filter(
              (autoSearchFlag) =>
                autoSearchFlag && this.breadcrumbStackCount() > 0
            ),
            switchMap(() =>
              this.breadcrumbFacade.selectShouldAutoRunSearch$.pipe(
                filter((autoRun) => autoRun)
              )
            ),
            switchMap(() =>
              this.breadcrumbFacade.selectLastSearchTriggerSource$.pipe(
                filter((source) => {
                  return source !== SearchTriggerSource.Manual
                })
              )
            )
          )
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.#performBreadcrumbSearch(this.completeBreadcrumbSyntax())
      })
  }
}
