import { ComponentFixture, TestBed } from '@angular/core/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { AiFacade } from '@venio/data-access/ai'
import { BehaviorSubject } from 'rxjs'

import { FocusedSectionComponent } from './focused-section.component'
import { Component, Input, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'

// Mock plotly-plot component
@Component({
  selector: 'venio-plotly-plot',
  template: '<div>Mock Plotly Chart</div>',
  standalone: true,
})
class MockPlotlyComponent {
  @Input() public data: any

  @Input() public layout: any

  @Input() public config: any
}

describe('FocusedSectionComponent', () => {
  let component: FocusedSectionComponent
  let fixture: ComponentFixture<FocusedSectionComponent>

  const mockAiFacade = {
    selectEciShowDetails$: new BehaviorSubject(false),
    selectActiveChartType$: new BehaviorSubject(null),
    selectEcaDocumentTypesSuccess$: new BehaviorSubject(null),
    selectEcaTopicsRelevantSuccess$: new BehaviorSubject(null),
    selectEcaTopicsNonRelevantSuccess$: new BehaviorSubject(null),
    selectEciTableData$: new BehaviorSubject([]),
    selectEciFileData$: new BehaviorSubject([]),
    selectChartCurrentData$: jest.fn().mockReturnValue(new BehaviorSubject([])),
    selectChartIsExpanded$: jest
      .fn()
      .mockReturnValue(new BehaviorSubject(false)),
    selectChartSelectedNode$: jest
      .fn()
      .mockReturnValue(new BehaviorSubject(null)),
    selectChartCurrentLevel$: jest.fn().mockReturnValue(new BehaviorSubject(0)),
    selectChartCanGoBack$: jest
      .fn()
      .mockReturnValue(new BehaviorSubject(false)),
    drillDownToNextLevel: jest.fn(),
    drillBackToPreviousLevel: jest.fn(),
    updateChartTableData: jest.fn(),
    setChartSelectedNode: jest.fn(),
  } satisfies Partial<AiFacade>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FocusedSectionComponent, MockPlotlyComponent],
      providers: [
        provideMockStore({}),
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: AiFacade, useValue: mockAiFacade },
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
      .overrideComponent(FocusedSectionComponent, {
        set: {
          imports: [MockPlotlyComponent],
          template: `
            <div class="t-bg-white t-flex t-gap-6 t-px-7 t-py-4 t-rounded t-flex-col t-mb-6 t-border t-border-[#DCDCDC]">
              <div class="t-w-full">
                <div class="t-w-full t-grid t-grid-cols-1 lg:t-grid-cols-2 t-gap-5 t-items-center">
                  <div class="t-col-span-1">
                    <div>Mock Sunburst Chart</div>
                  </div>
                  <div class="t-col-span-1">
                    <div>Mock Data Table</div>
                  </div>
                  <div class="t-w-full t-col-span-2">
                    <div>Mock List of Files</div>
                  </div>
                </div>
              </div>
            </div>
          `,
        },
      })
      .compileComponents()

    fixture = TestBed.createComponent(FocusedSectionComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
