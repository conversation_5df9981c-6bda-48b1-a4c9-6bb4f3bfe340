<div class="t-bg-white t-rounded-lg t-border t-border-[#dcdcdc] t-p-6 t-mb-6">
  <div class="t-flex t-justify-between t-items-center">
    <div class="t-flex t-gap-12">
      <div class="t-flex t-items-center t-gap-4">
        <span
          venioSvgLoader
          svgUrl="assets/svg/icon-total-documents.svg"
          width="2rem"
          height="2rem"
          color="#6366f1"></span>
        <div class="t-flex t-flex-col">
          <div class="t-text-sm">Total Documents</div>
          <h2 class="t-text-3xl t-font-bold t-text-primary t-m-0">
            {{ computedTotalDocuments() | number }}
          </h2>
        </div>
      </div>
      <div class="t-flex t-items-center t-gap-4">
        <span
          venioSvgLoader
          svgUrl="assets/svg/icon-selected-documents.svg"
          width="2rem"
          height="2rem"
          color="#10b981"></span>
        <div class="t-flex t-flex-col">
          <div class="t-text-sm">Selected Documents</div>
          <h2 class="t-text-3xl t-font-bold t-text-primary t-m-0">
            {{ selectedDocuments() | number }}
          </h2>
        </div>
      </div>
    </div>

    <div class="t-flex t-gap-4">
      <button
        kendoButton
        size="large"
        (click)="onCaseButtonClick()"
        themeColor="secondary"
        fillMode="outline"
        class="t-px-6 t-py-2">
        Case Info
      </button>
      <button
        kendoButton
        size="large"
        (click)="onNarrativeButtonClick()"
        themeColor="dark"
        fillMode="outline"
        class="t-px-6 t-py-2">
        Narrative
      </button>
    </div>
  </div>
</div>
