// Mock PlotlyJS for testing
export const mockPlotly = {
  newPlot: jest.fn().mockResolvedValue({}),
  react: jest.fn().mockResolvedValue({}),
  restyle: jest.fn().mockResolvedValue({}),
  relayout: jest.fn().mockResolvedValue({}),
  redraw: jest.fn().mockResolvedValue({}),
  purge: jest.fn().mockResolvedValue({}),
  downloadImage: jest.fn().mockResolvedValue({}),
  toImage: jest.fn().mockResolvedValue({}),
  validate: jest.fn().mockReturnValue(true),
  Plots: {
    resize: jest.fn(),
  },
}

// Mock PlotlyModule
export const MockPlotlyModule = {
  forRoot: (): { ngModule: any; providers: any[] } => ({
    ngModule: class MockPlotlyModuleClass {},
    providers: [{ provide: 'plotly', useValue: mockPlotly }],
  }),
}

// Setup function to configure PlotlyJS mocks
export function setupPlotlyMocks(): void {
  // Set up global Plotly object
  ;(global as any).Plotly = mockPlotly
  ;(global as any).window = global.window || {}
  ;(global as any).window.Plotly = mockPlotly

  // Try to configure PlotlyModule if it exists
  try {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const PlotlyModule = require('angular-plotly.js').PlotlyModule
    Object.defineProperty(PlotlyModule, 'plotlyjs', {
      value: mockPlotly,
      writable: true,
    })
  } catch (e) {
    // PlotlyModule not available, ignore
  }
}
