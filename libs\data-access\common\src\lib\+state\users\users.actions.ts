import { createAction, props } from '@ngrx/store'
import {
  InviteUserModel,
  ResponseModel,
  UsersListModel,
} from '@venio/shared/models/interfaces'
import { UsersState } from './users.reducer'
import { UserMessageModel } from '@venio/data-access/review'

export enum UserActionTypes {
  // Actions for fetch a user list
  FetchUserList = '[Users] Fetch User List',
  FetchUserListSuccess = '[Users] Fetch User List: Success',
  FetchUserListFailure = '[Users] Fetch User List: Failure',

  // Actions for fetch current user
  FetchCurrentUser = '[Users] Fetch Current User',
  FetchCurrentUserSuccess = '[Users] Fetch Current User: Success',
  FetchCurrentUserFailure = '[Users] Fetch Current User: Failure',

  // Resetting Users State
  ResetUserState = '[Users] Reset State',

  // Actions for fetch a user list
  FetchUserListToInvite = '[Users] Fetch User List To Invite',
  FetchUserListToInviteSuccess = '[Users] Fetch User List  To Invite: Success',
  FetchUserListToInviteFailure = '[Users] Fetch User List  To Invite: Failure',

  // Actions for fetch external user list
  FetchExternalUserListToInvite = '[Users] Fetch External User List To Invite',
  FetchExternalUserListToInviteSuccess = '[Users] Fetch External User List  To Invite: Success',
  FetchExternalUserListToInviteFailure = '[Users] Fetch External User List  To Invite: Failure',

  SendInvitation = '[User] Send invitation',
  SetSendInvitationResponseMessage = '[User] Set Send Invitation Response Message',

  ClearInternalUserFetchError = '[Users] Clear Internal User Fetch Error',
  ClearExternalUserFetchError = '[Users] Clear External User Fetch Error',
}

export const resetUserState = createAction(
  UserActionTypes.ResetUserState,
  props<{
    stateKey: keyof UsersState | Array<keyof UsersState>
  }>()
)

export const fetchUserList = createAction(UserActionTypes.FetchUserList)

export const fetchUserListSuccess = createAction(
  UserActionTypes.FetchUserListSuccess,
  props<{ userListSuccessResponse: ResponseModel }>()
)

export const fetchUserListFailure = createAction(
  UserActionTypes.FetchUserListFailure,
  props<{ userListErrorResponse: ResponseModel }>()
)

export const fetchCurrentUser = createAction(UserActionTypes.FetchCurrentUser)

export const fetchCurrentUserSuccess = createAction(
  UserActionTypes.FetchCurrentUserSuccess,
  props<{ currentUserSuccessResponse: ResponseModel }>()
)

export const fetchCurrentUserFailure = createAction(
  UserActionTypes.FetchCurrentUserFailure,
  props<{ currentUserErrorResponse: ResponseModel }>()
)

export const fetchUserListToInvite = createAction(
  UserActionTypes.FetchUserListToInvite,
  props<{ projectId: number }>()
)

export const fetchUserListToInviteSuccess = createAction(
  UserActionTypes.FetchUserListToInviteSuccess,
  props<{ userListToInviteSuccessResponse: UsersListModel[] }>()
)

export const fetchUserListToInviteFailure = createAction(
  UserActionTypes.FetchUserListToInviteFailure,
  props<{ userListToInviteErrorResponse: ResponseModel }>()
)

export const fetchExternalUserListToInvite = createAction(
  UserActionTypes.FetchExternalUserListToInvite
)

export const fetchExternalUserListToInviteSuccess = createAction(
  UserActionTypes.FetchExternalUserListToInviteSuccess,
  props<{ externalUserToInviteSuccessResponse: UsersListModel[] }>()
)

export const fetchExternalUserListToInviteFailure = createAction(
  UserActionTypes.FetchExternalUserListToInviteFailure,
  props<{ externalUserToInviteErrorResponse: ResponseModel }>()
)

export const sendInvitation = createAction(
  UserActionTypes.SendInvitation,
  props<{ payload: InviteUserModel }>()
)

export const setSendInvitationResponseMessage = createAction(
  UserActionTypes.SetSendInvitationResponseMessage,
  props<{ sendInvitationResponseMessage: UserMessageModel }>()
)

export const clearInternalUserFetchError = createAction(
  UserActionTypes.ClearInternalUserFetchError
)

export const clearExternalUserFetchError = createAction(
  UserActionTypes.ClearExternalUserFetchError
)
