import { Action, createReducer, on } from '@ngrx/store'
import * as CommandActions from './commands.actions'
import { resetStateProperty } from '@venio/util/utilities'
import { CommandEventTypes } from '@venio/shared/models/constants'

export const COMMANDS_FEATURE_KEY = 'commandsStore'

export type CommandState = Partial<Record<CommandEventTypes, unknown>>

export const commandInitialState: CommandState = {
  [CommandEventTypes.NotifyLaunchAdvancedSearch]: undefined,
  [CommandEventTypes.StoreFolderTabType]: 'system', // Default folder tab type
  [CommandEventTypes.TogglePanelVisibility]: undefined, // Panel visibility state
  // You can add more types in event enum and initialize them here
  // which you need to dispatch the event with the data of yours with the type.
  // The same dispatcher will update the state with the data by given type.
}

export interface CommandPartialState {
  readonly [COMMANDS_FEATURE_KEY]: CommandState
}

const reducer = createReducer<CommandState>(
  commandInitialState,
  on(CommandActions.resetCommandState, (state, { stateKey }) =>
    resetStateProperty<CommandState>(state, commandInitialState, stateKey)
  ),
  on(CommandActions.dispatchCommand, (state, { event }) => ({
    ...state,
    [event.type]: event.data,
  }))
)

export function commandsReducer(
  state: CommandState | undefined,
  action: Action
): CommandState {
  return reducer(state, action)
}
