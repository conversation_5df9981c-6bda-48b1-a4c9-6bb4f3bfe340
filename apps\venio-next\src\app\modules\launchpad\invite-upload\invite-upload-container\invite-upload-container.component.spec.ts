import { ComponentFixture, TestBed } from '@angular/core/testing'
import { InviteUploadContainerComponent } from './invite-upload-container.component'
import { signal } from '@angular/core'
import { FormBuilder } from '@angular/forms'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { NotificationService } from '@progress/kendo-angular-notification'
import { UserFacade } from '@venio/data-access/common'
import { ControlSettingService } from '@venio/data-access/control-settings'
import { of } from 'rxjs'
import { InviteUploadLocalState } from './invite-upload-local-state'

describe('InviteUploadContainerComponent', () => {
  let component: InviteUploadContainerComponent
  let fixture: ComponentFixture<InviteUploadContainerComponent>

  const mockInviteUploadLocalState = {
    shareToExternalUsers: signal(false),
    internalUsersList: signal(new Map<number, string>()),
    selectedExternalUsers: signal<string[]>([]),
    userMessage: signal(''),
    setUserMessage: jest.fn(),
  }

  const mockUserFacade = {
    selectCurrentUserDetails$: of({ userId: 1 }),
    selectIsInvitationInProgress$: of(false),
    selectSendInvitationResponseMessage$: of({ message: '', success: true }),
    selectExternalUserToInviteErrorResponse$: of(null),
    selectUserListToInviteErrorResponse$: of(null),
    sendInvitation: jest.fn(),
    resetUserState: jest.fn(),
  }

  const mockProjectId = 1

  const mockInviteUploadForm = new FormBuilder().group({
    shareToExternalUsers: {
      value: false,
      disabled: false,
    },
    instruction: '',
    newEmail: {
      value: '',
      disabled: true,
    },
    validity: 1,
  })

  const mockControlSettings = {
    getControlSetting: {
      ALLOW_EXTERNAL_USER_DATA_UPLOAD: false,
    },
  }

  const mockNotificationService = {
    close: jest.fn(),
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [InviteUploadContainerComponent],
      providers: [
        FormBuilder,
        {
          provide: DialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
        { provide: NotificationService, useValue: mockNotificationService },
        {
          provide: InviteUploadLocalState,
          useValue: mockInviteUploadLocalState,
        },
        { provide: UserFacade, useValue: mockUserFacade },
        { provide: ControlSettingService, useValue: mockControlSettings },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(InviteUploadContainerComponent)
    component = fixture.componentInstance
    fixture.componentRef.setInput('selectedProjectId', mockProjectId)
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should initialize the form with default values', () => {
    // GIVEN: Initial state of the form
    component.inviteUploadForm = mockInviteUploadForm

    // WHEN: The form is initialized
    fixture.detectChanges()
    const formValue = component.inviteUploadForm.getRawValue()

    // THEN: Verify default values
    expect(formValue).toEqual({
      shareToExternalUsers: false,
      instruction: '',
      newEmail: '',
      validity: 1,
    })
  })

  it('should initialize form with correct default values', () => {
    // GIVEN: Initial control settings and default values
    const initialControlSettings = {
      ALLOW_EXTERNAL_USER_DATA_UPLOAD: false,
    }
    mockControlSettings.getControlSetting = initialControlSettings

    // WHEN: Component is initialized
    fixture.detectChanges()

    // THEN: Verify all form controls have correct initial values and states
    const form = component.inviteUploadForm
    expect(form.get('shareToExternalUsers')?.value).toBe(false)
    expect(form.get('shareToExternalUsers')?.disabled).toBe(true)
    expect(form.get('newEmail')?.disabled).toBe(true)
    expect(form.get('instruction')?.value).toBe('')
    expect(form.get('validity')?.value).toBe(component['defaultValidity'])
  })

  it('should validate invalid email format', () => {
    // GIVEN: An invalid email format
    component.inviteUploadForm.get('newEmail')?.setValue('invalid-email')
    component.inviteUploadForm.get('newEmail')?.enable()

    // WHEN: The form control is checked for errors
    const newEmailControl = component.inviteUploadForm.get('newEmail')
    newEmailControl?.markAsTouched()
    fixture.detectChanges()

    // THEN: It should return an invalidEmail error
    expect(newEmailControl?.hasError('invalidEmail')).toBe(true)
  })

  it('should validate valid email format', () => {
    // GIVEN: An valid email format
    component.inviteUploadForm
      .get('newEmail')
      ?.setValue('<EMAIL>')
    component.inviteUploadForm.get('newEmail')?.enable()

    // WHEN: The form control is checked for errors
    const newEmailControl = component.inviteUploadForm.get('newEmail')
    newEmailControl?.markAsTouched()
    fixture.detectChanges()

    // THEN: It should not return an invalidEmail error
    expect(newEmailControl?.hasError('invalidEmail')).toBe(false)
  })

  it('should validate invalid validity value', () => {
    // GIVEN: A validity value less than 1
    component.inviteUploadForm.get('validity')?.setValue(0)

    // WHEN: The form control is checked for errors
    const validityControl = component.inviteUploadForm.get('validity')
    validityControl?.markAsTouched()
    fixture.detectChanges()

    // THEN: It should be invalid
    expect(validityControl?.valid).toBe(false)
  })

  it('should validate minimum validity value', () => {
    // GIVEN: A validity value less than 1
    component.inviteUploadForm.get('validity')?.setValue(1)

    // WHEN: The form control is checked for errors
    const validityControl = component.inviteUploadForm.get('validity')
    validityControl?.markAsTouched()
    fixture.detectChanges()

    // THEN: It should be valid
    expect(validityControl?.valid).toBe(true)
  })
})
