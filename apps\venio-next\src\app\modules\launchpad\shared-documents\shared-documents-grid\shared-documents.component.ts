import { CommonModule } from '@angular/common'
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  output,
  signal,
  TrackByFunction,
} from '@angular/core'
import {
  CellTemplateDirective,
  ColumnComponent as GridColumnComponent,
  FilterMenuTemplateDirective,
  GridComponent,
  HeaderTemplateDirective,
  NoRecordsTemplateDirective,
  SelectionDirective,
  CheckboxColumnComponent as GridCheckboxColumn,
  GridItem,
  PageChangeEvent,
  GridDataResult,
} from '@progress/kendo-angular-grid'
import { TooltipDirective } from '@progress/kendo-angular-tooltip'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { TextBoxComponent } from '@progress/kendo-angular-inputs'
import { LaunchpadAction } from '@venio/shared/models/interfaces'
import { toSignal } from '@angular/core/rxjs-interop'
import {
  CheckboxColumnComponent as TreelistCheckboxColumn,
  ColumnComponent as TreeListColumnComponent,
  FlatBindingDirective,
  SelectableDirective,
  TreeListComponent,
} from '@progress/kendo-angular-treelist'
import { SortDescriptor } from '@progress/kendo-data-query'
import { SkeletonComponent } from '@progress/kendo-angular-indicators'
import { checkIcon } from '@progress/kendo-svg-icons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { SharedDocumentGridActionsComponent } from '../shared-document-grid-actions/shared-document-grid-actions.component'
import { DomSanitizer } from '@angular/platform-browser'
import {
  DocumentShareFacade,
  SharedDocRequestType,
  SharedDocumentDetailModel,
} from '@venio/data-access/review'
import { DynamicHeightDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-shared-documents',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    GridComponent,
    SelectionDirective,
    GridColumnComponent,
    TreelistCheckboxColumn,
    HeaderTemplateDirective,
    TooltipDirective,
    FilterMenuTemplateDirective,
    CellTemplateDirective,
    TextBoxComponent,
    NoRecordsTemplateDirective,
    TreeListComponent,
    FlatBindingDirective,
    SelectableDirective,
    GridCheckboxColumn,
    TreeListColumnComponent,
    SkeletonComponent,
    SharedDocumentGridActionsComponent,
    DynamicHeightDirective,
  ],
  templateUrl: './shared-documents.component.html',
  styleUrl: './shared-documents.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SharedDocumentsComponent {
  private readonly sanitizer = inject(DomSanitizer)

  private readonly shareDocumentFacade = inject(DocumentShareFacade)

  /** Output event for the action invoked.
   * When the actions of grid e.g., produce, upload, etc. are clicked, this event is emitted with type `LaunchpadAction`
   */
  public readonly actionInvoked = output<LaunchpadAction>()

  /** An instance for grid [data] object including data and total */
  public gridView = signal<GridDataResult>(null)

  public sort: SortDescriptor[] = []

  /** This page size is only for the client side grid rendering optimization, not for serve.
   * This way, the UI is not overwhelmed with too many records at once making the grid unresponsive.
   *
   * When a user scrolls up or down, the grid will add or remove records based on the pageSize and skip
   * while the actual count of data is fetched from the server.
   */
  public readonly pageSize = computed(() => {
    return this.sharedDocCurrentPagingInfo()?.pageSize || 25
  })

  /** The number of records to skip when virtualization is enabled */
  public readonly skip = computed(() => {
    return (this.currentPage() - 1) * this.pageSize()
  })

  public readonly currentPage = computed(() => {
    return this.sharedDocCurrentPagingInfo()?.pageNumber || 1
  })

  /** Static common action types */
  public readonly commonActionTypes = CommonActionTypes

  public icons = { checkIcon: checkIcon }

  /** Signal for the Shared Documents detail loading state */
  public isSharedDocLoading = toSignal(
    this.shareDocumentFacade.selectIsSharedDocumentsLoading$,
    { initialValue: true }
  )

  private readonly shareDocList = toSignal(
    this.shareDocumentFacade.selectSharedDocumentList$
  )

  private readonly sharedDocCurrentPagingInfo = toSignal(
    this.shareDocumentFacade.selectSharedDocDetailPagingInfo$
  )

  private readonly sharedDocumentRequestInfo = toSignal(
    this.shareDocumentFacade.selectSharedDocumentRequestInfo$
  )

  private readonly loadedSharedDocs = computed<SharedDocumentDetailModel[]>(
    () => {
      const requestInfo = this.sharedDocumentRequestInfo()

      // filter based on SharedDocRequestType
      const sharedDocs = (this.shareDocList() || []).filter((doc) => {
        if (requestInfo.sharedDocRequestType === SharedDocRequestType.All) {
          return true
        }
        return doc.shareTypeEnum === requestInfo.sharedDocRequestType
      })

      // filter list based on search text
      if (requestInfo.searchText?.trim()) {
        const term = requestInfo.searchText.trim().toLowerCase()
        return sharedDocs.filter((doc) =>
          doc.shareName.toLowerCase().includes(term)
        )
      }
      return sharedDocs || []
    }
  )

  /** Signal for the total Shared Docs count */
  private readonly totalSharedDocs = computed<number>(
    () => this.shareDocList()?.length || 0
  )

  constructor() {
    // Effects are handle in the constructor.
    // if we need to handle outside the constructor, we need to use
    // injector and use it in lifecycle hooks.
    effect(
      () => {
        // If the Shared Docs detail is loading, exit early;
        if (this.isSharedDocLoading()) return

        this.loadGridData()
      },
      { allowSignalWrites: true }
    )
  }

  /** Handles the paging event for the virtual scroll to avoid loading all the data at once
   * stressing the UI performance. Instead, we load the data in chunks.
   *
   * @see loadGridData
   * @see pageSize
   * @see skip
   *
   * @param {PageChangeEvent} event - The paging event.
   * @returns {void}
   */
  public handlePagingForVirtualScroll(event: PageChangeEvent): void {
    this.loadGridData()
  }

  /** Loads the grid data based on the skip and pageSize
   * @see skip
   * @see pageSize
   * @see loadedSharedDocs
   * @returns {void}
   */
  private loadGridData(): void {
    const allSharedDocs = this.loadedSharedDocs()
    this.gridView.set({
      data: allSharedDocs.slice(this.skip(), this.skip() + this.pageSize()),
      total: this.totalSharedDocs(),
    })
  }

  /**
   * The grid data is rendered as virtual scroll, so when the user scrolls up or down,
   * the data are added or removed based on the pageSize and skip.
   *
   * To reflect actual changes in the UI, we need to track the data by the projectId.
   * @param {number} _ - The index of the item.
   * @param {GridItem} item - The grid item.
   * @returns {TrackByFunction<GridItem>} - The track by function.
   */
  public caseTrackByFn = (
    _: number,
    item: GridItem
  ): TrackByFunction<GridItem> =>
    item.data['DocumentShareID'] as TrackByFunction<GridItem>

  /**
   * When column action controls are clicked, this method is called which then
   * emits another event to the parent component with selected shared docs details and action type.
   * @param {CommonActionTypes} actionType - The action type that is clicked.
   * @param {SharedDocumentDetailModel} content - The selected shared docs details.
   * @returns {void}
   */
  public forwardActionControlClick(
    actionType: CommonActionTypes,
    content: SharedDocumentDetailModel
  ): void {
    this.actionInvoked.emit({
      actionType,
      content,
    })
  }

  /**
   * Strips HTML tags from the given HTML content and returns plain text.
   * If the input is null, it returns an empty string.
   *
   * @param {string | null} htmlContent - The HTML content to strip tags from.
   * @returns {string} - The plain text without HTML tags.
   */
  public stripHtmlTags(htmlContent: string | null): string {
    if (!htmlContent) return ''
    const div = document.createElement('div')
    div.innerHTML = htmlContent
    return div.textContent || div.innerText || ''
  }
}
