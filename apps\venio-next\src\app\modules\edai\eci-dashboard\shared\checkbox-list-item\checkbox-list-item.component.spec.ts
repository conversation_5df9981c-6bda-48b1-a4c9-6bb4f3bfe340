import { ComponentFixture, TestBed } from '@angular/core/testing'

import { CheckboxListItemComponent } from './checkbox-list-item.component'

describe('CheckboxListItemComponent', () => {
  let component: CheckboxListItemComponent
  let fixture: ComponentFixture<CheckboxListItemComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CheckboxListItemComponent],
    }).compileComponents()

    fixture = TestBed.createComponent(CheckboxListItemComponent)
    component = fixture.componentInstance

    // Set required input
    component.custodian = {
      id: 1,
      name: 'Test Custodian',
      email: '<EMAIL>',
    }

    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
