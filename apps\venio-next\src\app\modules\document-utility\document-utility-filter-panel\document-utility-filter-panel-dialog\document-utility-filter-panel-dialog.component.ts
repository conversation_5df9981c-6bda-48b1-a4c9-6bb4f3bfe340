import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  Input,
  OnDestroy,
  OnInit,
  Optional,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  SHOWHIDE_FIELDS,
  UtilityPanelType,
} from '@venio/shared/models/constants'
import { Subject, takeUntil } from 'rxjs'
import {
  ReviewPanelFacade,
  UtilityPanelFacade,
} from '@venio/data-access/document-utility'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { GridModule, SelectableSettings } from '@progress/kendo-angular-grid'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { DialogModule, DialogRef } from '@progress/kendo-angular-dialog'
import { IconsModule } from '@progress/kendo-angular-icons'
import { eyeIcon, searchIcon } from '@progress/kendo-svg-icons'
import {
  AfterValueChangedDirective,
  DynamicHeightDirective,
} from '@venio/feature/shared/directives'
import { cloneDeep, keyBy, chain } from 'lodash'
import {
  CompositeLayoutState,
  Field,
  FieldFacade,
  LayoutField,
  ReviewPanelType,
} from '@venio/data-access/review'

@Component({
  selector: 'venio-document-utility-filter-panel-dialog',
  standalone: true,
  imports: [
    CommonModule,
    DialogModule,
    GridModule,
    InputsModule,
    LabelModule,
    ButtonsModule,
    IconsModule,
    DynamicHeightDirective,
    AfterValueChangedDirective,
  ],
  templateUrl: './document-utility-filter-panel-dialog.component.html',
  styleUrl: './document-utility-filter-panel-dialog.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentUtilityFilterPanelDialogComponent
  implements OnInit, OnDestroy
{
  @Input() public panelFilterEventType: UtilityPanelType

  public reviewPanelFacade = inject(ReviewPanelFacade)

  public utilityPanelFacade = inject(UtilityPanelFacade)

  private fieldFacade = inject(FieldFacade)

  public changeDetectorRef = inject(ChangeDetectorRef)

  private readonly toDestroy$ = new Subject<void>()

  public icons = { search: searchIcon, eyeIcon: eyeIcon }

  public dialogTitle: string

  private selectedPanel: ReviewPanelType

  public initailGridData: Array<Field> = []

  public gridData: Array<Field> = []

  public selectedFields: string[]

  public selectedKeys: string[] = []

  public selectableSettings: SelectableSettings = {
    checkboxOnly: true,
    mode: 'multiple',
  }

  private permittedFields: Field[] = []

  public isDataLoaded = signal<boolean>(false)

  private layoutState: CompositeLayoutState = inject(CompositeLayoutState)

  constructor(
    @Optional()
    private dialogRef: DialogRef
  ) {}

  public ngOnInit(): void {
    this.#setTitle()
    this.#mapToReviewPanelType()
    this.initializeFieldSelections()
  }

  private initializeFieldSelections(): void {
    this.fieldFacade.selectAllPermittedFieldsOfCurrentUser$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((permittedFields) => {
        const panelFields = this.layoutState
          .userSelectedLayout()
          ?.layoutPanels?.find(
            (panel) => panel.panelName === this.selectedPanel
          )?.fields

        const layoutId = this.layoutState.userSelectedLayout()?.layoutId
        const fieldOrderMap = keyBy(panelFields, 'fieldName')

        this.permittedFields = chain(permittedFields)
          .filter((f) =>
            layoutId > 0 ? !!fieldOrderMap[f.displayFieldName] : true
          )
          .orderBy(
            [
              (f): number =>
                fieldOrderMap[f.displayFieldName]?.fieldOrder ??
                Number.MAX_SAFE_INTEGER,
            ],
            ['asc']
          )
          .value()
        this.permittedFields = this.permittedFields.map((f, index) => ({
          ...f,
          displayOrder: index + 1,
        }))
        this.gridData = [...this.permittedFields]
        this.initailGridData = cloneDeep(this.gridData)
        const venioFieldDisplayNames = panelFields
          ?.filter((f) => !f.isCustomField && f.isSelected)
          ?.map((f) => f.fieldName)
        const customFieldDisplayNames = panelFields
          ?.filter((f) => f.isCustomField && f.isSelected)
          ?.map((f) => f.fieldName)
        this.selectedKeys = [
          ...venioFieldDisplayNames,
          ...customFieldDisplayNames,
        ]
        this.isDataLoaded.set(true)

        this.changeDetectorRef.markForCheck()
      })
  }

  #mapToReviewPanelType(): void {
    switch (this.panelFilterEventType) {
      case UtilityPanelType.METADATA:
        this.selectedPanel = ReviewPanelType.Metadata
        break
      case UtilityPanelType.FAMILY:
        this.selectedPanel = ReviewPanelType.ParentChild
        break
      case UtilityPanelType.DUPLICATE:
        this.selectedPanel = ReviewPanelType.Duplicates
        break
      case UtilityPanelType.EMAIL_THREAD:
        this.selectedPanel = ReviewPanelType.EmailThread
        break
      case UtilityPanelType.SIMILAR_DOCUMENTS:
        this.selectedPanel = ReviewPanelType.SimilarDocument
        break
      case UtilityPanelType.NEAR_DUPLICATE:
        this.selectedPanel = ReviewPanelType.NearDuplicate
        break
    }
  }

  #setTitle(): void {
    this.dialogTitle = SHOWHIDE_FIELDS.TITLE(this.panelFilterEventType)
  }

  public onFilter(value): void {
    const searchTerm: string = value.trim().toLowerCase()
    const data = cloneDeep(this.initailGridData)
    if (searchTerm) {
      this.gridData = data.filter((item) =>
        item.displayFieldName.toLowerCase().includes(searchTerm)
      )
    } else {
      this.gridData = data
    }
    this.changeDetectorRef.markForCheck()
  }

  public save(): void {
    //const selectedLayoutPanelFields  = this.layoutState.userSelectedLayout().layoutPanels.find(p=>p.panelName ===this.selectedPanel).fields.map(f=>({...f,isSelected:this.selectedKeys.includes(f.fieldName)}))
    const currentLayout = this.layoutState.userSelectedLayout()
    const updatedPanels = currentLayout.layoutPanels.map((panel) => {
      if (panel.panelName === this.selectedPanel) {
        return {
          ...panel,
          fields: this.permittedFields.map(
            (f, index) =>
              ({
                fieldId: f.venioFieldId,
                fieldName: f.displayFieldName,
                fieldOrder: index,
                isCustomField: f.isCustomField,
                isSelected: this.selectedKeys.includes(f.displayFieldName),
              } as LayoutField)
          ),
        }
      }
      return panel // Keep other panels unchanged
    })

    const updatedLayout = {
      ...currentLayout,
      layoutPanels: updatedPanels,
    }

    // Set the updated Layout object back to the signal
    this.layoutState.userSelectedLayout.set(updatedLayout)
    this.fieldFacade.notifyFieldChanges.next()
    this.dialogRef.close()
  }

  public close(): void {
    this.dialogRef.close()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
