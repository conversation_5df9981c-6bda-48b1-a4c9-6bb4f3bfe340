import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  inject,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { DocumentTableModule } from '../document-table/document-table.module'
import { DocumentToolbarContainerComponent } from '../document-toolbar/document-toolbar-container/document-toolbar-container.component'
import { DocumentEditContainerComponent } from '../../document-utility/edit-action/document-edit-container/document-edit-container.component'
import { EdaiContainerComponent } from '../../edai/edai-container/edai-container.component'
import { ReviewSetStateService } from '@venio/data-access/review'
import { DialogContainerDirective } from '@progress/kendo-angular-dialog'

@Component({
  selector: 'venio-document-table-container',
  standalone: true,
  imports: [
    CommonModule,
    DocumentTableModule,
    DocumentToolbarContainerComponent,
    DocumentEditContainerComponent,
    EdaiContainerComponent,
    DialogContainerDirective,
  ],
  templateUrl: './document-table-container.component.html',
  styleUrls: ['./document-table-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentTableContainerComponent {
  public module = import('../document-table/document-table.module')

  /**
   * Container placeholder for export to file action of a gear icon menu
   */
  public exportToFileContainer = import(
    '../../document-utility/export-to-file/export-to-file-container/export-to-file-container.component'
  ).then((m) => m.ExportToFileContainerComponent)

  /**
   * Container placeholder for DELETE action of a gear icon menu
   */
  public deleteDocumentContainer = import(
    '../../document-utility/delete-document/delete-document-container/delete-document.component'
  ).then((m) => m.DeleteDocumentComponent)

  /**
   * Container placeholder for FOLDERING action of a gear icon menu
   */
  public folderingContainer = import(
    '../../document-utility/foldering/foldering-container/foldering-container.component'
  ).then((m) => m.FolderingContainerComponent)

  /**
   * Container placeholder for TALLY action of a gear icon menu
   */
  public tallyDocumentContainer = import(
    '../../document-utility/tally/document-tally-container/document-tally-container.component'
  ).then((m) => m.DocumentTallyContainerComponent)

  /**
   * Container placeholder for Tag/Coding action of a gear icon menu
   */
  public bulkDocumentTagCodingContainer = import(
    '../../document-utility/bulk-tag-coding-action/bulk-tag-coding-aciton-container/bulk-tag-coding-aciton-container.component'
  ).then((m) => m.BulkTagCodingAcitonContainerComponent)

  /**
   * Container placeholder for PRINT/DOWNLOAD action of a gear icon menu
   */
  public printDownloadDialogContainer = import(
    '../../document-utility/document-print-download/document-print-download-container/document-print-download-container.component'
  ).then((m) => m.DocumentPrintDownloadContainerComponent)

  /**
   * Container placeholder for CONVERT action of a gear icon menu
   */
  public convertDocumentContainer = import(
    '../../document-utility/convert-document/convert-document.component'
  ).then((m) => m.ConvertDocumentComponent)

  /**
   * Container placeholder for Send/Remove action of a gear icon menu
   */
  public bulkFolderContainer = import(
    '../../document-utility/bulk-Folder-action/bulk-Folder-aciton-container/bulk-Folder-aciton-container.component'
  ).then((m) => m.BulkFolderAcitonContainerComponent)

  /**
   * Container placeholder for SAVE SEARCH action of a button beside gear icon menu
   */
  public saveSearchContainer = import(
    '../../document-utility/save-search/save-search-container/save-search-container.component'
  ).then((m) => m.SaveSearchContainerComponent)

  /**
   * Container placeholder for REPLACE action of a button beside gear icon menu
   */
  public replaceFieldValueContainer = import(
    '../../document-utility/replace-field-value/replace-field-value.component'
  ).then((m) => m.ReplaceFieldValueComponent)

  /**
   * Container placeholder for document share action of a button beside gear icon menu
   */
  public documentShareContainer = import(
    '../../document-utility/document-share/document-share.component'
  ).then((m) => m.DocumentShareComponent)

  /**
   * Container placeholder for "switch to email thread view" / "switch to normal view" action of a button beside gear icon menu
   */
  public switchViewContainer = import(
    '../../document-utility/switch-view/switch-view.component'
  ).then((m) => m.SwitchViewComponent)

  /**
   * Container placeholder to tag all inclusive email action of a button beside gear icon menu
   */
  public tagAllInclusiveContainer = import(
    '../../document-utility/tag-all-inclusive-email/tag-all-inclusive.component'
  ).then((m) => m.TagAllInclusiveComponent)

  /**
   * Container placeholder to 'Show Inclusive Emails' or 'Show Non Inclusive Emails' action of a button beside gear icon menu
   */
  public showInclusiveContainer = import(
    '../../document-utility/show-inclusive-email/show-inclusive-email.component'
  ).then((m) => m.ShowInclusiveEmailComponent)

  /**
   * Container placeholder for production action of a button beside gear icon menu
   */
  public productionContainer = import(
    '../../document-utility/send-to-production/send-to-production.component'
  ).then((m) => m.SendToProductionComponent)

  /**
   * Container placeholder for move document to parent action of a button beside gear icon menu
   */
  public moveToParent = import(
    '../../document-utility/move-to-parent/move-to-parent-container/move-to-parent-container.component'
  ).then((m) => m.MoveToParentContainerComponent)

  /**
   * Container placeholder for Responsive PST action of a gear icon menu
   */
  public responsivePstContainer = import(
    '../../document-utility/responsive-pst/responsive-pst-container/responsive-pst-container.component'
  ).then((m) => m.ResponsivePstContainerComponent)

  /**
   * Container placeholder for Bulk Redaction of a gear icon menu
   */
  public bulkRedactionContainer = import(
    '../../document-utility/bulk-redaction/bulk-redaction-container/bulk-redaction-container.component'
  ).then((m) => m.BulkRedactionContainerComponent)

  /**
   * Container placeholder for RSMF Creation action of a gear icon menu
   */
  public rsmfCreationContainer = import(
    '../../document-utility/rsmf-creation/rsmf-creation-container/rsmf-creation-container.component'
  ).then((m) => m.RsmfCreationContainerComponent)

  /**
   * Container placeholder for Entity Extraction action of a gear icon menu
   */
  public entityExtractionContainer = import(
    '../../document-utility/entity-extraction/entity-extraction-container/entity-extraction-container.component'
  ).then((m) => m.EntityExtractionContainerComponent)

  /**
   * Container placeholder for Send To Analyze action of a gear icon menu
   */
  public sendToAnalyzeContainer = import(
    '../../document-utility/send-to-analyze/send-to-analyze.component'
  ).then((m) => m.SendToAnalyzeComponent)

  public reviewSetState = inject(ReviewSetStateService)

  constructor(public elRef: ElementRef) {}
}
