import { Injectable, inject } from '@angular/core'
import { select, Store } from '@ngrx/store'

import * as DocumentShareActions from './document-share.actions'
import * as DocumentShareSelectors from './document-share.selectors'
import {
  DocumentShareModel,
  SharedDocRequestType,
  SharedDocumentDetailModel,
  SharedDocumentDetailRequestInfo,
} from '../../models/interfaces/document-share.model'
import { DocumentShareState } from './document-share.reducer'

type DocumentSharedStateKeys =
  | keyof DocumentShareState
  | Array<keyof DocumentShareState>

@Injectable()
export class DocumentShareFacade {
  private readonly store = inject(Store)

  public isEditMode: boolean | undefined

  public getInternalUsers$ = this.store.pipe(
    select(
      DocumentShareSelectors.getStateOfDocumentShare(
        'documentShareInternalUsers'
      )
    )
  )

  public getExternalUsers$ = this.store.pipe(
    select(
      DocumentShareSelectors.getStateOfDocumentShare(
        'documentShareExternalUsers'
      )
    )
  )

  public getUserMessage$ = this.store.pipe(
    select(DocumentShareSelectors.getStateOfDocumentShare('userMessage'))
  )

  public getInvitationInProgressFlag$ = this.store.pipe(
    select(
      DocumentShareSelectors.getStateOfDocumentShare('invitationInProgress')
    )
  )

  public setInvitationInProgressFlag(isInvitationInProgress: boolean): void {
    this.store.dispatch(
      DocumentShareActions.setInvitationInProgressFlag({
        isInvitationInProgress,
      })
    )
  }

  public setUserMessage(message: string, success: boolean): void {
    this.store.dispatch(
      DocumentShareActions.setUserMessage({
        messageModel: {
          message,
          success,
        },
      })
    )
  }

  public fetchDocumentShareUsersInternal(projectId: number): void {
    this.store.dispatch(
      DocumentShareActions.fetchDocumentShareUsersInternal({ projectId })
    )
  }

  public fetchDocumentShareUsersExternal(projectId: number): void {
    this.store.dispatch(
      DocumentShareActions.fetchDocumentShareUsersExternal({ projectId })
    )
  }

  public shareDocument(
    projectId: number,
    requestModel: DocumentShareModel
  ): void {
    this.store.dispatch(
      DocumentShareActions.shareDocuments({ projectId, requestModel })
    )
  }

  public resetDocumentShareState(): void {
    this.store.dispatch(DocumentShareActions.resetDocumentShareState())
  }

  public readonly selectIsSharedDocumentsLoading$ = this.store.pipe(
    select(
      DocumentShareSelectors.getStateOfDocumentShare('isSharedDocumentsLoading')
    )
  )

  public readonly selectSharedDocumentList$ = this.store.pipe(
    select(
      DocumentShareSelectors.getStateOfDocumentShare(
        'sharedDocumentsSuccessResponse'
      )
    )
  )

  public readonly selectSharedDocumentCountDetail$ = this.store.pipe(
    select(
      DocumentShareSelectors.getStateOfDocumentShare(
        'sharedDocumentCountDetailSuccessResponse'
      )
    )
  )

  public readonly selectSharedDocumentCountDetailError$ = this.store.pipe(
    select(
      DocumentShareSelectors.getStateOfDocumentShare(
        'sharedDocumentCountDetailErrorResponse'
      )
    )
  )

  public readonly selectSharedDocumentsErrorResponse$ = this.store.pipe(
    select(
      DocumentShareSelectors.getStateOfDocumentShare(
        'sharedDocumentsErrorResponse'
      )
    )
  )

  public readonly selectIsSharedDocumentDetailLoading$ = this.store.pipe(
    select(
      DocumentShareSelectors.getStateOfDocumentShare(
        'isSharedDocumentDetailLoading'
      )
    )
  )

  public readonly selectSharedDocumentDetail$ = this.store.pipe(
    select(
      DocumentShareSelectors.getStateOfDocumentShare(
        'sharedDocumentDetailSuccessResponse'
      )
    )
  )

  public readonly selectSharedDocumentDetailErrorResponse$ = this.store.pipe(
    select(
      DocumentShareSelectors.getStateOfDocumentShare(
        'sharedDocumentDetailErrorResponse'
      )
    )
  )

  public readonly selectSharedDocumentRequestInfo$ = this.store.pipe(
    select(
      DocumentShareSelectors.getStateOfDocumentShare(
        'sharedDocumentRequestInfo'
      )
    )
  )

  public readonly selectUpdatedDocumentResponse$ = this.store.pipe(
    select(
      DocumentShareSelectors.getStateOfDocumentShare('updatedDocumentResponse')
    )
  )

  public readonly selectUpdateExpiryDateErrorResponse$ = this.store.pipe(
    select(
      DocumentShareSelectors.getStateOfDocumentShare(
        'updateExpiryDateErrorResponse'
      )
    )
  )

  public readonly selectUnsharedDocumentResponse$ = this.store.pipe(
    select(
      DocumentShareSelectors.getStateOfDocumentShare('unsharedDocumentResponse')
    )
  )

  public readonly selectUnShareDocumentErrorResponse$ = this.store.pipe(
    select(
      DocumentShareSelectors.getStateOfDocumentShare(
        'unShareDocumentErrorResponse'
      )
    )
  )

  public readonly selectSharedDocDetailPagingInfo$ = this.store.pipe(
    select(
      DocumentShareSelectors.getStateOfDocumentShare(
        'sharedDocumentRequestInfo'
      )
    )
  )

  public readonly selectDocumentShareAvailability$ = this.store.pipe(
    select(
      DocumentShareSelectors.getStateOfDocumentShare('documentShareAvailable')
    )
  )

  public fetchSharedDocumentAvailability(projectId: number): void {
    this.store.dispatch(
      DocumentShareActions.fetchSharedDocumentAvailability({ projectId })
    )
  }

  public fetchSharedDocuments(
    projectId: number,
    filterQuery: SharedDocRequestType
  ): void {
    this.store.dispatch(
      DocumentShareActions.fetchSharedDocuments({ projectId, filterQuery })
    )
  }

  public fetchSharedDocumentDetail(
    projectId: number,
    docShareId: number
  ): void {
    this.store.dispatch(
      DocumentShareActions.fetchSharedDocumentDetail({ projectId, docShareId })
    )
  }

  public fetchSharedDocumentCountDetail(): void {
    this.store.dispatch(DocumentShareActions.fetchSharedDocumentCountDetail())
  }

  public updateSharedDocumentRequestInfo(
    sharedDocumentRequestInfo: Partial<SharedDocumentDetailRequestInfo>
  ): void {
    this.store.dispatch(
      DocumentShareActions.updateSharedDocumentRequestInfo({
        sharedDocumentRequestInfo,
      })
    )
  }

  public updateExpiryDate(
    projectId: number,
    docShareId: number,
    payload: SharedDocumentDetailModel
  ): void {
    this.store.dispatch(
      DocumentShareActions.updateExpiryDate({ projectId, docShareId, payload })
    )
  }

  public unShareDocument(
    projectId: number,
    docShareId: number,
    payload: SharedDocumentDetailModel
  ): void {
    this.store.dispatch(
      DocumentShareActions.unShareDocument({ projectId, docShareId, payload })
    )
  }

  public resetSharedDocumentState(stateKey: DocumentSharedStateKeys): void {
    this.store.dispatch(
      DocumentShareActions.resetSharedDocumentState({ stateKey })
    )
  }

  public clearUpdateExpiryDateSuccessResponse(): void {
    this.store.dispatch(DocumentShareActions.updateExpiryDateSuccess(undefined))
  }

  public clearUpdateExpiryDateErrorResponse(): void {
    this.store.dispatch(DocumentShareActions.updateExpiryDateFailure(undefined))
  }

  public clearSharedDocumentDetail(): void {
    this.store.dispatch(
      DocumentShareActions.fetchSharedDocumentDetailSuccess(undefined)
    )
  }
}
