import { Injectable } from '@angular/core'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { SearchFacade } from '../search/search.facade'
import { DocumentsFacade } from '../documents/documents.facade'
import { MoveToParentService } from '../../services/move-to-parent.service'
import * as MoveToParentActions from './move-to-parent.actions'
import { catchError, concatMap, map, of, switchMap, withLatestFrom } from 'rxjs'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { VenioNotificationService } from '@venio/feature/notification'
import { MoveToParentDocumentOptions } from '../../models/interfaces/move-to-parent.model'
import { TempTableResponseModel } from '../../models/interfaces/search.model'

@Injectable()
export class MoveToParentEffects {
  constructor(
    private readonly actions$: Actions,
    private searchFacade: SearchFacade,
    private documentFacade: DocumentsFacade,
    private moveToParentService: MoveToParentService,
    private notificationSerive: VenioNotificationService
  ) {}

  public moveToParent$ = createEffect(() =>
    this.actions$.pipe(
      ofType(MoveToParentActions.moveToParentAction),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.searchFacade.getSearchTempTables$,
            this.documentFacade.getIsBatchSelected$,
            this.documentFacade.getSelectedDocuments$,
            this.documentFacade.getUnselectedDocuments$,
            (
              action,
              tempTables,
              isBatchSelected,
              selectedDocs,
              unSelectedDocs
            ) => ({
              action,
              tempTables,
              isBatchSelected,
              selectedDocs,
              unSelectedDocs,
            })
          )
        )
      ),
      switchMap((param) => {
        const moveToParentDocumentOptions: MoveToParentDocumentOptions =
          this.getMoveToParentOption(
            param.isBatchSelected,
            param.unSelectedDocs,
            param.selectedDocs,
            param.tempTables,
            param.action.payload.parentFileId,
            param.action.payload.sessionId
          )

        return this.moveToParentService
          .moveToParent$<any>(
            param.action.payload.projectId,
            moveToParentDocumentOptions
          )
          .pipe(
            switchMap((response: ResponseModel) => {
              if (response.status === 'Success') {
                this.notificationSerive.showSuccess(
                  'Files moved to another parent successfully.'
                )

                return [
                  MoveToParentActions.setMoveToParentSuccessAction({
                    payload: { message: response.message },
                  }),
                ]
              }
              let message = 'Error occured while moving files to parent.'
              if (response.message.includes('parent file id is not valid')) {
                message =
                  "Can't Move the file to Parent. Provided Parent File ID is Invalid."
              }
              return [
                MoveToParentActions.setMoveToParentFailureAction({
                  payload: {
                    error: message,
                  },
                }),
              ]
            }),
            catchError((err: unknown) => {
              this.notificationSerive.showError(
                'Error occured while moving files to parent.'
              )
              return [
                MoveToParentActions.setMoveToParentFailureAction({
                  payload: { error: err.toString() },
                }),
              ]
            })
          )
      })
    )
  )

  private getMoveToParentOption(
    isBatchSelected: boolean,
    unSelectedDocs: number[],
    selectedDocs: number[],
    tempTables: TempTableResponseModel,
    parentFileId: number,
    sessionId: string
  ): MoveToParentDocumentOptions {
    const moveToParentDocumentOption: MoveToParentDocumentOptions = {
      IsBatchSelected: isBatchSelected,
      UnSelectedFileIds: unSelectedDocs,
      SelectedFileIds: selectedDocs,
      SearchTempTable: tempTables.searchResultTempTable,
      SessionId: sessionId,
      ParentFileid: parentFileId,
    }
    return moveToParentDocumentOption
  }

  public getMoveDocumentStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(MoveToParentActions.getMoveToParentStatusAction),
      switchMap((action) =>
        this.moveToParentService
          .getMoveDocumentStatus$(
            action.payload.projectId,
            action.payload.sessionId
          )
          .pipe(
            map((res: ResponseModel) => {
              return MoveToParentActions.setMoveToParentStatusAction({
                payload: { moveToParentStatus: res.data },
              })
            }),
            catchError((error: unknown) =>
              of(
                MoveToParentActions.setMoveToParentFailureAction({
                  payload: { error: 'Move to parent failed' },
                })
              )
            )
          )
      )
    )
  )

  public dropMoveDocumentTempTable$ = createEffect(() =>
    this.actions$.pipe(
      ofType(MoveToParentActions.dropMoveToParentTempTableAction),
      switchMap((action) =>
        this.moveToParentService
          .dropMoveDocumentTempTable$(
            action.payload.projectId,
            action.payload.sessionId
          )
          .pipe(
            map((Response) =>
              MoveToParentActions.resetMoveToParentOptionAction()
            )
          )
      )
    )
  )

  public getNonParentDocument$ = createEffect(() =>
    this.actions$.pipe(
      ofType(MoveToParentActions.getNonParentDocumentAction),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.documentFacade.getSelectedDocuments$,
            this.documentFacade.getUnselectedDocuments$,
            this.documentFacade.getIsBatchSelected$,
            this.searchFacade.getTotalHitCount$,
            this.searchFacade.getSearchTempTables$
          )
        )
      ),
      switchMap(
        ([
          action,
          selectedDocs,
          unSelectedDocs,
          isBatchSelected,
          totalHitCount,
          searchResultTempTable,
        ]) => {
          const moveToParentDocumentOptions: MoveToParentDocumentOptions = {
            SelectedFileIds: selectedDocs,
            UnSelectedFileIds: unSelectedDocs,
            IsBatchSelected: isBatchSelected,
            SearchTempTable: searchResultTempTable.searchResultTempTable,
            SessionId: '',
            ParentFileid: 0,
          }
          return this.moveToParentService
            .getNonParentDocument$(
              action.payload.projectId,
              moveToParentDocumentOptions
            )
            .pipe(
              map((res: ResponseModel) => {
                if (res) {
                  let selectedDocCount = 0
                  if (isBatchSelected) {
                    selectedDocCount = totalHitCount - unSelectedDocs.length
                  } else {
                    selectedDocCount = selectedDocs.length
                  }
                  return MoveToParentActions.getNonParentDocumentSuccessAction({
                    payload: {
                      parentFileCount: selectedDocCount - res.data.length,
                    },
                  })
                }
              }),
              catchError((error: unknown) => {
                throw error
              })
            )
        }
      )
    )
  )
}
