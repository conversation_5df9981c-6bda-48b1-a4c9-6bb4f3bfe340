import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentViewDesignerDropdownComponent } from './document-view-designer-dropdown.component'
import { provideMockStore } from '@ngrx/store/testing'
import { FieldFacade, SearchFacade, ViewModel } from '@venio/data-access/review'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideRouter } from '@angular/router'
import { UserFacade } from '@venio/data-access/common'
import { of } from 'rxjs'

describe('DocumentViewDesignerDropdownComponent', () => {
  let component: DocumentViewDesignerDropdownComponent
  let fixture: ComponentFixture<DocumentViewDesignerDropdownComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DocumentViewDesignerDropdownComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideRouter([]),
        provideMockStore({}),
        SearchFacade,
        FieldFacade,
        {
          provide: BreadcrumbFacade,
          useValue: jest.fn(),
        },
        {
          provide: UserFacade,
          useValue: {
            selectCurrentUserDetails$: of(null),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentViewDesignerDropdownComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
  it('should toggle dropdown visibility', () => {
    // GIVEN the initial state of dropdown visibility is false
    expect(component.dropdownContentVisibility).toBeFalsy()

    // WHEN the toggleDropdown method is called
    component.toggleDropdown()

    // THEN the dropdownContentVisibility should be true
    expect(component.dropdownContentVisibility).toBeTruthy()
  })
  it('should load items correctly excluding the selected one', () => {
    // GIVEN a selected item
    component.allItems = [
      { viewId: 1, viewName: 'Selected Item' },
      { viewId: 2, viewName: 'Item 2' },
      { viewId: 3, viewName: 'Item 3' },
    ] as ViewModel[]
    component.selectedDocumentViewDesignerDropdownItem.set({
      viewId: 1,
      viewName: 'Selected Item',
    } as ViewModel)

    // WHEN loadMoreItems is called
    component.loadMoreItems()

    // THEN loadedItems should not contain the selected item
    expect(component.loadedItems()).not.toContainEqual(
      expect.objectContaining({ viewId: 1 })
    )
  })
  it('should handle document click to close the dropdown', () => {
    // GIVEN the dropdown is visible
    component.toggleDropdown(true)

    // WHEN a click outside the dropdown occurs
    document.dispatchEvent(new MouseEvent('click'))
    fixture.detectChanges()

    // THEN the dropdown should be closed
    expect(component.dropdownContentVisibility).toBe(false)
  })
  it('should not include the selected item in the dropdown list', () => {
    // GIVEN a selected item
    const selectedItem = { viewId: 1, viewName: 'Selected View' } as ViewModel
    component.selectedDocumentViewDesignerDropdownItem.set(selectedItem)
    component.allItems = [
      selectedItem,
      { viewId: 2, viewName: 'Another View' },
    ] as ViewModel[]

    // WHEN loading items
    component.loadMoreItems()

    // THEN the dropdown list should not include the selected item
    const items = component.loadedItems()
    expect(items).not.toContainEqual(selectedItem)
  })
  it('should not contain duplicate items in the dropdown list', () => {
    // GIVEN a set of items with duplicates
    component.allItems = [
      { viewId: 2, viewName: 'View A' },
      { viewId: 2, viewName: 'View A' },
      { viewId: 3, viewName: 'View B' },
    ] as ViewModel[]

    // WHEN loading items
    component.loadMoreItems()

    // THEN the dropdown list should only contain unique items
    const items = component.loadedItems()
    const uniqueItems = Array.from(new Set(items.map((item) => item.viewId)))
    expect(items.map((c) => c.viewId)).toStrictEqual(uniqueItems)
  })
  it('should load more items when scrolled to the bottom', () => {
    // GIVEN a list of items that exceeds the page size
    const largeNumberOfItems = new Array(50).fill(null).map(
      (_, index) =>
        ({
          viewId: index + 1,
          viewName: `View ${index}`,
        } as ViewModel)
    )
    component.allItems = [...largeNumberOfItems] as ViewModel[]
    component.loadedItems.set(largeNumberOfItems.slice(0, 20))

    // WHEN scrolling to the bottom of the list
    component.loadMoreItems() // Simulate the initial load

    // THEN it should load the next set of items
    expect(component.loadedItems().length).toBeGreaterThan(20)
  })
})
