<div class="t-flex t-gap-2" kendoTooltip>
  <button
    kendoButton
    #anchor
    title="Document Menu"
    (click)="toggle()"
    [svgIcon]="(isDocumentMenuLoading | async) ? null : gearSVG"
    [disabled]="isDocumentMenuLoading | async"
    fillMode="outline"
    class="!t-px-[6px] !t-text-[#1EBADC] hover:t-opacity-90 hover:!t-border-[#1EBADC] hover:!t-bg-[#1EBADC] hover:!t-text-[#FFFFFF]"
    size="none">
    <kendo-loader
      *ngIf="isDocumentMenuLoading | async; else tplChevIcon"
      size="medium"
      type="pulsing"></kendo-loader>
    <ng-template #tplChevIcon>
      <div class="t-flex t-pl-2">
        <kendo-svg-icon [icon]="chevDown"></kendo-svg-icon>
      </div>
    </ng-template>
  </button>
  <kendo-popup
    #popup
    [anchor]="anchor.element"
    [popupClass]="
      '!t-rounded-none v-document-menu-popup-container !t-shadow-md'
    "
    *ngIf="show">
    <div
      class="t-flex t-flex-col t-whitespace-nowrap t-max-h-[calc(100vh_-_10rem)] t-overflow-auto">
      @for (link of documentMenuItems; track link.svgIcon){
      <div
        #menuItem
        *venioHasUserGroupRights="
          link.allowedPermission;
          anyOfTheGivenPermission: link.isAnyOfThese
        "
        (click)="!link.disabled && menuItemClick(link.type)"
        [ngClass]="{
          'v-document-menu-popup-item': !link.disabled,
          'k-disabled': link.disabled
        }"
        class="t-relative t-cursor-pointer t-px-2 t-py-1">
        <div class="t-flex t-flex-row t-gap-4 t-items-center">
          <span
            class="t-inline-block t-align-bottom"
            venioSvgLoader
            [applyEffectsTo]="link.disabled ? 'fill' : link.applyEffectsTo"
            [parentElement]="menuItem"
            [color]="link.disabled ? '#969696' : '#263238'"
            hoverColor="#ffffff"
            [svgUrl]="link.svgIcon"
            [height]="link.x"
            [width]="link.y">
            <!-- Shows until the SVG is fully loaded-->
            <kendo-loader size="small"></kendo-loader>
          </span>
          <span>
            {{ link.text }}
          </span>
        </div>
      </div>
      }
    </div>
  </kendo-popup>

  <button
    kendoButton
    class="v-custom-secondary-button !t-capitalize !t-px-2 !t-text-[#9BD2A7] enabled:hover:!t-border-[#9BD2A7] enabled:hover:!t-bg-[#9BD2A7] enabled:hover:!t-text-[#FFFFFF] !t-rounded"
    themeColor="secondary"
    fillMode="clear"
    *ngIf="
      reviewViewType !== ReviewViewType.EmailThread &&
      !this.reviewSetState.isBatchReview()
    "
    (click)="menuItemClick(documentMenuType.SAVE_SEARCH)"
    #saveSearchButton>
    <span
      class="t-inline-block t-align-[-3px]"
      venioSvgLoader
      applyEffectsTo="fill"
      [parentElement]="saveSearchButton.element"
      hoverColor="#FFFFFF"
      [isSelectedState]="saveSearchButton.isFocused"
      [color]="saveSearchButton.isFocused ? '#9BD2A7' : '#9BD2A7'"
      svgUrl="assets/svg/icon-review-save.svg"
      height="1rem"
      width="1rem">
      <kendo-loader size="small"></kendo-loader>
    </span>

    Save Search
  </button>

  <!-- Batch Review Buttons -->
  <div class="t-flex t-gap-3" *ngIf="reviewSetState.isBatchReview()">
    <button
      kendoButton
      fillMode="outline"
      class="t-px-1.5 t-py-0.5 !t-border-[#ccc]"
      [disabled]="isReviewedButtonDisabled()"
      (click)="onMarkAsReviewed()"
      kendoTooltip
      [title]="reviewedTooltip()">
      <span
        venioSvgLoader
        height="22px"
        width="23px"
        [svgUrl]="isReviewedIcon()"></span>
    </button>
    <button
      kendoButton
      fillMode="outline"
      class="t-px-1.5 t-py-0.5 !t-border-[#ccc]"
      [disabled]="isBatchCheckInDisabled()"
      (click)="checkInBatch()"
      kendoTooltip
      title="Batch Check In">
      <span
        venioSvgLoader
        height="22px"
        width="23px"
        [svgUrl]="batchCheckInIcon()"></span>
    </button>
  </div>
</div>
