import { ComponentFixture, TestBed } from '@angular/core/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { AiFacade } from '@venio/data-access/ai'
import { BehaviorSubject } from 'rxjs'

import { VerticalLegendsComponent } from './vertical-legends.component'

describe('VerticalLegendsComponent', () => {
  let component: VerticalLegendsComponent
  let fixture: ComponentFixture<VerticalLegendsComponent>

  const mockAiFacade = {
    selectEcaDocumentTypesSuccess$: new BehaviorSubject(null),
    selectEcaTopicsRelevantSuccess$: new BehaviorSubject(null),
    selectEcaTopicsNonRelevantSuccess$: new BehaviorSubject(null),
    selectEcaRelevanceSuccess$: new BehaviorSubject(null),
    selectChartCurrentData$: jest.fn().mockReturnValue(new BehaviorSubject([])),
    drillDownToNextLevel: jest.fn(),
    updateChartTableData: jest.fn(),
    setChartSelectedNode: jest.fn(),
  } satisfies Partial<AiFacade>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [VerticalLegendsComponent],
      providers: [
        provideMockStore({}),
        { provide: AiFacade, useValue: mockAiFacade },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(VerticalLegendsComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
