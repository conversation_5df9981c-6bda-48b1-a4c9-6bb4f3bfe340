import {
  createFeatureSelector,
  createSelector,
  MemoizedSelector,
} from '@ngrx/store'
import {
  ControlNumberConfigState,
  CONTROL_NUMBER_CONFIG_FEATURE_KEY,
} from './control-number.reducer'
import { CustodianMediaControlNumberConfig } from '../../models/interfaces'

export const getControlNumberConfigState =
  createFeatureSelector<ControlNumberConfigState>(
    CONTROL_NUMBER_CONFIG_FEATURE_KEY
  )

export const getStateOfControlNumberConfig = <
  T extends keyof ControlNumberConfigState
>(
  stateKey: T
): MemoizedSelector<object, ControlNumberConfigState[T], unknown> =>
  createSelector(
    getControlNumberConfigState,
    (state: ControlNumberConfigState) => state[stateKey]
  )

export const custodianMediaList = createSelector(
  getStateOfControlNumberConfig('custodianMedia'),
  (custodianMedia: CustodianMediaControlNumberConfig[]) => custodianMedia
)

export const controlNumberStatus = createSelector(
  getStateOfControlNumberConfig('custodianMediaStatus'),
  (custodianMediaStatus: ControlNumberConfigState['custodianMediaStatus']) =>
    custodianMediaStatus
)

export const controlNumberConfigError = createSelector(
  getStateOfControlNumberConfig('controlNumberConfigError'),
  (error: ControlNumberConfigState['controlNumberConfigError']) => error
)

export const controlNumberStatusError = createSelector(
  getStateOfControlNumberConfig('controlNumberStatusError'),
  (error: ControlNumberConfigState['controlNumberStatusError']) => error
)
