import { Component, inject } from '@angular/core'
import {
  TagCloudComponent,
  CloudOptions,
  CloudData,
} from 'angular-tag-cloud-module'
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component'
import { toSignal } from '@angular/core/rxjs-interop'
import { AiFacade, ECADashboardType } from '@venio/data-access/ai'
import { map } from 'rxjs'

@Component({
  selector: 'venio-word-cloud',
  standalone: true,
  imports: [TagCloudComponent, TitleAndDownloadComponent],
  templateUrl: './word-cloud.component.html',
  styleUrl: './word-cloud.component.scss',
})
export class WordCloudComponent {
  private readonly aiFacade = inject(AiFacade)

  public readonly dashboardType = ECADashboardType
  public readonly options: CloudOptions = {
    // if width is between 0 and 1 it will be set to the width of the upper element multiplied by the value
    width: 1000,
    // if height is between 0 and 1 it will be set to the height of the upper element multiplied by the value
    height: 400,
    overflow: false,
  }

  // ECA API data - Use relevant topics for word cloud
  public readonly ecaTopicsData = toSignal(
    this.aiFacade.selectEcaTopicsRelevantSuccess$,
    { initialValue: null }
  )

  public readonly data = toSignal(
    this.aiFacade.selectEciWordCloudData$.pipe(
      map((wordCloudData) => {
        try {
          // Use ECA API data if available
          const ecaData = this.ecaTopicsData()
          if (ecaData?.data?.topics && Array.isArray(ecaData.data.topics)) {
            const transformed = this.#transformEcaTopicsToWordCloud(
              ecaData.data.topics
            )
            return transformed || []
          }

          // Fallback to legacy word cloud data
          if (wordCloudData && Array.isArray(wordCloudData)) {
            return wordCloudData
              .map((item) => {
                if (!item) return null
                return {
                  text: item.text || '',
                  weight: item.weight || 1,
                  color: item.color || '#000000',
                  tooltip: item.tooltip || '',
                  link: item.link || '',
                  external: item.external || false,
                } as CloudData
              })
              .filter((item) => item !== null && item.text.length > 0)
          }

          return []
        } catch (error) {
          console.error('Error processing word cloud data:', error)
          return []
        }
      })
    ),
    { initialValue: [] as CloudData[] }
  )

  #transformEcaTopicsToWordCloud(ecaTopics: any[]): CloudData[] {
    try {
      if (!ecaTopics || !Array.isArray(ecaTopics)) {
        console.warn('Invalid ECA topics data for word cloud transformation')
        return []
      }

      return ecaTopics
        .map((topic, index) => {
          if (!topic || !topic.topicName) return null

          const docCount = topic.docCount || 0
          const percentage = topic.percentage || 0

          return {
            text: topic.topicName,
            weight: Math.max(10, Math.min(100, docCount / 10)), // Scale weight between 10-100
            color: this.#generateColor(index),
            tooltip: `${
              topic.topicName
            }: ${docCount} documents (${percentage.toFixed(1)}%)`,
            link: '',
            external: false,
          } as CloudData
        })
        .filter((item) => item !== null)
    } catch (error) {
      console.error('Error transforming ECA topics to word cloud:', error)
      return []
    }
  }

  #generateColor(index: number): string {
    const colors = [
      '#FF6B6B',
      '#4ECDC4',
      '#45B7D1',
      '#96CEB4',
      '#FFEAA7',
      '#DDA0DD',
      '#98D8C8',
      '#F7DC6F',
      '#BB8FCE',
      '#85C1E9',
    ]
    return colors[index % colors.length]
  }
}
