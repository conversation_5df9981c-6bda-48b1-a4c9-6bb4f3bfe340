import { inject, Injectable } from '@angular/core'
import { Store } from '@ngrx/store'
import * as CommandActions from './commands.actions'
import * as CommandSelectors from './commands.selectors'
import { CommandEventTypes } from '@venio/shared/models/constants'
import { dispatchCommand } from './commands.actions'
import { CommandEvent } from '@venio/shared/models/interfaces'

@Injectable({ providedIn: 'root' })
export class CommandsFacade {
  private readonly store = inject(Store)

  /**
   * Selecting `CommandEventTypes.NotifyLaunchAdvancedSearch` from the Commands State.
   */
  public readonly selectNotifyLaunchAdvancedSearch$ = this.store.select(
    CommandSelectors.getStateFromCommandStore(
      CommandEventTypes.NotifyLaunchAdvancedSearch
    )
  )

  public readonly selectNotifyEnterViewerPanel$ = this.store.select(
    CommandSelectors.getStateFromCommandStore(
      CommandEventTypes.NotifyEnterViewerPanel
    )
  )

  public selectResetSelectedCustomFolder$ = this.store.select(
    CommandSelectors.getStateFromCommandStore(
      CommandEventTypes.ResetSelectedCustomFolder
    )
  )

  public selectFolderTabType$ = this.store.select(
    CommandSelectors.getStateFromCommandStore(
      CommandEventTypes.StoreFolderTabType
    )
  )

  public selectPanelVisibility$ = this.store.select(
    CommandSelectors.getStateFromCommandStore(
      CommandEventTypes.TogglePanelVisibility
    )
  )

  // TODO: Similar to the above, add more selectors for other command types

  /**
   * Resetting a specific property of the Commands State.
   * You can pass a single property or an array of properties to reset.
   * @example
   * // Reset a single property
   * this.commandsFacade.resetCommandState('NotifyLaunchAdvancedSearch')
   *
   * // Reset multiple properties
   * this.commandsFacade.resetCommandState(['NotifyLaunchAdvancedSearch', 'RemoveX'])
   * @param {CommandEventTypes} stateKey - state keys
   * @returns {void}
   */
  public resetCommandState(
    stateKey: CommandEventTypes | CommandEventTypes[]
  ): void {
    this.store.dispatch(CommandActions.resetCommandState({ stateKey }))
  }

  /**
   * Dispatching a command event.
   * This will trigger the command dispatcher with the event type and data.
   * The data is optional and can be any type as needed.
   * @example
   * // Dispatch an event to notify the launch of advanced search
   * this.commandsFacade.dispatchCommand({
   *  type: CommandEventTypes.NotifyLaunchAdvancedSearch,
   *  data: { isLaunch: true } // or false
   *  })
   *
   *  // Dispatch an event to remove X
   *  this.commandsFacade.dispatchCommand({
   *   type: CommandEventTypes.RemoveX
   *  })
   * @param {CommandEvent} event - command event
   * @returns {void}
   */
  public dispatchCommand(event: CommandEvent): void {
    this.store.dispatch(dispatchCommand({ event }))
  }
}
