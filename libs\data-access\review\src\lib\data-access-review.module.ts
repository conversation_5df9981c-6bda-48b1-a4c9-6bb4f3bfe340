import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http'
import { StoreModule } from '@ngrx/store'
import {
  searchReducer,
  SEARCH_FEATURE_KEY,
} from './state/search/search.reducer'
import { SearchFacade } from './state/search/search.facade'
import { EffectsModule } from '@ngrx/effects'
import { SearchEffects } from './state/search/search.effects'
import { BatchReviewFacade } from './state/batch-review/batch-review.facade'
import {
  batchReviewReducer,
  BATCH_REVIEW_FEATURE_KEY,
} from './state/batch-review/batch-review.reducer'
import { SearchResultEffects } from './state/search-result/search-result.effects'
import {
  searchResultReducer,
  SEARCH_RESULT_FEATURE_KEY,
} from './state/search-result/search-result.reducer'
import { SearchResultFacade } from './state/search-result/search-result.facade'
import {
  DocumentsEffects,
  DocumentsFacade,
  documentsReducer,
  DOCUMENTS_FEATURE_KEY,
} from './state/documents'
import {
  FieldEffects,
  FieldFacade,
  fieldReducer,
  FIELD_FEATURE_KEY,
} from './state/field'
import {
  ViewerEffects,
  ViewerFacade,
  viewerReducer,
  VIEWER_FEATURE_KEY,
} from './state/viewer'
import {
  DYNAMIC_FOLDER_FEATURE_KEY,
  DynamicFolderEffects,
  DynamicFolderFacade,
  dynamicFolderReducer,
} from './state/dynamic-folder'
import {
  FOLDER_FEATURE_KEY,
  FolderEffects,
  FolderFacade,
  folderReducer,
} from './state/folder'
import {
  DELETE_DOCUMENT_FEATURE_KEY,
  deleteDocReducer,
  DeleteDocumentFacade,
  DeleteDocumentEffects,
} from './state/delete-document'
import {
  CONVERT_DOCUMENT_FEATURE_KEY,
  convertDocumentReducer,
  ConvertDocumentFacade,
  ConvertDocumentEffects,
} from './state/convert-document'
import {
  CONVERT_TO_HTML_FEATURE_KEY,
  convertToHtmlReducer,
  ConvertToHtmlFacade,
  ConvertToHtmlEffects,
} from './state/convert-document/convert-to-html'
import {
  CONVERT_TO_RTF_FEATURE_KEY,
  convertToRtfReducer,
  ConvertToRtfFacade,
  ConvertToRtfEffects,
} from './state/convert-document/convert-to-rtf'
import {
  CONVERT_TO_IMAGE_FEATURE_KEY,
  convertToImageReducer,
  ConvertToImageFacade,
  ConvertToImageEffects,
} from './state/convert-document/convert-to-image'
import {
  OCR_IMAGE_FEATURE_KEY,
  ocrImageReducer,
  OcrImageFacade,
  OcrImageEffects,
} from './state/convert-document/ocr-image'
import {
  OCR_GENERATED_IMAGE_FEATURE_KEY,
  ocrGeneratedImageReducer,
  OcrGeneratedImageFacade,
  OcrGeneratedImageEffects,
} from './state/convert-document/ocr-generated-image'
import {
  OCR_REDACTED_IMAGE_FEATURE_KEY,
  ocrRedactedImageReducer,
  OcrRedactedImageFacade,
  OcrRedactedImageEffects,
} from './state/convert-document/ocr-redacted-image'
import {
  CREATE_SLIPSHEET_FEATURE_KEY,
  createSlipsheetReducer,
  CreateSlipsheetFacade,
  CreateSlipsheetEffects,
} from './state/convert-document/create-slipsheet'

import {
  STARTUPS_FEATURE_KEY,
  StartupsEffects,
  StartupsFacade,
  startupsReducer,
} from './state/startups'
import { VIEW_FEATURE_KEY, viewReducer } from './state/view/view.reducer'
import { ViewEffects } from './state/view/view.effects'
import { ViewFacade } from './state/view'
import {
  TALLY_FEATURE_KEY,
  TallyEffects,
  TallyFacade,
  tallyReducer,
} from './state/tally'
import {
  PRINT_DOCUMENT_FEATURE_KEY,
  PrintDocumentFacade,
  PrintDocumentsEffects,
  printDocumentReducer,
} from './state/print-document'
import { VenioNotificationService } from '@venio/feature/notification'
import { NotificationService } from '@progress/kendo-angular-notification'
import {
  NativeDownloadEffects,
  NativeDownloadFacade,
} from './state/native-download'
import {
  NATIVE_DOWNLOAD_FEATURE_KEY,
  nativeDownloadReducer,
} from './state/native-download/native-download.reducer'
import { NativeDownloadService } from './services'
import {
  REPLACE_FIELD_FEATURE_KEY,
  ReplaceFieldEffects,
  ReplaceFieldFacade,
  replaceFieldReducer,
} from './state/replace-field'
import {
  DOCUMENT_SHARE_FEATURE_KEY,
  DocumentShareReducer,
  DocumentShareFacade,
  DocumentShareEffects,
} from './state/document-share'
import {
  CASE_INFO_FEATURE_KEY,
  CaseInfoReducer,
  CaseInfoFacade,
  CaseInfoEffects,
} from './state/case-info'
import {
  REVIEW_FEATURE_KEY,
  ReviewReducer,
  ReviewFacade,
  ReviewEffects,
} from './state/review'
import {
  MOVE_TO_PARENT_FEATURE_KEY,
  MoveToParentEffects,
  MoveToParentFacade,
  moveToParentReducer,
} from './state/move-to-parent'
import {
  RESPONSIVE_PST_FEATURE_KEY,
  ResponsivePstEffects,
  ResponsivePstFacade,
  responsivePstReducer,
} from './state/responsive-pst'
import {
  AUDIO_TRANSCRIBE_FEATURE_KEY,
  AudioTranscribeEffects,
  AudioTranscribeFacade,
  audioTranscribeReducer,
} from './state/convert-document/audio-transcribe'
import {
  BulkRedactEffects,
  BULK_REDACT_FEATURE_KEY,
  bulkRedactReducer,
  BulkRedactFacade,
} from './state/bulk-redact'
import { ReviewResponseProcessorUtilityService } from './utilities'
import {
  RSMF_FEATURE_KEY,
  RSMFCreationEffects,
  rsmfCreationReducer,
  RSMFCreationFacade,
} from './state/rsmf-creation'
import { EntityExtractionService } from './services/entity-extraction.service'
import {
  PASSWORD_BANK_FEATURE_KEY,
  PasswordBankEffects,
  PasswordBankFacade,
  passwordBankReducer,
} from './state/password-bank'
import {
  CONTROL_NUMBER_CONFIG_FEATURE_KEY,
  ControlNumberConfigReducer,
} from './state/control-number-config/control-number.reducer'
import { ControlNumberConfigFacade } from './state/control-number-config/control-number.facade'
import { ControlNumberConfigEffects } from './state/control-number-config/control-number.effects'

@NgModule({
  imports: [
    CommonModule,
    EffectsModule.forFeature([
      SearchEffects,
      SearchResultEffects,
      FieldEffects,
      DocumentsEffects,
      ViewerEffects,
      DynamicFolderEffects,
      FolderEffects,
      StartupsEffects,
      DeleteDocumentEffects,
      ViewEffects,
      TallyEffects,
      PrintDocumentsEffects,
      ConvertDocumentEffects,
      ConvertToHtmlEffects,
      ConvertToRtfEffects,
      ConvertToImageEffects,
      OcrGeneratedImageEffects,
      OcrRedactedImageEffects,
      OcrImageEffects,
      CreateSlipsheetEffects,
      NativeDownloadEffects,
      ReplaceFieldEffects,
      DocumentShareEffects,
      CaseInfoEffects,
      ReviewEffects,
      MoveToParentEffects,
      ResponsivePstEffects,
      AudioTranscribeEffects,
      BulkRedactEffects,
      RSMFCreationEffects,
      PasswordBankEffects,
      ControlNumberConfigEffects,
    ]),
    StoreModule.forFeature(SEARCH_FEATURE_KEY, searchReducer),
    StoreModule.forFeature(BATCH_REVIEW_FEATURE_KEY, batchReviewReducer),
    StoreModule.forFeature(SEARCH_RESULT_FEATURE_KEY, searchResultReducer),
    StoreModule.forFeature(DOCUMENTS_FEATURE_KEY, documentsReducer),
    StoreModule.forFeature(FIELD_FEATURE_KEY, fieldReducer),
    StoreModule.forFeature(VIEWER_FEATURE_KEY, viewerReducer),
    StoreModule.forFeature(DYNAMIC_FOLDER_FEATURE_KEY, dynamicFolderReducer),
    StoreModule.forFeature(FOLDER_FEATURE_KEY, folderReducer),
    StoreModule.forFeature(STARTUPS_FEATURE_KEY, startupsReducer),
    StoreModule.forFeature(DELETE_DOCUMENT_FEATURE_KEY, deleteDocReducer),
    StoreModule.forFeature(VIEW_FEATURE_KEY, viewReducer),
    StoreModule.forFeature(TALLY_FEATURE_KEY, tallyReducer),
    StoreModule.forFeature(PRINT_DOCUMENT_FEATURE_KEY, printDocumentReducer),
    StoreModule.forFeature(
      CONVERT_DOCUMENT_FEATURE_KEY,
      convertDocumentReducer
    ),
    StoreModule.forFeature(CONVERT_TO_HTML_FEATURE_KEY, convertToHtmlReducer),
    StoreModule.forFeature(CONVERT_TO_RTF_FEATURE_KEY, convertToRtfReducer),
    StoreModule.forFeature(CONVERT_TO_IMAGE_FEATURE_KEY, convertToImageReducer),
    StoreModule.forFeature(OCR_IMAGE_FEATURE_KEY, ocrImageReducer),
    StoreModule.forFeature(
      OCR_GENERATED_IMAGE_FEATURE_KEY,
      ocrGeneratedImageReducer
    ),
    StoreModule.forFeature(
      OCR_REDACTED_IMAGE_FEATURE_KEY,
      ocrRedactedImageReducer
    ),
    StoreModule.forFeature(
      CREATE_SLIPSHEET_FEATURE_KEY,
      createSlipsheetReducer
    ),
    StoreModule.forFeature(NATIVE_DOWNLOAD_FEATURE_KEY, nativeDownloadReducer),
    StoreModule.forFeature(REPLACE_FIELD_FEATURE_KEY, replaceFieldReducer),
    StoreModule.forFeature(DOCUMENT_SHARE_FEATURE_KEY, DocumentShareReducer),
    StoreModule.forFeature(CASE_INFO_FEATURE_KEY, CaseInfoReducer),
    StoreModule.forFeature(REVIEW_FEATURE_KEY, ReviewReducer),
    StoreModule.forFeature(MOVE_TO_PARENT_FEATURE_KEY, moveToParentReducer),
    StoreModule.forFeature(RESPONSIVE_PST_FEATURE_KEY, responsivePstReducer),
    StoreModule.forFeature(
      AUDIO_TRANSCRIBE_FEATURE_KEY,
      audioTranscribeReducer
    ),
    StoreModule.forFeature(BULK_REDACT_FEATURE_KEY, bulkRedactReducer),
    StoreModule.forFeature(RSMF_FEATURE_KEY, rsmfCreationReducer),
    StoreModule.forFeature(PASSWORD_BANK_FEATURE_KEY, passwordBankReducer),
    StoreModule.forFeature(
      CONTROL_NUMBER_CONFIG_FEATURE_KEY,
      ControlNumberConfigReducer
    ),
  ],
  providers: [
    SearchFacade,
    BatchReviewFacade,
    SearchResultFacade,
    DocumentsFacade,
    FieldFacade,
    ViewerFacade,
    DynamicFolderFacade,
    FolderFacade,
    StartupsFacade,
    DeleteDocumentFacade,
    ViewFacade,
    TallyFacade,
    PrintDocumentFacade,
    NotificationService,
    VenioNotificationService,
    ConvertDocumentFacade,
    ConvertToHtmlFacade,
    ConvertToImageFacade,
    ConvertToRtfFacade,
    OcrGeneratedImageFacade,
    OcrRedactedImageFacade,
    OcrImageFacade,
    CreateSlipsheetFacade,
    NativeDownloadService,
    NativeDownloadFacade,
    ReplaceFieldFacade,
    DocumentShareFacade,
    CaseInfoFacade,
    ReviewFacade,
    MoveToParentFacade,
    ResponsivePstFacade,
    AudioTranscribeFacade,
    BulkRedactFacade,
    ReviewResponseProcessorUtilityService,
    RSMFCreationFacade,
    EntityExtractionService,
    PasswordBankFacade,
    provideHttpClient(withInterceptorsFromDi()),
    ControlNumberConfigFacade,
  ],
})
export class DataAccessReviewModule {}
