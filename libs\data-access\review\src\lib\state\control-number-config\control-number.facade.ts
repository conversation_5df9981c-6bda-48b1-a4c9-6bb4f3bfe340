import { Injectable, inject } from '@angular/core'
import { select, Store } from '@ngrx/store'
import * as ControlNumberConfigActions from './control-number.actions'
import * as ControlNumberConfigSelector from './control-number.selectors'
import { GenerateControlNumberRequestModel } from '../../models/interfaces'

@Injectable()
export class ControlNumberConfigFacade {
  private store: Store = inject(Store)

  public getCustodianMedia$ = this.store.pipe(
    select(
      ControlNumberConfigSelector.getStateOfControlNumberConfig(
        'custodianMedia'
      )
    )
  )

  public getCustodianMediaStatus$ = this.store.pipe(
    select(
      ControlNumberConfigSelector.getStateOfControlNumberConfig(
        'custodianMediaStatus'
      )
    )
  )

  public getControlNumberConfigError$ = this.store.pipe(
    select(ControlNumberConfigSelector.controlNumberConfigError)
  )

  public getControlNumberStatusError$ = this.store.pipe(
    select(ControlNumberConfigSelector.controlNumberStatusError)
  )

  public fetchCustodianMedia(projectId: number): void {
    this.store.dispatch(
      ControlNumberConfigActions.fetchCustodianMedia$({ projectId })
    )
  }

  public fetchCustodianMediaStatus(projectId: number): void {
    this.store.dispatch(
      ControlNumberConfigActions.fetchCustodianMediaStatus$({ projectId })
    )
  }

  public generateControlNumber(
    projectId: number,
    payload: GenerateControlNumberRequestModel
  ): void {
    this.store.dispatch(
      ControlNumberConfigActions.generateControlNumber({
        projectId,
        payload,
      })
    )
  }

  public clearControlNumberConfigError(): void {
    this.store.dispatch(
      ControlNumberConfigActions.clearControlNumberConfigError()
    )
  }

  public clearControlNumberStatusError(): void {
    this.store.dispatch(
      ControlNumberConfigActions.clearControlNumberStatusError()
    )
  }

  public resetControlNumberConfigState(): void {
    this.store.dispatch(
      ControlNumberConfigActions.resetControlNumberConfigState()
    )
  }

  public resetControlNumberStatusState(): void {
    this.store.dispatch(
      ControlNumberConfigActions.resetControlNumberStatusState()
    )
  }
}
