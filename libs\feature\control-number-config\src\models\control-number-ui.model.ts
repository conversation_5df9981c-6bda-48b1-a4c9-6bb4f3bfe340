export enum ControlNumberStatus {
  NotStarted = 'NotStarted',
  InProgress = 'InProgress',
  Completed = 'Completed',
  Error = 'Error',
}

export interface ControlNumberSettingsModel {
  prefix?: string
  startNumber?: number
  endNumber?: number
}

export interface TreeModel extends ControlNumberSettingsModel {
  id?: string
  parentId?: string
  custodianId?: number
  custodianName?: string
  isCustodian?: boolean
  mediaId?: number
  mediaName?: string
  hasWarning?: boolean
  warningMessage?: string
  isEditable?: boolean
  isDisabled?: boolean
}

export interface ControlNumberStatusModel extends TreeModel {
  jobId?: number
  status?: ControlNumberStatus
  startDate?: Date
  endDate?: Date
  errorMessage?: string
  isJobRoot?: boolean
}
