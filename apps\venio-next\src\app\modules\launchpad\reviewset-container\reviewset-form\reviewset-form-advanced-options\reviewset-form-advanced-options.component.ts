import { Component, computed, inject, input } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms'
import { ReviewSetForm } from '@venio/shared/models/interfaces'
import {
  CheckBoxComponent,
  CheckBoxDirective,
  NumericTextBoxComponent,
} from '@progress/kendo-angular-inputs'
import { DropDownListComponent } from '@progress/kendo-angular-dropdowns'
import { LabelComponent } from '@progress/kendo-angular-label'
import { ReviewsetFormService } from '../reviewset-form.service'
import {
  PopoverAnchorDirective,
  PopoverBodyTemplateDirective,
  PopoverComponent,
} from '@progress/kendo-angular-tooltip'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

interface OptionItem {
  controlName: string
  label: string
}

@Component({
  selector: 'venio-reviewset-form-advanced-options',
  standalone: true,
  imports: [
    CommonModule,
    CheckBoxComponent,
    CheckBoxDirective,
    DropDownListComponent,
    LabelComponent,
    NumericTextBoxComponent,
    FormsModule,
    ReactiveFormsModule,
    SvgLoaderDirective,
    PopoverComponent,
    PopoverBodyTemplateDirective,
    PopoverAnchorDirective,
  ],
  templateUrl: './reviewset-form-advanced-options.component.html',
  styleUrl: './reviewset-form-advanced-options.component.scss',
})
export class ReviewsetFormAdvancedOptionsComponent {
  public readonly reviewSetForm = input.required<FormGroup<ReviewSetForm>>()

  public readonly infoSvgUrl = 'assets/svg/material_info_outline.svg'

  private reviewSetFormService = inject(ReviewsetFormService)

  public readonly documentCount = computed(
    () => this.reviewSetFormService.samplingCount()?.sampleSize || 0
  )

  public readonly populationCount = computed(
    () => this.reviewSetFormService.samplingCount()?.populationSize || 0
  )

  public tagPropagateOptions: OptionItem[] = [
    {
      controlName: 'propagateTagPCSet',
      label: 'Propagate tag to family (parent/child)',
    },
    {
      controlName: 'tagPropagationRule',
      label: 'Propagate tag to all duplicates',
    },
    {
      controlName: 'propagateTagEmailThread',
      label: 'Propagate tag to email thread',
    },
  ]

  public includeOptions: OptionItem[] = [
    {
      controlName: 'parentChildIncluded',
      label: 'Include all documents in family (parent/child)',
    },
    {
      controlName: 'msgThreadIncluded',
      label:
        'Include all documents that are part of email thread in selected document source',
    },
  ]

  public excludeOptions: OptionItem[] = [
    {
      controlName: 'excludePrevReviewSetDoc',
      label:
        'Exclude documents that are already part of other review set in this case',
    },
    {
      controlName: 'excludeNonInclusiveEmails',
      label: 'Exclude non-inclusive emails',
    },
  ]

  public reviewPropagateOptions: OptionItem[] = [
    {
      controlName: 'propagateReviewPCSet',
      label: 'Propagate review set status to family (parent/child)',
    },
    {
      controlName: 'reviewDuplicatePropagationRule',
      label: 'Propagate review set to all duplicates',
    },
    {
      controlName: 'propagateReviewEmailThread',
      label: 'Propagate review set to email thread',
    },
  ]

  public controlSetOptions = [
    {
      value: 0,
      label: 'Normal Distribution',
    },
    {
      value: 1,
      label: 'Percentage Of Population',
    },
    {
      value: 2,
      label: 'Number Of Documents',
    },
  ]

  public controlSetFormatOptions = [
    {
      value: false,
      label: 'Fixed',
    },
    {
      value: true,
      label: 'Dynamic',
    },
  ]
}
