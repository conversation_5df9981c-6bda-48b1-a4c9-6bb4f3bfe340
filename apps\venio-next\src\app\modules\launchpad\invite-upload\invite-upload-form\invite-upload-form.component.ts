import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
  On<PERSON><PERSON><PERSON>,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'
import {
  InviteUploadForm,
  UsersListModel,
} from '@venio/shared/models/interfaces'
import { filter, Subject, takeUntil } from 'rxjs'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { EditorModule } from '@progress/kendo-angular-editor'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { cloneDeep } from 'lodash'
import { UserFacade } from '@venio/data-access/common'
import { plusIcon } from '@progress/kendo-svg-icons'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { SkeletonModule } from '@progress/kendo-angular-indicators'
import { toSignal } from '@angular/core/rxjs-interop'
import { GridTypes, UserGridColumns } from '@venio/shared/models/constants'
import { InviteUploadUserGridComponent } from '../invite-upload-user-grid/invite-upload-user-grid.component'
import { InviteUploadEditorComponent } from '../invite-upload-editor/invite-upload-editor.component'
import { InviteUploadLocalState } from '../invite-upload-container/invite-upload-local-state'
import { ControlSettingService } from '@venio/data-access/control-settings'

@Component({
  selector: 'venio-invite-upload-form',
  standalone: true,
  imports: [
    CommonModule,
    InputsModule,
    ButtonsModule,
    EditorModule,
    DropDownsModule,
    LabelModule,
    SkeletonModule,
    ReactiveFormsModule,
    InviteUploadUserGridComponent,
    InviteUploadEditorComponent,
  ],
  templateUrl: './invite-upload-form.component.html',
  styleUrl: './invite-upload-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InviteUploadFormComponent implements OnInit, OnDestroy {
  public inviteUploadForm = input.required<FormGroup<InviteUploadForm>>()

  public selectedProjectId = input.required<number>()

  private readonly inviteUploadLocalState = inject(InviteUploadLocalState)

  private readonly userFacade = inject(UserFacade)

  private readonly controlSettingService = inject(ControlSettingService)

  public readonly GridTypes = GridTypes

  public readonly allowExternalUserUpload = computed(
    () =>
      this.controlSettingService.getControlSetting
        .ALLOW_EXTERNAL_USER_DATA_UPLOAD
  )

  public readonly invitationInProgress = toSignal(
    this.userFacade.selectIsInvitationInProgress$,
    {
      initialValue: false,
    }
  )

  public readonly shareToExternalUsers =
    this.inviteUploadLocalState.shareToExternalUsers

  public internalUsers: UsersListModel[] = []

  public externalUsers = this.inviteUploadLocalState.externalUsers

  public hasNoUsersSelected = this.inviteUploadLocalState.hasNoUsersSelected

  public readonly plusIcon = plusIcon

  private toDestroy$: Subject<void> = new Subject<void>()

  public ngOnInit(): void {
    this.#initializeUserGridColumns()
    this.#handleShareToExternalUsersChange()
    this.#fetchInternalUsers()
    this.#fetchExternalUsers()
    this.#selectInternalUserList()
    this.#selectExternalUserList()
  }

  #handleShareToExternalUsersChange(): void {
    this.inviteUploadForm()
      .get('shareToExternalUsers')
      ?.valueChanges.pipe(takeUntil(this.toDestroy$))
      .subscribe((value: boolean): void => {
        this.shareToExternalUsers.set(value)
        if (value) {
          this.inviteUploadForm().get('newEmail')?.enable()
        } else {
          this.inviteUploadForm().get('newEmail')?.disable()
        }
      })
  }

  #initializeUserGridColumns(): void {
    this.inviteUploadLocalState.setGridColumns(UserGridColumns)
  }

  #fetchInternalUsers(): void {
    this.userFacade.fetchUserListToInvite(this.selectedProjectId())
  }

  #fetchExternalUsers(): void {
    this.userFacade.fetchExternalUserListToInvite()
  }

  #selectInternalUserList(): void {
    this.userFacade.selectUserListToInviteSuccessResponse$
      .pipe(
        filter((success) => Boolean(success)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((success) => {
        const internalUsers = success || []
        this.internalUsers = cloneDeep(internalUsers)
        this.inviteUploadLocalState.setFilteredInternalUsers(internalUsers)
      })
  }

  #selectExternalUserList(): void {
    this.userFacade.selectExternalUserToInviteSuccessResponse$
      .pipe(
        filter((success) => Boolean(success)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((success) => {
        const externalUsers = success || []
        this.externalUsers.set(cloneDeep(externalUsers))
        this.inviteUploadLocalState.setFilteredExternalUsers(externalUsers)
      })
  }

  public onInternalUserFilter(searchInput: string): void {
    const filterValue = searchInput.toLowerCase().trim()
    let filteredInternalUsers = cloneDeep(this.internalUsers)
    if (filterValue)
      filteredInternalUsers = filteredInternalUsers.filter(
        (user: UsersListModel) => user.email.toLowerCase().includes(filterValue)
      )
    this.inviteUploadLocalState.setFilteredInternalUsers(filteredInternalUsers)
  }

  public onExternalUserFilter(searchInput: string): void {
    const filterValue = searchInput.toLowerCase().trim()
    let filteredExternalUsers = cloneDeep(this.externalUsers())
    if (filterValue)
      filteredExternalUsers = filteredExternalUsers.filter(
        (user: UsersListModel) => user.email.toLowerCase().includes(filterValue)
      )
    this.inviteUploadLocalState.setFilteredExternalUsers(filteredExternalUsers)
  }

  public addExternalUser(): void {
    this.inviteUploadLocalState.setUserMessage('')
    const emailControl = this.inviteUploadForm().get('newEmail')
    emailControl?.markAsTouched()
    if (emailControl?.invalid) {
      return
    }
    const newEmail = emailControl?.value?.trim().toLowerCase() ?? ''
    emailControl?.setValue(newEmail)

    if (!newEmail) {
      emailControl.setErrors({ required: true })
      return
    }

    const newUser: UsersListModel = {
      email: newEmail,
      userName: newEmail,
    }
    this.inviteUploadLocalState.addFilteredExternalUser(newUser)
    emailControl?.setValue('')
    emailControl.markAsPristine()
    emailControl.setErrors(null)
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
