import { ComponentFixture, TestBed } from '@angular/core/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { AiFacade } from '@venio/data-access/ai'
import {
  DocumentsFacade,
  SearchFacade,
  SearchResponseModel,
} from '@venio/data-access/review'
import { BehaviorSubject } from 'rxjs'

import { SummaryComponent } from './summary.component'

describe('SummaryComponent', () => {
  let component: SummaryComponent
  let fixture: ComponentFixture<SummaryComponent>

  const mockAiFacade = {
    selectEciTotalDocuments$: new BehaviorSubject(0),
    selectEciSelectedDocuments$: new BehaviorSubject(0),
    selectEcaRelevanceSuccess$: new BehaviorSubject(null),
  } satisfies Partial<AiFacade>

  const mockDocumentsFacade = {
    getSelectedDocuments$: new BehaviorSubject([]),
    getIsBatchSelected$: new BehaviorSubject(false),
    getUnselectedDocuments$: new BehaviorSubject([]),
  } satisfies Partial<DocumentsFacade>

  const mockSearchFacade = {
    getSearchResponse$: new BehaviorSubject<SearchResponseModel>({
      tempTables: {
        searchResultTempTable: 'testTable',
        baseGUID: 'test-guid',
        computedSearchTempTable: 'computed-table',
        savedSearchTempTable: 'saved-table',
        searchGuid: 'search-guid',
        searchId: 123,
        userTempTable: 'user-table',
      },
      error: null,
      searchResultIntialParameters: null,
    }),
    getTotalHitCount$: new BehaviorSubject(0),
  } satisfies Partial<SearchFacade>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SummaryComponent],
      providers: [
        provideMockStore({}),
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: AiFacade, useValue: mockAiFacade },
        { provide: DocumentsFacade, useValue: mockDocumentsFacade },
        { provide: SearchFacade, useValue: mockSearchFacade },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(SummaryComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
