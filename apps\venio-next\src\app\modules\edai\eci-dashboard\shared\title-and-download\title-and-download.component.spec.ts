import { ComponentFixture, TestBed } from '@angular/core/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

import { TitleAndDownloadComponent } from './title-and-download.component'

describe('TitleAndDownloadComponent', () => {
  let component: TitleAndDownloadComponent
  let fixture: ComponentFixture<TitleAndDownloadComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TitleAndDownloadComponent],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(TitleAndDownloadComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
