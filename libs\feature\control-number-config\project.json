{"name": "control-number-config", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/feature/control-number-config/src", "prefix": "venio", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/feature/control-number-config/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/feature/control-number-config/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/feature/control-number-config/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/feature/control-number-config/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}