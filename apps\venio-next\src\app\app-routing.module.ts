import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'
import { CaptureTokenAndRedirect } from './capture-token-and-redirect'

import { userProjectRightResolver } from './resolvers/user-project-rights.resolver'

const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import(
        './modules/layout/layout-container/layout-container.component'
      ).then(({ LayoutContainerComponent }) => LayoutContainerComponent),
    children: [
      { path: '', redirectTo: '/launchpad', pathMatch: 'full' },
      {
        path: 'documents',
        loadChildren: () =>
          import('./modules/documents/documents-module.module').then(
            ({ DocumentsModuleModule }) => DocumentsModuleModule
          ),
      },
      {
        path: 'launchpad',
        loadComponent: () =>
          import('./modules/launchpad/launchpad-container.component').then(
            ({ LaunchpadContainerComponent }) => LaunchpadContainerComponent
          ),
      },
      // TODO: we'll use this in near future:
      // @see https://github.com/nrwl/nx/issues/19121
      // and then we need to set "devRemotes": ["advanced-search"] project.json
      // for live serve, otherwise it will first builds it and serves as an static
      // which might not reload on changes
      // {
      //   path: 'advanced-search',
      //   loadChildren: () =>
      //     import('advanced-search/Routes').then((m) => m.remoteRoutes),
      // },
      // {
      //   path: 'advanced-search',
      //   loadChildren: () =>
      //     loadRemoteModule('advanced-search', './Routes').then(
      //       (m) => m.remoteRoutes
      //     ),
      // },
      {
        path: 'ui-demo',
        loadChildren: () =>
          import('./modules/ui-demo/ui-demo-module.module').then(
            ({ UiDemoModuleModule }) => UiDemoModuleModule
          ),
      },
      {
        path: 'document-detail',
        loadChildren: () =>
          import('./modules/document-utility/document-utility.module').then(
            ({ DocumentUtilityModule }) => DocumentUtilityModule
          ),
      },
      {
        path: 'reports',
        loadComponent: () =>
          import('./modules/reports/report-index.component').then(
            ({ ReportIndexComponent }) => ReportIndexComponent
          ),
        children: [
          {
            path: '',
            redirectTo: 'tabular',
            pathMatch: 'full',
          },
          {
            path: 'tabular',
            loadComponent: () =>
              import(
                './modules/reports/tabular/report-container/report-container.component'
              ).then(
                ({ ReportContainerComponent }) => ReportContainerComponent
              ),
          },
          // TODO: more reports in future, such as chats, graphs, etc.
        ],
      },
      {
        path: 'quick-links',
        children: [
          {
            path: 'control-number-config',
            loadComponent: () =>
              import('@venio/feature/control-number-config').then(
                ({ ControlNumberContainerComponent }) =>
                  ControlNumberContainerComponent
              ),
          },
        ],
      },
    ],
  },
  {
    path: 'review',
    loadChildren: () =>
      import('./modules/document-utility/document-utility.module').then(
        ({ DocumentUtilityModule }) => DocumentUtilityModule
      ),
    resolve: {
      userRightsData: userProjectRightResolver,
    },
  },
  {
    path: 'viewer',
    loadChildren: () =>
      import(
        './modules/viewers/viewer-container/viewer-main-container.component'
      ).then(({ ViewerContainerModule }) => ViewerContainerModule),
    resolve: {
      userRightsData: userProjectRightResolver,
    },
  },
  {
    path: 'tags',
    loadChildren: () =>
      import('./modules/document-utility/tag-coding/tag-coding.module').then(
        ({ TagCodingModule }) => TagCodingModule
      ),
    resolve: {
      userRightsData: userProjectRightResolver,
    },
  },
  {
    path: 'utility-panel',
    loadChildren: () =>
      import(
        './modules/document-utility/document-utility-panel/document-utility-panel.module'
      ).then(({ DocumentUtilityPanelModule }) => DocumentUtilityPanelModule),
    resolve: {
      userRightsData: userProjectRightResolver,
    },
  },
  {
    path: 'html-viewer',
    loadChildren: () =>
      import('@venio/feature/near-native').then(
        ({ HtmlViewerModule }) => HtmlViewerModule
      ),
  },
  /**
   * TODO:Remove the below route once we have proper solution to navigate route in iframe in html viewer.
   */
  {
    path: 'html_viewer',
    loadChildren: () =>
      import('@venio/feature/near-native').then(
        ({ HtmlViewerModule }) => HtmlViewerModule
      ),
  },
  {
    path: 'login',
    loadComponent: () =>
      import('./modules/auth/login-container/login-container.component').then(
        ({ LoginContainerComponent }) => LoginContainerComponent
      ),
  },
  {
    path: 'reset-password',
    loadComponent: () =>
      import(
        './modules/auth/forgot-password-container/forgot-password-container.component'
      ).then(
        ({ ForgotPasswordContainerComponent }) =>
          ForgotPasswordContainerComponent
      ),
  },
  {
    path: 'external-user-login',
    loadComponent: () =>
      import(
        './modules/auth/external-user-login-container/external-user-login-container.component'
      ).then(
        ({ ExternalUserLoginContainerComponent }) =>
          ExternalUserLoginContainerComponent
      ),
  },
  { path: '**', redirectTo: '/404', pathMatch: 'full' },
]

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      initialNavigation: 'enabledBlocking',
      onSameUrlNavigation: 'reload',
      bindToComponentInputs: true,
    }),
  ],
  exports: [RouterModule],
  providers: [CaptureTokenAndRedirect],
})
export class AppRoutingModule {}
