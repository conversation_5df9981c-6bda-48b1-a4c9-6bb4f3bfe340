import {
  ChangeDetectionStrategy,
  Component,
  OnD<PERSON>roy,
  OnInit,
  Type,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { Subject, combineLatest, filter, take, takeUntil } from 'rxjs'
import { DialogRef, DialogService } from '@progress/kendo-angular-dialog'
import {
  DocumentsFacade,
  InitialSearchResultParameter,
  SearchFacade,
} from '@venio/data-access/review'
import { DocumentMenuType } from '@venio/shared/models/constants'
import {
  NotificationDialogComponent,
  VenioNotificationService,
} from '@venio/feature/notification'

@Component({
  selector: 'venio-move-to-parent-container',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './move-to-parent-container.component.html',
  styleUrl: './move-to-parent-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MoveToParentContainerComponent implements OnInit, OnD<PERSON>roy {
  private readonly toD<PERSON>roy$ = new Subject<void>()

  private dialogRef: DialogRef

  constructor(
    private documentsFacade: DocumentsFacade,
    private searchFacade: SearchFacade,
    private dialogService: DialogService,
    private notification: VenioNotificationService
  ) {}

  public ngOnInit(): void {
    this.#selectedDocumentEvent()
  }

  /**
   * When the dialog is closed, it is no point to keep the menu event state
   * @returns {void}
   */
  #resetMenuEventState(): void {
    this.documentsFacade.resetDocumentState('menuEventPayload')
  }

  /**
   * When a menu link is clicked, the loading indicator is displayed to
   * indicate the user that there is something happening in the background
   * such as loading the dialog component lazily and then once loaded, turn off the indicator
   * @returns {void}
   */
  #resetMenuLoadingState(): void {
    this.documentsFacade.resetDocumentState('isDocumentMenuLoading')
  }

  /**
   * Handle the dialog close event such as cleanup or resetting the state
   * @returns {void}
   */
  #handleEditDialogCloseEvent(): void {
    this.dialogRef.dialog.onDestroy(() => {
      this.#resetMenuEventState()
      // maybe more cleanup or event trigger if required.
    })
  }

  #launchDialogContent(dialogContent: Type<unknown>): void {
    this.dialogRef = this.dialogService.open({
      content: dialogContent,
      maxHeight: '500px',
      maxWidth: '950px',
      minWidth: '700px',
      width: '70%',
    })

    this.dialogRef.result.subscribe((result: { isRefreshNeeded: boolean }) => {
      // if document has been deleted then refresh the search results
      if (result.isRefreshNeeded) {
        this.searchFacade.doSearch()
      }
    })
  }

  /**
   * Load the dialog component lazily and open it
   * @returns {void}
   */
  #handleLazyLoadedDialog(): void {
    import('@venio/feature/move-to-parent').then((d) => {
      // reset the loading indicator
      this.#resetMenuLoadingState()

      // launch the dialog
      this.#launchDialogContent(d.MoveToParentComponent)

      // once the dialogRef instance is created
      this.#handleEditDialogCloseEvent()
    })
  }

  /**
   * Select the document menu event state when it is triggered
   * @returns {void}
   */
  #selectedDocumentEvent(): void {
    combineLatest([
      this.documentsFacade.selectDocumentMenuEvent$,
      this.documentsFacade.getIsBatchSelected$,
      this.documentsFacade.getSelectedDocuments$,
      this.documentsFacade.getUnselectedDocuments$,
      this.searchFacade.getSearchInitialParameters$,
    ])
      .pipe(
        filter(([event]) => event === DocumentMenuType.MOVE),
        takeUntil(this.toDestroy$)
      )
      .subscribe(
        ([_, isBatchSelected, selectedDocs, unselectedDocs, searchParams]) => {
          let selectedDocuments = 0
          selectedDocuments = this.getSelectedDocumentCount(
            isBatchSelected,
            selectedDocuments,
            searchParams,
            unselectedDocs,
            selectedDocs
          )

          if (selectedDocuments === 0) {
            this.#showNotificationMessage()
          } else {
            // launch the dialog
            this.#handleLazyLoadedDialog()
          }
        }
      )
  }

  #showNotificationMessage(): void {
    const notificationDialogRef = this.dialogService.open({
      content: NotificationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-warning',
      width: '35rem',
    })

    this.#setDialogInput(notificationDialogRef.content.instance)

    notificationDialogRef.result
      .pipe(
        filter((result) => typeof result === 'boolean' && result === true),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.#resetMenuLoadingState()
        this.#resetMenuEventState()
      })
  }

  #setDialogInput(instance: NotificationDialogComponent): void {
    instance.title = 'Move To Parent'
    instance.message = `Please select at least one document to move to parent`
  }

  private getSelectedDocumentCount(
    isBatchSelected: boolean,
    selectedDocuments: number,
    searchParams: InitialSearchResultParameter,
    unselectedDocs: number[],
    selectedDocs: number[]
  ): number {
    if (isBatchSelected) {
      selectedDocuments = searchParams.totalHitCount - unselectedDocs.length
    } else {
      selectedDocuments = selectedDocs.length
    }
    return selectedDocuments
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetMenuEventState()
  }
}
