import {
  ChangeDetectionStrategy,
  ChangeDetector<PERSON><PERSON>,
  <PERSON>mponent,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  ViewChild,
} from '@angular/core'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  DialogContentBase,
  DialogRef,
  DialogsModule,
} from '@progress/kendo-angular-dialog'
import {
  LayoutModule,
  SelectEvent,
  TabStripComponent,
} from '@progress/kendo-angular-layout'
import { CustomFolderComponent } from '../custom-folder/custom-folder.component'
import { DynamicFolderComponent } from '../dynamic-folder/dynamic-folder.component'
import { AutoFolderComponent } from '../auto-folder/auto-folder.component'
import {
  DynamicFolderFacade,
  FolderFacade,
  ReviewSetStateService,
  UserRights,
} from '@venio/data-access/review'
import { NotificationModule } from '@progress/kendo-angular-notification'
import { Subject } from 'rxjs'
import { FolderTabType } from '@venio/shared/models/constants'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { CommonModule } from '@angular/common'
import {
  UserGroupRightCheckDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-folder-dialog',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    LayoutModule,
    CustomFolderComponent,
    DynamicFolderComponent,
    AutoFolderComponent,
    NotificationModule,
    IndicatorsModule,
    UserGroupRightCheckDirective,
    SvgLoaderDirective,
  ],
  templateUrl: './folder-dialog.component.html',
  styleUrls: ['./folder-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FolderDialogComponent
  extends DialogContentBase
  implements OnInit, OnDestroy
{
  @ViewChild(CustomFolderComponent, { static: false })
  public customFolderComponent: CustomFolderComponent

  @ViewChild(DynamicFolderComponent, { static: false })
  public dynamicFolderComponent: DynamicFolderComponent

  @ViewChild(AutoFolderComponent, { static: false })
  public autoFolderComponent: AutoFolderComponent

  @ViewChild('mainTabStrip')
  public mainTabStrip: TabStripComponent

  public FolderTabType = FolderTabType

  public folderTabType: FolderTabType = FolderTabType.CUSTOM

  public saveTitle = 'CREATE'

  private unsubscribed$: Subject<void> = new Subject<void>()

  public isCreateFolderLoading = this.folderFacade.getIsCreateFolderLoading$

  public UserRights = UserRights

  constructor(
    public dialog: DialogRef,
    private folderFacade: FolderFacade,
    private dynamicFolderFacade: DynamicFolderFacade,
    private cdr: ChangeDetectorRef,
    public reviewSetState: ReviewSetStateService
  ) {
    super(dialog)
  }

  public ngOnInit(): void {
    this.#activateInitialTab()
  }

  #activateInitialTab(): void {
    setTimeout(() => {
      this.cdr.markForCheck()
      this.mainTabStrip.selectTab(0)
      this.#resetControlByTitle(this.mainTabStrip.tabs.first.title)
    })
  }

  public ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
    this.folderFacade.clearMessage()
    this.dynamicFolderFacade.clearDynamicFolderMessage()
  }

  public close(): void {
    this.dialog.close()
  }

  public save(): void {
    if (this.folderTabType === FolderTabType.CUSTOM) {
      this.customFolderComponent.createCustomFolder()
    } else if (this.folderTabType === FolderTabType.SYSTEM) {
      this.dynamicFolderComponent.saveDynamicFolder()
    } else if (this.folderTabType === FolderTabType.AUTO) {
      this.autoFolderComponent.createAutoFolder()
    }
  }

  public onTabSelect(e: SelectEvent): void {
    this.#resetControlByTitle(e.title)
  }

  #resetControlByTitle(title: string): void {
    if (title === 'Custom Folder') {
      this.folderTabType = FolderTabType.CUSTOM
      this.saveTitle = 'CREATE'
    } else if (title === 'Auto Folder') {
      this.folderTabType = FolderTabType.AUTO
      this.saveTitle = 'AUTO-FOLDER'
    } else {
      this.folderTabType = FolderTabType.SYSTEM
      this.saveTitle = 'SAVE'
    }
  }
}
