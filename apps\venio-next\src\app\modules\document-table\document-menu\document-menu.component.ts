import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostListener,
  OnDestroy,
  OnInit,
  ViewChild,
  ViewEncapsulation,
  computed,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { chevronDownIcon, gearIcon, SVGIcon } from '@progress/kendo-svg-icons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { DocumentMenuType } from '@venio/shared/models/constants'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { PopoverModule, TooltipsModule } from '@progress/kendo-angular-tooltip'
import { RadioButtonModule } from '@progress/kendo-angular-inputs'
import { ReactiveFormsModule } from '@angular/forms'
import {
  SvgLoaderDirective,
  UserGroupRightCheckDirective,
} from '@venio/feature/shared/directives'
import {
  DocumentsFacade,
  EmailThreadVisibleType,
  ReviewFacade,
  ReviewViewType,
  UserRights,
  InclusiveEmailStatus,
  CaseInfoFacade,
  SearchFacade,
  ReviewSetStateService,
  ReviewsetFacade,
  MarkAsReviewedRequestModel,
  SearchResultFacade,
  ReviewParamService,
} from '@venio/data-access/review'
import {
  Subject,
  filter,
  takeUntil,
  switchMap,
  of,
  take,
  combineLatest,
} from 'rxjs'
import { toSignal } from '@angular/core/rxjs-interop'
import { HttpErrorResponse } from '@angular/common/http'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { ConfirmationDialogService } from '../../../services/confirmation-dialog-service'
import { VenioNotificationService } from '@venio/feature/notification'
import { DocumentTagUtilityService } from '../../document-utility/utility-services/document-tag-utility'
import { PopupModule } from '@progress/kendo-angular-popup'
import { ControlSettingService } from '@venio/data-access/control-settings'
import { IframeMessengerService } from '@venio/data-access/iframe-messenger'

interface DocumentMenuItem {
  text: string
  type: DocumentMenuType
  svgIcon?: string
  allowedPermission?: UserRights | UserRights[]
  isAnyOfThese?: boolean
  applyEffectsTo?: 'both' | 'stroke' | 'fill'
  x?: string
  y?: string
  visibleInEmailThreadViewOnly?: boolean
  disabled?: boolean
}
@Component({
  selector: 'venio-document-menu',
  standalone: true,
  imports: [
    CommonModule,
    ButtonsModule,
    IconsModule,
    LoaderModule,
    PopoverModule,
    RadioButtonModule,
    ReactiveFormsModule,
    SvgLoaderDirective,
    TooltipsModule,
    UserGroupRightCheckDirective,
    PopupModule,
  ],
  templateUrl: './document-menu.component.html',
  styleUrls: ['./document-menu.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class DocumentMenuComponent implements OnInit, OnDestroy {
  private toDestroy$: Subject<void> = new Subject<void>()

  public gearSVG: SVGIcon = gearIcon

  public chevDown: SVGIcon = chevronDownIcon

  public readonly documentMenuType = DocumentMenuType

  public reviewViewType = ReviewViewType.Search

  public isShowingInclusiveEmailsOnly = false

  public hasInclusiveEmailJobRun = false

  public isTranscriptViewerEnabled = false

  public isImageTypePdf = false

  /**
   * Returns the document menu items based on the review view type.
   * If the review view type is EmailThread, then it will include the email thread view menu items along with other items.
   * If review type is Search, then email thread related items will be excluded.
   */
  public get documentMenuItems(): Array<DocumentMenuItem> {
    this.#updateSwitchViewMenuText()
    this.#updateShowInclusiveEmailMenuText()

    if (this.isTranscriptViewerEnabled && !this.transcriptItemExists()) {
      this._documentMenuItems.push({
        text: DocumentMenuType.TRANSCRIPT,
        type: DocumentMenuType.TRANSCRIPT,
        svgIcon: 'assets/svg/icon-gear-transcript-menu.svg',
        applyEffectsTo: 'fill',
        x: '0.8rem',
        y: '0.8rem',
        allowedPermission: [UserRights.ALLOW_TO_VIEW_TRANSCRIPT],
      })
    } else if (!this.isTranscriptViewerEnabled && this.transcriptItemExists()) {
      this.removeTranscriptItem()
    }

    if (this.isImageTypePdf && !this.bulkRedactItemExists()) {
      this._documentMenuItems.push({
        text: DocumentMenuType.BULK_REDACT,
        type: DocumentMenuType.BULK_REDACT,
        svgIcon: 'assets/svg/icon-bulk-redact-gear.svg',
        applyEffectsTo: 'stroke',
        x: '0.8rem',
        y: '0.8rem',
      })
    } else if (!this.isImageTypePdf && this.bulkRedactItemExists()) {
      this.removeBulkRedactItem()
    }

    if (
      this.controlSettingService.getControlSetting.ENABLE_ENTITY_EXTRACTION &&
      !this.entityExtractionItemExists()
    ) {
      this._documentMenuItems.push({
        text: DocumentMenuType.ENTITY_EXTRACTION,
        type: DocumentMenuType.ENTITY_EXTRACTION,
        svgIcon: 'assets/svg/icon-entity-extraction.svg',
        applyEffectsTo: 'stroke',
        x: '0.8rem',
        y: '0.8rem',
        allowedPermission: [
          UserRights.ALLOW_TO_QUEUE_DOCUMENT_FOR_ENTITY_EXTRACTION,
        ],
      })
    }

    if (this.reviewViewType === ReviewViewType.EmailThread)
      // in email thread view, show only menu items related to email thread view along with `switch to ..` and `export to file` menu
      // done according to discussion on grooming meeting (11 April 2024). might need to change later.
      return this._documentMenuItems.filter(
        (item) =>
          item.visibleInEmailThreadViewOnly ||
          item.type === DocumentMenuType.SWITCH_VIEW ||
          item.type === DocumentMenuType.EXPORT_TO_FILE
      )

    if (this.reviewSetState.isBatchReview()) this.showOnlySelectedMenuItems()

    // in normal view, show all menu items except email thread view related menu items
    return this._documentMenuItems.filter(
      (item) => !item.visibleInEmailThreadViewOnly
    )
  }

  // Function to check if the TRANSCRIPT item exists
  private transcriptItemExists(): boolean {
    return this._documentMenuItems.some(
      (item) => item.type === DocumentMenuType.TRANSCRIPT
    )
  }

  // Function to check if the Bulk Redact item exists
  private bulkRedactItemExists(): boolean {
    return this._documentMenuItems.some(
      (item) => item.type === DocumentMenuType.BULK_REDACT
    )
  }

  // Function to check if the Entity Extraction item exists
  private entityExtractionItemExists(): boolean {
    return this._documentMenuItems.some(
      (item) => item.type === DocumentMenuType.ENTITY_EXTRACTION
    )
  }

  private removeBulkRedactItem(): void {
    const indexToRemove = this._documentMenuItems.findIndex(
      (item) => item.type === DocumentMenuType.BULK_REDACT
    )
    if (indexToRemove > -1) {
      this._documentMenuItems.splice(indexToRemove, 1)
    }
  }

  private removeTranscriptItem(): void {
    const indexToRemove = this._documentMenuItems.findIndex(
      (item) => item.type === DocumentMenuType.TRANSCRIPT
    )
    if (indexToRemove > -1) {
      this._documentMenuItems.splice(indexToRemove, 1)
    }
  }

  // Please uncomment commented menu items once the functionality is ready or implemented.
  public _documentMenuItems: Array<DocumentMenuItem> = [
    /**
     * Edit menu item: Allows users to edit tags and custom fields on a document.
     * Requires any of the following permissions: manage tag, manage custom field, add, delete, or edit tag.
     */
    {
      text: DocumentMenuType.EDIT,
      type: DocumentMenuType.EDIT,
      svgIcon: 'assets/svg/icon-action-grid-pencil.svg',
      applyEffectsTo: 'fill',
      x: '1.2rem',
      y: '0.7rem',
      allowedPermission: [
        UserRights.ALLOW_TO_MANAGE_TAG,
        UserRights.ALLOW_TO_MANAGE_CUSTOMFIELD,
        UserRights.ADD_NEW_TAG,
        UserRights.DELETE_TAG,
        UserRights.EDIT_EXISTING_TAG,
      ],
      // if you need to assert with all no need to set this prop as by default, it is false
      // and asserts on all.
      isAnyOfThese: true,
    },
    /**
     * Move menu item: Allows users to move documents to a different location or folder.
     * Requires permission to move files.
     */
    {
      text: DocumentMenuType.MOVE,
      type: DocumentMenuType.MOVE,
      svgIcon: 'assets/svg/icon-move.svg',
      applyEffectsTo: 'stroke',
      x: '1.2rem',
      y: '0.8rem',
      allowedPermission: [UserRights.ALLOW_TO_MOVE_FILES],
    },
    /**
     * Delete menu item: Allows users to delete a document from the system.
     * Requires permission to delete documents.
     */
    {
      text: DocumentMenuType.DELETE,
      type: DocumentMenuType.DELETE,
      svgIcon: 'assets/svg/Icon-material-delete.svg',
      applyEffectsTo: 'fill',
      x: '0.8rem',
      y: '0.8rem',
      allowedPermission: [UserRights.ALLOW_TO_DELETE_DOCUMENT],
    },
    /**
     * Replace menu item: Allows users to replace the content of a document in bulk coding scenarios.
     * Requires permission to enable or disable bulk coding.
     */
    {
      text: DocumentMenuType.REPLACE,
      type: DocumentMenuType.REPLACE,
      svgIcon: 'assets/svg/icon-replace.svg',
      applyEffectsTo: 'fill',
      x: '0.8rem',
      y: '0.8rem',
      allowedPermission: [UserRights.ALLOW_ENABLE_DISABLE_BULK_CODING],
    },
    /**
     * Tally menu item: Provides access to document tallying/statistics features.
     * No specific permissions required.
     */
    {
      text: DocumentMenuType.TALLY,
      type: DocumentMenuType.TALLY,
      svgIcon: 'assets/svg/icon-percentage.svg',
      applyEffectsTo: 'fill',
      x: '0.8rem',
      y: '0.8rem',
    },
    /**
     * Convert menu item: Allows users to convert documents to other formats (TIFF, OCR, HTML, etc.).
     * Requires any of the listed conversion-related permissions.
     */
    {
      text: DocumentMenuType.CONVERT,
      type: DocumentMenuType.CONVERT,
      svgIcon: 'assets/svg/icon-double-arrow-right.svg',
      applyEffectsTo: 'fill',
      x: '0.8rem',
      y: '0.8rem',
      allowedPermission: [
        UserRights.ALLOW_TO_BULK_TIFF,
        UserRights.ALLOW_TO_LAUNCH_OCR,
        UserRights.ALLOW_TO_LAUNCH_OCRGENERATEDTIFF,
        UserRights.ALLOW_TO_LAUNCH_OCRREDACTEDTIFF,
        UserRights.ALLOW_TO_CONVERT_TO_HTML,
        UserRights.ALLOW_TO_LAUNCH_TRANSCRIBING,
      ],
      isAnyOfThese: true,
    },
    /**
     * Send to EDAI menu item: Allows users to send documents to the EDAI system for further processing.
     * No specific permissions required.
     */
    {
      text: DocumentMenuType.SEND_TO_EDAI,
      type: DocumentMenuType.SEND_TO_EDAI,
      svgIcon: 'assets/svg/icon-discovery-ai.svg',
      applyEffectsTo: 'stroke',
      x: '0.8rem',
      y: '0.8rem',
    },
    /**
     * Share Document menu item: Allows users to share documents with others.
     * Requires permission to share documents.
     */
    {
      text: DocumentMenuType.SHARE_DOCUMENT,
      type: DocumentMenuType.SHARE_DOCUMENT,
      svgIcon: 'assets/svg/share-svgrepo.svg',
      applyEffectsTo: 'stroke',
      x: '0.8rem',
      y: '0.8rem',
      allowedPermission: [UserRights.ALLOW_TO_SHARE_DOCUMENT],
    },
    /**
     * Send to Analyze menu item: Allows users to send documents for analysis (dashboard/statistics).
     * Requires permission to view dashboard.
     */
    {
      text: DocumentMenuType.SEND_TO_ANALYZE,
      type: DocumentMenuType.SEND_TO_ANALYZE,
      svgIcon: 'assets/svg/icon-o-analysis-stats.svg',
      applyEffectsTo: 'stroke',
      x: '0.8rem',
      y: '0.8rem',
      allowedPermission: [UserRights.ALLOW_TO_VIEW_DASHBOARD],
    },
    /**
     * Tags menu item: Allows users to tag or untag documents.
     * Requires permission for tagging/untagging.
     */
    {
      text: DocumentMenuType.TAGS,
      type: DocumentMenuType.TAGS,
      svgIcon: 'assets/svg/icon-tags.svg',
      applyEffectsTo: 'fill',
      x: '0.8rem',
      y: '0.8rem',
      allowedPermission: [UserRights.ALLOW_TAGGING_UNTAGGING],
    },
    /**
     * Production menu item: Allows users to export documents for production.
     * Requires export permission.
     */
    {
      text: DocumentMenuType.PRODUCTION,
      type: DocumentMenuType.PRODUCTION,
      svgIcon: 'assets/svg/icon-file-signature.svg',
      applyEffectsTo: 'fill',
      x: '0.8rem',
      y: '0.8rem',
      allowedPermission: [UserRights.ALLOW_EXPORT],
    },
    /**
     * Print/Download menu item: Allows users to print or download native files.
     * Requires print or download native files permission.
     */
    {
      text: DocumentMenuType.PRINT_DOWNLOAD,
      type: DocumentMenuType.PRINT_DOWNLOAD,
      svgIcon: 'assets/svg/icon-print.svg',
      applyEffectsTo: 'fill',
      x: '0.8rem',
      y: '0.8rem',
      allowedPermission: [
        UserRights.ALLOW_PRINT,
        UserRights.ALLOW_TO_DOWNLOAD_NATIVE_FILES,
      ],
    },
    /**
     * Send/Remove menu item: Allows users to send documents and remove them from a folder.
     * Requires permission to send/remove from folder.
     */
    {
      text: DocumentMenuType.SEND_REMOVE,
      type: DocumentMenuType.SEND_REMOVE,
      svgIcon: 'assets/svg/icon-send-and-remove-folder.svg',
      applyEffectsTo: 'stroke',
      x: '1.3rem',
      y: '0.8rem',
      allowedPermission: [UserRights.ALLOW_TO_SEND_REMOVE_FOLDER],
    },
    /**
     * Foldering menu item: Allows users to create or manage folders for documents.
     * Requires any of the listed folder-related permissions.
     */
    {
      text: DocumentMenuType.FOLDERING,
      type: DocumentMenuType.FOLDERING,
      svgIcon: 'assets/svg/icon-folder-outline.svg',
      applyEffectsTo: 'stroke',
      x: '0.8rem',
      y: '0.8rem',
      allowedPermission: [
        UserRights.ALLOW_TO_CREATE_NEW_FOLDER,
        UserRights.ALLOW_TO_ADD_GLOBAL_DYNAMIC_FOLDER,
        UserRights.ALLOW_TO_ADD_LOCAL_DYNAMIC_FOLDER,
        UserRights.ALLOW_TO_AUTO_FOLDER,
      ],
      isAnyOfThese: true,
    },
    /**
     * Export to File menu item: Allows users to export documents to a file.
     * No specific permissions required.
     */
    {
      text: DocumentMenuType.EXPORT_TO_FILE,
      type: DocumentMenuType.EXPORT_TO_FILE,
      svgIcon: 'assets/svg/icon-export.svg',
      applyEffectsTo: 'fill',
      x: '0.8rem',
      y: '0.8rem',
    },
    /**
     * Switch View menu item: Allows users to switch between normal and email thread views.
     * Requires permission to view message threads.
     */
    {
      text: DocumentMenuType.SWITCH_VIEW,
      type: DocumentMenuType.SWITCH_VIEW,
      svgIcon: 'assets/svg/icon-email.svg',
      applyEffectsTo: 'stroke',
      x: '0.8rem',
      y: '0.8rem',
      allowedPermission: [UserRights.ALLOW_TO_VIEW_MESSAGE_THREAD],
    },
    {
      text: DocumentMenuType.TAG_ALL_INCLUSIVE_EMAIL,
      type: DocumentMenuType.TAG_ALL_INCLUSIVE_EMAIL,
      svgIcon: 'assets/svg/icon-tag.svg',
      applyEffectsTo: 'stroke',
      x: '0.8rem',
      y: '0.8rem',
      allowedPermission: [UserRights.ALLOW_TO_VIEW_MESSAGE_THREAD],
      visibleInEmailThreadViewOnly: true,
    },
    {
      text: DocumentMenuType.TAG_WHOLE_THREAD,
      type: DocumentMenuType.TAG_WHOLE_THREAD,
      svgIcon: 'assets/svg/icon-tag.svg',
      applyEffectsTo: 'stroke',
      x: '0.8rem',
      y: '0.8rem',
      allowedPermission: [UserRights.ALLOW_TO_VIEW_MESSAGE_THREAD],
      visibleInEmailThreadViewOnly: true,
    },
    {
      text: DocumentMenuType.SHOW_INCLUSIVE_EMAIL,
      type: DocumentMenuType.SHOW_INCLUSIVE_EMAIL,
      svgIcon: 'assets/svg/icon-email.svg',
      applyEffectsTo: 'stroke',
      x: '0.8rem',
      y: '0.8rem',
      allowedPermission: [UserRights.ALLOW_TO_VIEW_MESSAGE_THREAD],
      visibleInEmailThreadViewOnly: true,
    },
    /**
     * Responsive PST menu item: Allows users to generate a responsive PST file for emails.
     * Requires permission to generate responsive PST.
     */
    {
      text: DocumentMenuType.RESPONSIVE_PST,
      type: DocumentMenuType.RESPONSIVE_PST,
      svgIcon: 'assets/svg/icon-column-action-PST.svg',
      applyEffectsTo: 'stroke',
      x: '0.8rem',
      y: '0.8rem',
      allowedPermission: [UserRights.ALLOW_TO_GENERATE_RESPONSIVE_PST],
    },
    /**
     * Generate RSMF menu item: Allows users to generate an RSMF (Relativity Short Message Format) file for chat data.
     * Requires permission to generate RSMF.
     */
    {
      text: DocumentMenuType.GENERATE_RSMF,
      type: DocumentMenuType.GENERATE_RSMF,
      svgIcon: 'assets/svg/rsmf-icon.svg',
      applyEffectsTo: 'stroke',
      x: '0.8rem',
      y: '0.8rem',
      allowedPermission: [UserRights.ALLOW_TO_GENERATE_RSMF],
    },
    // Entity Extraction will be conditionally added in the getter
  ]

  public show = false

  public ReviewViewType = ReviewViewType

  public isDocumentMenuLoading = this.documentsFacade.isDocumentMenuLoading$

  @ViewChild('anchor', { read: ElementRef }) public anchor: ElementRef

  @ViewChild('popup', { read: ElementRef }) public popup: ElementRef

  // Batch review computed properties
  public isReviewedIcon = computed(() => {
    const batchInfo = this.reviewSetState.reviewsetBatchInfo()
    if (!batchInfo) return 'assets/svg/icon-document-unreviewed.svg'
    return batchInfo.remainingFiles === 0
      ? 'assets/svg/icon-document-reviewed.svg'
      : 'assets/svg/icon-document-unreviewed.svg'
  })

  public reviewedTooltip = computed(() => {
    const batchInfo = this.reviewSetState.reviewsetBatchInfo()
    if (!batchInfo) return 'Mark Selected as Reviewed'
    return batchInfo.remainingFiles === 0
      ? 'All Documents Reviewed'
      : 'Mark Selected as Reviewed'
  })

  public isReviewedButtonDisabled = computed(() => {
    const batchInfo = this.reviewSetState.reviewsetBatchInfo()
    const selectedDocs = this.selectedDocuments()
    const isBatchSelected = this.isBatchSelected()

    // Disable if all documents are already reviewed
    if (batchInfo && batchInfo.remainingFiles === 0) {
      return true
    }

    // Disable if no documents are selected
    return !isBatchSelected && (!selectedDocs || selectedDocs.length === 0)
  })

  public batchCheckInIcon = computed(() => {
    const batchInfo = this.reviewSetState.reviewsetBatchInfo()
    if (!batchInfo) return 'assets/svg/icon-batch-check-in-disable.svg'
    return batchInfo.remainingFiles > 0
      ? 'assets/svg/icon-batch-check-in-disable.svg'
      : 'assets/svg/icon-batch-check-in.svg'
  })

  public isBatchCheckInDisabled = computed(() => {
    const batchInfo = this.reviewSetState.reviewsetBatchInfo()
    return !batchInfo || batchInfo.remainingFiles > 0
  })

  // Signals for document selection state
  private selectedDocuments = toSignal(
    this.documentsFacade.getSelectedDocuments$,
    { initialValue: [] }
  )

  private isBatchSelected = toSignal(this.documentsFacade.getIsBatchSelected$, {
    initialValue: false,
  })

  constructor(
    private documentsFacade: DocumentsFacade,
    private reviewFacade: ReviewFacade,
    private caseInfoFacade: CaseInfoFacade,
    private searchFacade: SearchFacade,
    private controlSettingService: ControlSettingService,
    private cdr: ChangeDetectorRef,
    public reviewSetState: ReviewSetStateService,
    private reviewsetFacade: ReviewsetFacade,
    private searchResultFacade: SearchResultFacade,
    private confirmationDialogService: ConfirmationDialogService,
    private notificationService: VenioNotificationService,
    private reviewParamService: ReviewParamService,
    private documentTagUtilityService: DocumentTagUtilityService,
    private iframeMessengerService: IframeMessengerService
  ) {}

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public ngOnInit(): void {
    this.#handleReviewViewTypeChange()
    this.#handleVisibleEmailTypeChange()
    this.#handleInclusiveEmailStatusChange()
    this.#handleTranscriptViewerEnabled()
    this.#handleImageTypePdfChange()
  }

  #handleVisibleEmailTypeChange(): void {
    this.reviewFacade.getVisibleEmailType$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((visibleEmailType) => {
        this.cdr.markForCheck()
        this.isShowingInclusiveEmailsOnly =
          visibleEmailType === EmailThreadVisibleType.InclusiveEmailOnly
      })
  }

  #handleReviewViewTypeChange(): void {
    this.reviewFacade.getReviewViewType$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((viewType) => {
        this.cdr.markForCheck()
        this.reviewViewType = viewType
      })
  }

  #handleInclusiveEmailStatusChange(): void {
    this.reviewFacade.getInclusiveEmailStatus$
      .pipe(
        filter((s) => !!s),
        takeUntil(this.toDestroy$)
      )
      .subscribe((status) => {
        const inclusiveEmailMenuItem = this._documentMenuItems.find(
          (item) => item.type === DocumentMenuType.SHOW_INCLUSIVE_EMAIL
        )
        inclusiveEmailMenuItem.disabled =
          status !== InclusiveEmailStatus.INCLUSIVE_EMAIL_HAS_RUN
        this.cdr.detectChanges()
      })
  }

  #handleTranscriptViewerEnabled(): void {
    this.caseInfoFacade.selectIsTranscriptViewerEnabled$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((isTranscriptViewerEnabled) => {
        this.cdr.markForCheck()
        this.isTranscriptViewerEnabled = isTranscriptViewerEnabled
      })
  }

  #handleImageTypePdfChange(): void {
    this.caseInfoFacade.selectIsImageTypePdfProject$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((isImageTypePdf) => {
        this.cdr.markForCheck()
        this.isImageTypePdf = isImageTypePdf
      })
  }

  /**
   * When view type is normal view then switch view menu text should be 'Switch to Email Thread View'
   * When view type is email thread view then switch view menu text should be 'Switch to Normal View'
   * @returns {void}
   */
  #updateSwitchViewMenuText(): void {
    const switchViewMenuText =
      this.reviewViewType === ReviewViewType.Search
        ? 'Switch to Email Thread View'
        : 'Switch to Normal View'

    const switchViewMenuItem = this._documentMenuItems.find(
      (item) => item.type === DocumentMenuType.SWITCH_VIEW
    )
    if (switchViewMenuItem) switchViewMenuItem.text = switchViewMenuText
  }

  #updateShowInclusiveEmailMenuText(): void {
    const showInclusiveEmailMenuText = this.isShowingInclusiveEmailsOnly
      ? 'Show All Email Threads'
      : 'Show Inclusive Emails'

    const inclusiveEmailMenuItem = this._documentMenuItems.find(
      (item) => item.type === DocumentMenuType.SHOW_INCLUSIVE_EMAIL
    )
    if (inclusiveEmailMenuItem)
      inclusiveEmailMenuItem.text = showInclusiveEmailMenuText
  }

  @HostListener('document:keydown', ['$event'])
  public keydown(event: KeyboardEvent): void {
    if (event.code === 'Escape') {
      this.toggle(false)
    }
  }

  @HostListener('document:click', ['$event'])
  public documentClick(event: KeyboardEvent): void {
    if (!this.contains(event.target)) {
      this.toggle(false)
    }
  }

  public toggle(show?: boolean): void {
    this.show = show !== undefined ? show : !this.show
  }

  public menuItemClick(menuEventPayload: DocumentMenuType): void {
    this.documentsFacade.triggerMenuEvent(menuEventPayload)
    // once the menu item is clicked, there is no point in keeping the menu open
    this.show = false
  }

  private contains = (target: EventTarget): boolean =>
    this.anchor.nativeElement.contains(target) ||
    (this.popup ? this.popup.nativeElement.contains(target) : false)

  // Batch review methods
  public onMarkAsReviewed(): void {
    combineLatest([
      this.searchFacade.getSearchResponse$,
      this.reviewParamService.projectId,
      this.documentsFacade.getSelectedDocuments$,
      this.documentsFacade.getUnselectedDocuments$,
      this.documentsFacade.getIsBatchSelected$,
    ])
      .pipe(
        take(1),
        filter(
          ([
            searchResponse,
            projectId,
            selectedDocs,
            unselectedDocs,
            isBatchSelected,
          ]) =>
            Boolean(searchResponse) &&
            Boolean(projectId) &&
            (Boolean(selectedDocs?.length > 0) || Boolean(isBatchSelected))
        ),
        switchMap(
          ([
            searchResponse,
            projectId,
            selectedDocs,
            unselectedDocs,
            isBatchSelected,
          ]) => {
            const payload: MarkAsReviewedRequestModel = {
              fileIds: isBatchSelected ? [] : selectedDocs,
              globalTempTable: searchResponse.tempTables?.searchResultTempTable,
              isBatchSelected: isBatchSelected,
              unSelectedFileIds: isBatchSelected ? unselectedDocs : [],
            }

            return this.reviewsetFacade
              .markAsReviewedBulk$(
                projectId,
                this.reviewSetState.reviewSetId(),
                this.reviewSetState.batchId(),
                payload
              )
              .pipe(
                switchMap((response: ResponseModel) => {
                  if (response.status === 'Success') {
                    // Update the UI with reviewed status
                    this.documentTagUtilityService.updateIsReviewedFlag()
                    this.searchResultFacade.updateIsReviewedFlag$.next(
                      projectId
                    )

                    // Fetch updated batch info to get the latest remainingFiles count
                    return this.reviewsetFacade
                      .fetchReviewSetBatchInfo$(
                        projectId,
                        this.reviewSetState.reviewSetId(),
                        this.reviewSetState.batchId()
                      )
                      .pipe(
                        switchMap((batchResponse: ResponseModel) => {
                          const batchInfo = batchResponse?.data
                          this.reviewSetState.reviewsetBatchInfo.set(batchInfo)
                          return of(batchInfo)
                        })
                      )
                  }
                  return of(null)
                })
              )
          }
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (batchInfo) => {
          this.notificationService.showSuccess(
            'Selected documents marked as reviewed successfully'
          )

          this.reviewsetFacade.checkBatchReviewCompletedAction$.next(
            this.reviewSetState.batchId()
          )
        },
        error: (err: unknown) => {
          const httpError = err as HttpErrorResponse
          this.notificationService.showError(
            httpError.error?.message || 'Failed to mark documents as reviewed'
          )
        },
      })
  }

  public checkInBatch(): void {
    const batchInfo = this.reviewSetState.reviewsetBatchInfo()
    if (!batchInfo || batchInfo.remainingFiles > 0) return

    this.reviewsetFacade.batchCheckInAction$.next()
  }

  private showOnlySelectedMenuItems(): void {
    // List of items to keep
    const itemsToKeep: DocumentMenuType[] = [
      DocumentMenuType.PRINT_DOWNLOAD,
      DocumentMenuType.FOLDERING,
      DocumentMenuType.CONVERT,
      DocumentMenuType.EXPORT_TO_FILE,
      DocumentMenuType.REPLACE,
      DocumentMenuType.TAGS,
      DocumentMenuType.DELETE,
      DocumentMenuType.MOVE,
      DocumentMenuType.RESPONSIVE_PST,
      DocumentMenuType.SEND_REMOVE,
    ]

    // Filter the menu items to keep only those in the itemsToKeep list
    this._documentMenuItems = this._documentMenuItems.filter((item) =>
      itemsToKeep.includes(item.type)
    )
  }
}
