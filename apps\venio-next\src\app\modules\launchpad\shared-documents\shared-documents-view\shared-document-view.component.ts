import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { IconsModule } from '@progress/kendo-angular-icons'
import { checkIcon } from '@progress/kendo-svg-icons'
import { DialogModule, DialogRef } from '@progress/kendo-angular-dialog'
import { toSignal } from '@angular/core/rxjs-interop'
import {
  DocumentShareFacade,
  SharedDocumentDetailModel,
} from '@venio/data-access/review'
import { DomSanitizer } from '@angular/platform-browser'
import { DynamicHeightDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-shared-document-view',
  standalone: true,
  imports: [CommonModule, IconsModule, DialogModule, DynamicHeightDirective],
  templateUrl: './shared-document-view.component.html',
  styleUrl: './shared-document-view.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SharedDocumentViewComponent {
  private readonly sanitizer = inject(DomSanitizer)

  private readonly sharedDocFacade = inject(DocumentShareFacade)

  // Convert the observable to a signal
  private readonly shareDocDetail = toSignal(
    this.sharedDocFacade.selectSharedDocumentDetail$
  )

  // Derive computed signal for processed data
  public readonly selectedSharedDocData = computed<SharedDocumentDetailModel>(
    (): SharedDocumentDetailModel => this.shareDocDetail()?.data
  )

  public icons = { tickIcon: checkIcon }

  private readonly dialog = inject(DialogRef)

  public closeSharedDocViewDialog(): void {
    this.dialog.close()
    this.sharedDocFacade.clearSharedDocumentDetail()
  }

  /**
   * Strips HTML tags from the given HTML content and returns plain text.
   * If the input is null, it returns an empty string.
   *
   * @param {string | null} htmlContent - The HTML content to strip tags from.
   * @returns {string} - The plain text without HTML tags.
   */
  public stripHtmlTags(htmlContent: string | null): string {
    if (!htmlContent) return ''
    const div = document.createElement('div')
    div.innerHTML = htmlContent
    return div.textContent || div.innerText || ''
  }
}
