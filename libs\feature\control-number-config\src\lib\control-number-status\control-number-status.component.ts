import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  Injector,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { TreeListModule } from '@progress/kendo-angular-treelist'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import {
  ControlNumberStatus,
  ControlNumberStatusModel,
} from '../../models/control-number-ui.model'
import { TreeViewModule } from '@progress/kendo-angular-treeview'
import { IconsModule } from '@progress/kendo-angular-icons'
import { DialogModule } from '@progress/kendo-angular-dialog'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { Subject } from 'rxjs'
import {
  ControlNumberConfigFacade,
  ControlNumberConfigStatus,
} from '@venio/data-access/review'
import { toSignal } from '@angular/core/rxjs-interop'

@Component({
  selector: 'venio-control-number-status',
  standalone: true,
  imports: [
    CommonModule,
    TreeViewModule,
    TreeListModule,
    InputsModule,
    LabelModule,
    FormsModule,
    IndicatorsModule,
    ButtonModule,
    TooltipsModule,
    SvgLoaderDirective,
    DynamicHeightDirective,
    IconsModule,
    DialogModule,
    DropDownsModule,
  ],
  templateUrl: './control-number-status.component.html',
  styleUrl: './control-number-status.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ControlNumberStatusComponent implements OnInit, OnDestroy {
  public controlNumberFacade = inject(ControlNumberConfigFacade)

  public injector = inject(Injector)

  @Input() public projectId: number

  private readonly toDestroy$ = new Subject<void>()

  public statusList = toSignal(
    this.controlNumberFacade.getCustodianMediaStatus$,
    {
      initialValue: [],
    }
  )

  public isTreeLoading = signal<boolean>(false)

  public expandedIds: string[] = []

  public treeData = signal<ControlNumberStatusModel[]>([])

  public filterStatus = signal<string | null>(null)

  public readonly custodianWarningMsg =
    'Control number may not be sequential as documents are deleted/reprocessed.'

  public controlNumberStatusList = [
    { value: 'NotStarted', label: 'Not Started' },
    { value: 'InProgress', label: 'In Progress' },
    { value: 'Completed', label: 'Completed' },
    { value: 'Error', label: 'Error' },
  ]

  public filteredTreeData = computed(() => {
    const currentStatus = this.filterStatus()
    if (!currentStatus) return this.treeData()

    const fullData = this.treeData()
    const filteredMedia = fullData.filter(
      (item) =>
        !item.isCustodian && !item.isJobRoot && item.status === currentStatus
    )

    const allowedCustodianIds = new Set(filteredMedia.map((m) => m.parentId))
    const allowedJobIds = new Set(
      fullData
        .filter((c) => allowedCustodianIds.has(c.id))
        .map((c) => c.parentId)
    )

    return fullData.filter((item) => {
      if (item.isJobRoot) return allowedJobIds.has(item.id)
      if (item.isCustodian) return allowedCustodianIds.has(item.id)
      return filteredMedia.includes(item)
    })
  })

  public ngOnInit(): void {
    this.fetchStatusData()
    this.prepareTreeData()
  }

  public ngOnDestroy(): void {
    this.controlNumberFacade.resetControlNumberStatusState()
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  private prepareTreeData(): void {
    effect(
      () => {
        const statuses = this.statusList()
        if (!statuses) {
          return
        }
        const transformedData = this.transformToTreeModel(statuses)
        this.treeData.set(transformedData)
        this.expandedIds = transformedData
          .filter((item) => item.isCustodian || item.isJobRoot)
          .map((item) => item.id)
        this.isTreeLoading.set(false)
      },
      { injector: this.injector, allowSignalWrites: true }
    )
  }

  private transformToTreeModel(
    data: ControlNumberConfigStatus[]
  ): ControlNumberStatusModel[] {
    const result: ControlNumberStatusModel[] = []

    const jobMap = new Map<string, ControlNumberStatusModel>()
    const custodianMap = new Map<string, ControlNumberStatusModel>()

    for (const item of data) {
      const jobKey = `job-${item.jobId}`

      // Create Job node
      if (!jobMap.has(jobKey)) {
        const jobNode: ControlNumberStatusModel = {
          id: jobKey,
          parentId: null,
          isJobRoot: true,
          isCustodian: false,
          jobId: item.jobId,
          status: this.mapStatus(item.status),
          startDate: item.startDate ? new Date(item.startDate) : undefined,
          endDate: item.endDate ? new Date(item.endDate) : undefined,
          errorMessage: item.errorMessage ?? undefined,
          isEditable: false,
          isDisabled: false,
        }
        jobMap.set(jobKey, jobNode)
        result.push(jobNode)
      }

      // Create Custodian node (per job)
      const custodianKey = `${jobKey}::custodian-${item.custodianId}`
      if (!custodianMap.has(custodianKey)) {
        const custodianNode: ControlNumberStatusModel = {
          id: custodianKey,
          parentId: jobKey,
          isCustodian: true,
          isJobRoot: false,
          jobId: item.jobId,
          custodianId: item.custodianId,
          custodianName: item.custodianName,
          hasWarning: item.hasWarning,
          prefix: item.prefix,
          startNumber: item.startNumber,
          endNumber: item.endNumber,
          isEditable: false,
          isDisabled: false,
        }
        custodianMap.set(custodianKey, custodianNode)
        result.push(custodianNode)
      }

      // Create Media node
      const mediaKey = `${custodianKey}::media-${item.mediaId}`
      const mediaNode: ControlNumberStatusModel = {
        id: mediaKey,
        parentId: custodianKey,
        isCustodian: false,
        isJobRoot: false,
        jobId: item.jobId,
        custodianId: item.custodianId,
        mediaId: item.mediaId,
        mediaName: item.mediaName,
        prefix: item.prefix,
        startNumber: item.startNumber,
        endNumber: item.endNumber,
        status: this.mapStatus(item.status),
        startDate: item.startDate ? new Date(item.startDate) : undefined,
        endDate: item.endDate ? new Date(item.endDate) : undefined,
        errorMessage: item.errorMessage ?? undefined,
        hasWarning: item.hasWarning,
        isEditable: item.isEditable,
        isDisabled: false,
      }
      result.push(mediaNode)
    }

    return result
  }

  public download(): void {}

  private mapStatus(raw: string): ControlNumberStatus {
    switch (raw) {
      case 'Completed':
        return ControlNumberStatus.Completed
      case 'In Progress':
        return ControlNumberStatus.InProgress
      case 'Failed':
        return ControlNumberStatus.Error
      case 'Not Started':
      case 'NotStarted':
        return ControlNumberStatus.NotStarted
      default:
        return ControlNumberStatus.NotStarted
    }
  }

  private fetchStatusData(): void {
    this.controlNumberFacade.fetchCustodianMediaStatus(this.projectId)
  }

  public onFilterValueChange(filter: { value: string; label: string }): void {
    this.filterStatus.set(filter.value)
  }

  public getStatusColor(status: ControlNumberStatus): string {
    switch (status) {
      case ControlNumberStatus.NotStarted:
        return 't-font-bold t-text-gray'
      case ControlNumberStatus.InProgress:
        return 't-font-bold t-text-[#FEB43C]'
      case ControlNumberStatus.Completed:
        return 't-font-bold t-text-[#9BD2A7]'
      case ControlNumberStatus.Error:
        return 't-font-bold t-text-[#FD696C]'
      default:
        return 't-font-bold t-text-gray'
    }
  }

  public getStatusText(status: ControlNumberStatus): string {
    switch (status) {
      case ControlNumberStatus.NotStarted:
        return 'NOT STARTED'
      case ControlNumberStatus.InProgress:
        return 'IN PROGRESS'
      case ControlNumberStatus.Completed:
        return 'COMPLETED'
      case ControlNumberStatus.Error:
        return 'ERROR'
      default:
        return 'UNKNOWN'
    }
  }

  public refreshStatus(): void {
    this.fetchStatusData()
  }

  public treeViewTrackByFn(index: number, item: any): any {
    return item.id
  }
}
