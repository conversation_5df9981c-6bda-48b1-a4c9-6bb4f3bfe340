export interface CustodianMediaControlNumberConfig {
  custodianId?: number
  custodianName?: string
  mediaId?: number
  mediaName?: string
  prefix?: string
  startNumber?: number
  endNumber?: number
  hasWarning?: boolean
  isEditable?: boolean
}

export interface ControlNumberConfigStatus
  extends CustodianMediaControlNumberConfig {
  jobId?: number
  status?: string
  startDate?: Date
  endDate?: Date
  errorMessage?: string
}

export interface GenerateControlNumberRequestModel {
  medias: GenerateControlNumberModel[]
}

export interface GenerateControlNumberModel {
  mediaId?: number
  prefix?: string
  startNumber?: number
  endNumber?: number
}
