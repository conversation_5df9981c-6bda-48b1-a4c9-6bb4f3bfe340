import { Injectable } from '@angular/core'
import { select, Store } from '@ngrx/store'
import * as UserActions from './users.actions'
import * as UserSelectors from './users.selectors'
import { UsersState } from './users.reducer'
import { InviteUserModel } from '@venio/shared/models/interfaces'

type UserStateKeys = keyof UsersState | Array<keyof UsersState>
@Injectable({ providedIn: 'root' })
export class UserFacade {
  // User list selectors
  public readonly selectIsUserListLoading$ = this.store.pipe(
    select(UserSelectors.getStateFromUserStore('isUserListLoading'))
  )

  public readonly selectUserListSuccessResponse$ = this.store.pipe(
    select(UserSelectors.getStateFromUserStore('userListSuccessResponse'))
  )

  public readonly selectUserListErrorResponse$ = this.store.pipe(
    select(UserSelectors.getStateFromUserStore('userListErrorResponse'))
  )

  // Current user selectors
  public readonly selectIsCurrentUserLoading$ = this.store.pipe(
    select(UserSelectors.getStateFromUserStore('isCurrentUserLoading'))
  )

  public readonly selectCurrentUserSuccessResponse$ = this.store.pipe(
    select(UserSelectors.getStateFromUserStore('currentUserSuccessResponse'))
  )

  public readonly selectCurrentUserErrorResponse$ = this.store.pipe(
    select(UserSelectors.getStateFromUserStore('currentUserErrorResponse'))
  )

  public readonly selectCurrentUsername$ = this.store.pipe(
    select(UserSelectors.getCurrentUsername)
  )

  public readonly selectCurrentUserDetails$ = this.store.pipe(
    select(UserSelectors.getCurrentUserDetails)
  )

  public readonly selectCurrentUserRole$ = this.store.pipe(
    select(UserSelectors.getCurrentUserRole)
  )

  // User list to invite selectors
  public readonly selectIsInvitationInProgress$ = this.store.pipe(
    select(UserSelectors.getStateFromUserStore('invitationInProgress'))
  )

  public readonly selectUserListToInviteSuccessResponse$ = this.store.pipe(
    select(
      UserSelectors.getStateFromUserStore('userListToInviteSuccessResponse')
    )
  )

  public readonly selectUserListToInviteErrorResponse$ = this.store.pipe(
    select(UserSelectors.getStateFromUserStore('userListToInviteErrorResponse'))
  )

  public readonly selectExternalUserToInviteSuccessResponse$ = this.store.pipe(
    select(
      UserSelectors.getStateFromUserStore('externalUserToInviteSuccessResponse')
    )
  )

  public readonly selectExternalUserToInviteErrorResponse$ = this.store.pipe(
    select(
      UserSelectors.getStateFromUserStore('externalUserToInviteErrorResponse')
    )
  )

  public readonly selectSendInvitationResponseMessage$ = this.store.pipe(
    select(UserSelectors.getStateFromUserStore('sendInvitationResponseMessage'))
  )

  constructor(private readonly store: Store) {}

  public fetchUserList(): void {
    this.store.dispatch(UserActions.fetchUserList())
  }

  public fetchExternalUserListToInvite(): void {
    this.store.dispatch(UserActions.fetchExternalUserListToInvite())
  }

  public fetchCurrentUser(): void {
    this.store.dispatch(UserActions.fetchCurrentUser())
  }

  /**
   * Resetting a specific property of the User State
   * @param {UserStateKeys} stateKey - state keys
   * @returns {void}
   */
  public resetUserState(stateKey: UserStateKeys): void {
    this.store.dispatch(UserActions.resetUserState({ stateKey }))
  }

  public fetchUserListToInvite(projectId: number): void {
    this.store.dispatch(UserActions.fetchUserListToInvite({ projectId }))
  }

  public sendInvitation(payload: InviteUserModel): void {
    this.store.dispatch(UserActions.sendInvitation({ payload }))
  }

  public clearInternalUserFetchError(): void {
    this.store.dispatch(UserActions.clearInternalUserFetchError())
  }

  public clearExternalUserFetchError(): void {
    this.store.dispatch(UserActions.clearExternalUserFetchError())
  }
}
