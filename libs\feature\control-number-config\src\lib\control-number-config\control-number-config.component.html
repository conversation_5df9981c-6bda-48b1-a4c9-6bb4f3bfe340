<div class="t-h-full t-pt-4">
  <kendo-treelist
    [kendoTreeListFlatBinding]="treeData()"
    [parentIdField]="'parentId'"
    [idField]="'id'"
    childrenField="children"
    [loading]="isTreeLoading()"
    kendoTreeListExpandable
    expandBy="custodianId"
    [(expandedKeys)]="expandedIds"
    [trackBy]="treeViewTrackByFn"
    [pageSize]="15"
    [autoSize]="true"
    [hideHeader]="isTreeLoading()"
    [navigable]="true"
    venioDynamicHeight
    [extraSpacing]="80"
    class="control-number-tree !t-border-0 !t-w-full v-custom-treelist-no-alt-bg">
    <kendo-treelist-column
      [expandable]="true"
      field="custodianName"
      title="Custodian Name"
      class="t-flex t-items-baseline !t-border-0">
      <!-- Select All checkbox in the header -->
      <ng-template kendoTreeListHeaderTemplate let-column>
        <kendo-checkbox
          class="t-pl-6"
          [checkedState]="selectAllChecked()"
          (checkedStateChange)="onSelectAllChange($event)">
        </kendo-checkbox>
        <kendo-label text="Select All" class="t-text-sm"></kendo-label>
      </ng-template>

      <ng-template kendoTreeListCellTemplate let-dataItem>
        <div *ngIf="dataItem.isCustodian; else mediaRow">
          <div class="t-flex t-space-x-2">
            <!-- custodian level checkbox -->
            <input
              type="checkbox"
              [checked]="isCustodianChecked(dataItem) === 'checked'"
              (change)="onCustodianCheckboxChange(dataItem, $event)"
              [indeterminate]="isCustodianChecked(dataItem) === 'indeterminate'"
              [disabled]="isCustodianDisabled(dataItem)"
              class="t-self-auto"
              kendoCheckBox
              size="medium"
              rounded="small" />
            <!-- custodian name -->
            <span class="t-font-bold">Custodian Name</span>
            <span>{{ dataItem.custodianName }}</span>
            <span class="t-flex t-items-center" *ngIf="dataItem.hasWarning">
              <img
                src="assets/svg/icon-warning.svg"
                height="18"
                width="15"
                alt="Warning"
                [popover]="controlNumberWarning"
                showOn="hover"
                kendoPopoverAnchor />
            </span>
          </div>
        </div>

        <ng-template #mediaRow>
          <div class="t-flex t-flex-row t-gap-2 t-text-xs">
            <div
              class="t-flex t-flex-col t-w-48"
              [ngClass]="{
                't-gap-1': editingState[dataItem.id],
                't-gap-2': !editingState[dataItem.id]
              }">
              <div class="t-flex t-space-x-2">
                <!-- Checkbox or lock icon -->
                <ng-container *ngIf="dataItem.isEditable; else lockedIcon">
                  <!-- media level checkbox-->
                  <input
                    type="checkbox"
                    [checked]="selectedIds.includes(dataItem.id)"
                    (change)="onCheckboxChange(dataItem, $event)"
                    class="t-self-auto"
                    kendoCheckBox
                    size="medium"
                    rounded="small" />
                </ng-container>
                <ng-template #lockedIcon>
                  <span
                    venioSvgLoader
                    applyEffectsTo="fill"
                    color="#FD696C"
                    hoverColor="#FFBB12"
                    svgUrl="assets/svg/icon-lock.svg"
                    height="1rem"
                    width="1rem">
                    <kendo-loader size="small"></kendo-loader>
                  </span>
                </ng-template>

                <!-- Media name -->
                <div
                  class="t-max-w-[100%] t-overflow-hidden t-text-ellipsis t-whitespace-no-wrap">
                  {{ dataItem.mediaName }}
                </div>
              </div>

              <!-- Prefix -->
              <div
                class="t-flex t-items-center t-pl-6 t-space-x-1"
                [ngClass]="{ 't-p-[0.3rem]': !editingState[dataItem.id] }">
                <ng-container
                  *ngIf="editingState[dataItem.id]; else showPrefix">
                  <kendo-textbox
                    [(ngModel)]="editingPrefixes[dataItem.id]"
                    (keydown.enter)="savePrefix(dataItem)"
                    (keydown.escape)="cancelEdit(dataItem)"
                    (blur)="savePrefix(dataItem)"
                    id="prefix-{{ dataItem.id }}"
                    class="t-w-48 t-h-6 t-text-xs">
                  </kendo-textbox>
                </ng-container>

                <ng-template #showPrefix>
                  <span class="t-font-bold t-text-[#8F8F8F]">Prefix</span>
                  <span
                    class="t-max-w-sm t-overflow-hidden t-text-ellipsis t-text-[#8F8F8F]"
                    >{{ dataItem.prefix }}</span
                  >
                  <button
                    kendoButton
                    #editBtn
                    fillMode="clear"
                    size="none"
                    class="t-h-1"
                    *ngIf="dataItem.isEditable"
                    (click)="editPrefix(dataItem)">
                    <span
                      [parentElement]="editBtn.element"
                      venioSvgLoader
                      applyEffectsTo="fill"
                      color="#ADADAD"
                      hoverColor="#FFBB12"
                      svgUrl="assets/svg/icon-action-grid-pencil.svg"
                      height="1rem"
                      width="1rem">
                      <kendo-loader size="small"></kendo-loader>
                    </span>
                  </button>
                </ng-template>
              </div>
            </div>

            <!-- Start Number -->
            <div
              class="t-flex t-flex-col t-gap-2 t-w-32"
              *ngIf="dataItem.parentId">
              <div>&nbsp;</div>
              <div class="t-p-[0.3rem] t-space-x-1 t-text-[#8F8F8F]">
                <span class="t-font-bold">Start Number</span>
                <span>{{ dataItem.startNumber }}</span>
              </div>
            </div>

            <!-- End Number -->
            <div
              class="t-flex t-flex-col t-gap-2 t-w-32"
              *ngIf="dataItem.parentId">
              <div>&nbsp;</div>
              <div class="t-p-[0.3rem] t-space-x-1 t-text-[#8F8F8F]">
                <span class="t-font-bold">End Number</span>
                <span>{{ dataItem.endNumber }}</span>
              </div>
            </div>
          </div>
        </ng-template>
      </ng-template>
    </kendo-treelist-column>

    <ng-template kendoTreeListNoRecordsTemplate>
      <div *ngIf="!isTreeLoading()">No data</div>
    </ng-template>
  </kendo-treelist>
</div>

<kendo-popover #controlNumberWarning [width]="300" position="right">
  <ng-template kendoPopoverBodyTemplate>
    <div class="t-text-[#ED7428] t-text-sm">{{ custodianWarningMsg }}</div>
  </ng-template>
</kendo-popover>
