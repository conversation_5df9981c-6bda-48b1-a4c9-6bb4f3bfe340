import { SamplingModel } from '@venio/shared/models/interfaces'
import { ProjectGroups } from './global.model'
import {
  FolderDocumentSelectionType,
  FolderType,
  Module,
  FolderTabType,
} from '@venio/shared/models/constants'

export interface FolderModel {
  folderId: number
  folderName: string
  parentFolderId: number
  accessType: string
  folderLineage: string
  folderIdlineage: string
  fileCount: number
  isGlobal?: boolean
  isDynamicFolder?: boolean
  isSystemFolder: boolean
  isRelativePath: boolean
  customFieldInfoId: number
  folderProjectGroupAssociations: FolderProjectGroupAssociation[]
  groupAccess?: ProjectGroups[]
  userGroupPermissionFolder?: string
  parentFileCount?: number
  description?: string
  folderOrder?: number
  belowFolderId?: number
}

export interface FolderProjectGroupAssociation {
  folderProjectGroupAssociationId: number
  folderId: number
  groupId: number
  permission: string
}

export interface FolderViewModel extends FolderModel {
  id?: number
  parentId?: number
}

export interface FolderTypeModel {
  folderTypeId: number

  folderType: string

  defaultValue: boolean
}

export interface FolderGroupPermissionModel {
  folderId: number
  groupId: number
  permission: string
}

export interface FolderActionPayloadModel {
  projectId: number
  folderActionType: FolderType
  docSelectionType: FolderDocumentSelectionType
  mainTempTable?: string
  sourceModule: Module
  folderIds: number[]
  useInjectedFileIds?: boolean
  selectedFileIds?: number[]
  folderEventComment?: string
  samplingInfo?: SamplingModel
  isRandom?: boolean
  isIncludeSubFolder?: boolean
  toFolderIds?: number[]
  module?: Module
  moduleName?: string
  propagateDocumentThread?: boolean
  isBatchSelection?: boolean
}

export interface FolderingRequestModel {
  projectId: number
  selectedFileIds: number[]
  searchTempTable: string
  documentSelectionType: FolderDocumentSelectionType
  isBatchSelected: boolean
  folderEventComment?: string
  samplingInfo?: SamplingModel
  isRandom?: boolean
  moduleName?: string
  propagateDocumentThread?: boolean
}

export interface AddFolderingRequestModel extends FolderingRequestModel {
  folderId: number[]
}

export interface RemoveFolderingRequestModel extends FolderingRequestModel {
  folderIds: number[]
  isIncludeSubFolder: boolean
}

export interface DelimiterInfo {
  displayText: string
  value: number
  text: string
}

export enum AutoFolderOption {
  Relative_Path,
  CustomField,
}

export interface AutoFolder {
  autoFolderOption: AutoFolderOption
  isCreateFolderForEachCustodian: boolean
  isCreateFolderForEachMedia: boolean
  customFieldId?: number
  customField?: string
  customFieldSeparator?: string
  groupAccess?: ProjectGroups[]
  fileListTempTableName: string
}

export interface FolderTreeState {
  rowIndex: number
}

export interface FolderTabTreeState extends FolderTreeState {
  actionType: FolderTabType.AUTO | FolderTabType.SYSTEM
  expandedIds: number[]
}

export interface CustomFolderTabTreeState extends FolderTreeState {
  actionType: FolderTabType.CUSTOM
  expandedIds: string[]
}
