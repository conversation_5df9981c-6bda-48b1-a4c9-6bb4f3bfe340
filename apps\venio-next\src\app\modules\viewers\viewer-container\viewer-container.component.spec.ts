import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ViewerContainerComponent } from './viewer-container.component'

import {
  CaseInfoFacade,
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
  SearchResultFacade,
  StartupsFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { WINDOW, windowFactory } from '@venio/data-access/iframe-messenger'
import { PLATFORM_ID } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { By } from '@angular/platform-browser'

describe('ViewerContainer', () => {
  let component: ViewerContainerComponent
  let fixture: ComponentFixture<ViewerContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ViewerContainerComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
        provideMockStore({}),
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        CaseInfoFacade,
        StartupsFacade,
        SearchResultFacade,
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: '1',
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ViewerContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should render the tabstrip component', () => {
    fixture.detectChanges()
    const tabstrip = fixture.debugElement.query(By.css('kendo-tabstrip'))
    expect(tabstrip).toBeTruthy()
  })

  describe('Viewer Tab Visibility Logic', () => {
    beforeEach(() => {
      // Set up default values for testing
      component.currentFileId = 123
      component.hasNativeViewerRight = true
      component.hasFulltextViewerRight = true
      component.hasImageViewerRight = true
      component.hasTranscriptViewerRight = true
      component.isImageTypePdf = false
      component.isTranscriptViewerEnabled = true

      // Mock the hasViewerPanelInLayout method
      jest.spyOn(component, 'hasViewerPanelInLayout').mockReturnValue(true)
    })

    afterEach(() => {
      jest.clearAllMocks()
    })

    describe('Native Viewer Tab Logic', () => {
      it('should evaluate Native tab conditions correctly when all conditions are met', () => {
        // Set all required conditions for Native tab to be visible
        component.hasNativeViewerRight = true
        component.currentFileId = 123

        // Mock hasViewerPanelInLayout to return true for NearNativeViewer
        const hasViewerPanelSpy = jest.spyOn(
          component,
          'hasViewerPanelInLayout'
        )
        hasViewerPanelSpy.mockImplementation((panelName: string) => {
          return panelName === component.ReviewPanelType.NearNativeViewer
        })

        // Test the ngIf condition logic directly
        const shouldShowNativeTab =
          component.hasViewerPanelInLayout(
            component.ReviewPanelType.NearNativeViewer
          ) &&
          component.hasNativeViewerRight &&
          (component.currentFileId ?? 0) > 0

        expect(shouldShowNativeTab).toBe(true)
        expect(component.hasViewerPanelInLayout).toHaveBeenCalledWith(
          component.ReviewPanelType.NearNativeViewer
        )
      })

      it('should evaluate Native tab conditions correctly when hasNativeViewerRight is false', () => {
        component.hasNativeViewerRight = false
        component.currentFileId = 123

        const shouldShowNativeTab =
          component.hasViewerPanelInLayout(
            component.ReviewPanelType.NearNativeViewer
          ) &&
          component.hasNativeViewerRight &&
          (component.currentFileId ?? 0) > 0

        expect(shouldShowNativeTab).toBe(false)
      })

      it('should evaluate Native tab conditions correctly when currentFileId is 0', () => {
        component.hasNativeViewerRight = true
        component.currentFileId = 0

        const shouldShowNativeTab =
          component.hasViewerPanelInLayout(
            component.ReviewPanelType.NearNativeViewer
          ) &&
          component.hasNativeViewerRight &&
          (component.currentFileId ?? 0) > 0

        expect(shouldShowNativeTab).toBe(false)
      })

      it('should evaluate Native tab conditions correctly when currentFileId is negative', () => {
        component.hasNativeViewerRight = true
        component.currentFileId = -1

        const shouldShowNativeTab =
          component.hasViewerPanelInLayout(
            component.ReviewPanelType.NearNativeViewer
          ) &&
          component.hasNativeViewerRight &&
          (component.currentFileId ?? 0) > 0

        expect(shouldShowNativeTab).toBe(false)
      })

      it('should evaluate Native tab conditions correctly when NearNativeViewer panel is not in layout', () => {
        component.hasNativeViewerRight = true
        component.currentFileId = 123

        const hasViewerPanelSpy = jest.spyOn(
          component,
          'hasViewerPanelInLayout'
        )
        hasViewerPanelSpy.mockImplementation((panelName: string) => {
          return panelName !== component.ReviewPanelType.NearNativeViewer
        })

        const shouldShowNativeTab =
          component.hasViewerPanelInLayout(
            component.ReviewPanelType.NearNativeViewer
          ) &&
          component.hasNativeViewerRight &&
          (component.currentFileId ?? 0) > 0

        expect(shouldShowNativeTab).toBe(false)
      })
    })

    describe('Fulltext Viewer Tab Logic', () => {
      it('should evaluate Fulltext tab conditions correctly when all conditions are met', () => {
        component.hasFulltextViewerRight = true
        component.currentFileId = 123

        const hasViewerPanelSpy = jest.spyOn(
          component,
          'hasViewerPanelInLayout'
        )
        hasViewerPanelSpy.mockImplementation((panelName: string) => {
          return panelName === component.ReviewPanelType.TextViewer
        })

        const shouldShowFulltextTab =
          component.hasViewerPanelInLayout(
            component.ReviewPanelType.TextViewer
          ) &&
          component.hasFulltextViewerRight &&
          (component.currentFileId ?? 0) > 0

        expect(shouldShowFulltextTab).toBe(true)
      })

      it('should evaluate Fulltext tab conditions correctly when hasFulltextViewerRight is false', () => {
        component.hasFulltextViewerRight = false
        component.currentFileId = 123

        const shouldShowFulltextTab =
          component.hasViewerPanelInLayout(
            component.ReviewPanelType.TextViewer
          ) &&
          component.hasFulltextViewerRight &&
          (component.currentFileId ?? 0) > 0

        expect(shouldShowFulltextTab).toBe(false)
      })

      it('should evaluate Fulltext tab conditions correctly when currentFileId is 0', () => {
        component.hasFulltextViewerRight = true
        component.currentFileId = 0

        const shouldShowFulltextTab =
          component.hasViewerPanelInLayout(
            component.ReviewPanelType.TextViewer
          ) &&
          component.hasFulltextViewerRight &&
          (component.currentFileId ?? 0) > 0

        expect(shouldShowFulltextTab).toBe(false)
      })

      it('should evaluate Fulltext tab conditions correctly when TextViewer panel is not in layout', () => {
        component.hasFulltextViewerRight = true
        component.currentFileId = 123

        const hasViewerPanelSpy = jest.spyOn(
          component,
          'hasViewerPanelInLayout'
        )
        hasViewerPanelSpy.mockImplementation((panelName: string) => {
          return panelName !== component.ReviewPanelType.TextViewer
        })

        const shouldShowFulltextTab =
          component.hasViewerPanelInLayout(
            component.ReviewPanelType.TextViewer
          ) &&
          component.hasFulltextViewerRight &&
          (component.currentFileId ?? 0) > 0

        expect(shouldShowFulltextTab).toBe(false)
      })
    })

    describe('PDF Viewer Tab Logic', () => {
      it('should evaluate PDF tab conditions correctly when all conditions are met', () => {
        component.isImageTypePdf = true
        component.hasImageViewerRight = true
        component.currentFileId = 123

        const hasViewerPanelSpy = jest.spyOn(
          component,
          'hasViewerPanelInLayout'
        )
        hasViewerPanelSpy.mockImplementation((panelName: string) => {
          return panelName === component.ReviewPanelType.ImageViewer
        })

        const shouldShowPdfTab =
          component.isImageTypePdf &&
          component.hasImageViewerRight &&
          component.hasViewerPanelInLayout(
            component.ReviewPanelType.ImageViewer
          ) &&
          (component.currentFileId ?? 0) > 0

        expect(shouldShowPdfTab).toBe(true)
      })

      it('should evaluate PDF tab conditions correctly when isImageTypePdf is false', () => {
        component.isImageTypePdf = false
        component.hasImageViewerRight = true
        component.currentFileId = 123

        const shouldShowPdfTab =
          component.isImageTypePdf &&
          component.hasImageViewerRight &&
          component.hasViewerPanelInLayout(
            component.ReviewPanelType.ImageViewer
          ) &&
          (component.currentFileId ?? 0) > 0

        expect(shouldShowPdfTab).toBe(false)
      })

      it('should evaluate PDF tab conditions correctly when hasImageViewerRight is false', () => {
        component.isImageTypePdf = true
        component.hasImageViewerRight = false
        component.currentFileId = 123

        const shouldShowPdfTab =
          component.isImageTypePdf &&
          component.hasImageViewerRight &&
          component.hasViewerPanelInLayout(
            component.ReviewPanelType.ImageViewer
          ) &&
          (component.currentFileId ?? 0) > 0

        expect(shouldShowPdfTab).toBe(false)
      })

      it('should evaluate PDF tab conditions correctly when currentFileId is 0', () => {
        component.isImageTypePdf = true
        component.hasImageViewerRight = true
        component.currentFileId = 0

        const shouldShowPdfTab =
          component.isImageTypePdf &&
          component.hasImageViewerRight &&
          component.hasViewerPanelInLayout(
            component.ReviewPanelType.ImageViewer
          ) &&
          (component.currentFileId ?? 0) > 0

        expect(shouldShowPdfTab).toBe(false)
      })

      it('should evaluate PDF tab conditions correctly when ImageViewer panel is not in layout', () => {
        component.isImageTypePdf = true
        component.hasImageViewerRight = true
        component.currentFileId = 123

        const hasViewerPanelSpy = jest.spyOn(
          component,
          'hasViewerPanelInLayout'
        )
        hasViewerPanelSpy.mockImplementation((panelName: string) => {
          return panelName !== component.ReviewPanelType.ImageViewer
        })

        const shouldShowPdfTab =
          component.isImageTypePdf &&
          component.hasImageViewerRight &&
          component.hasViewerPanelInLayout(
            component.ReviewPanelType.ImageViewer
          ) &&
          (component.currentFileId ?? 0) > 0

        expect(shouldShowPdfTab).toBe(false)
      })
    })

    describe('Tiff Viewer Tab Logic', () => {
      it('should evaluate Tiff tab conditions correctly when all conditions are met', () => {
        component.isImageTypePdf = false
        component.hasImageViewerRight = true
        component.currentFileId = 123

        const hasViewerPanelSpy = jest.spyOn(
          component,
          'hasViewerPanelInLayout'
        )
        hasViewerPanelSpy.mockImplementation((panelName: string) => {
          return panelName === component.ReviewPanelType.ImageViewer
        })

        const shouldShowTiffTab =
          !component.isImageTypePdf &&
          component.hasImageViewerRight &&
          component.hasViewerPanelInLayout(
            component.ReviewPanelType.ImageViewer
          ) &&
          (component.currentFileId ?? 0) > 0

        expect(shouldShowTiffTab).toBe(true)
      })

      it('should evaluate Tiff tab conditions correctly when isImageTypePdf is true', () => {
        component.isImageTypePdf = true
        component.hasImageViewerRight = true
        component.currentFileId = 123

        const shouldShowTiffTab =
          !component.isImageTypePdf &&
          component.hasImageViewerRight &&
          component.hasViewerPanelInLayout(
            component.ReviewPanelType.ImageViewer
          ) &&
          (component.currentFileId ?? 0) > 0

        expect(shouldShowTiffTab).toBe(false)
      })

      it('should evaluate Tiff tab conditions correctly when hasImageViewerRight is false', () => {
        component.isImageTypePdf = false
        component.hasImageViewerRight = false
        component.currentFileId = 123

        const shouldShowTiffTab =
          !component.isImageTypePdf &&
          component.hasImageViewerRight &&
          component.hasViewerPanelInLayout(
            component.ReviewPanelType.ImageViewer
          ) &&
          (component.currentFileId ?? 0) > 0

        expect(shouldShowTiffTab).toBe(false)
      })

      it('should evaluate Tiff tab conditions correctly when currentFileId is 0', () => {
        component.isImageTypePdf = false
        component.hasImageViewerRight = true
        component.currentFileId = 0

        const shouldShowTiffTab =
          !component.isImageTypePdf &&
          component.hasImageViewerRight &&
          component.hasViewerPanelInLayout(
            component.ReviewPanelType.ImageViewer
          ) &&
          (component.currentFileId ?? 0) > 0

        expect(shouldShowTiffTab).toBe(false)
      })

      it('should evaluate Tiff tab conditions correctly when ImageViewer panel is not in layout', () => {
        component.isImageTypePdf = false
        component.hasImageViewerRight = true
        component.currentFileId = 123

        const hasViewerPanelSpy = jest.spyOn(
          component,
          'hasViewerPanelInLayout'
        )
        hasViewerPanelSpy.mockImplementation((panelName: string) => {
          return panelName !== component.ReviewPanelType.ImageViewer
        })

        const shouldShowTiffTab =
          !component.isImageTypePdf &&
          component.hasImageViewerRight &&
          component.hasViewerPanelInLayout(
            component.ReviewPanelType.ImageViewer
          ) &&
          (component.currentFileId ?? 0) > 0

        expect(shouldShowTiffTab).toBe(false)
      })
    })

    describe('Transcript Viewer Tab Logic', () => {
      it('should evaluate Transcript tab conditions correctly when all conditions are met', () => {
        component.isTranscriptViewerEnabled = true
        component.hasTranscriptViewerRight = true

        const shouldShowTranscriptTab =
          component.isTranscriptViewerEnabled &&
          component.hasTranscriptViewerRight

        expect(shouldShowTranscriptTab).toBe(true)
      })

      it('should evaluate Transcript tab conditions correctly when isTranscriptViewerEnabled is false', () => {
        component.isTranscriptViewerEnabled = false
        component.hasTranscriptViewerRight = true

        const shouldShowTranscriptTab =
          component.isTranscriptViewerEnabled &&
          component.hasTranscriptViewerRight

        expect(shouldShowTranscriptTab).toBe(false)
      })

      it('should evaluate Transcript tab conditions correctly when hasTranscriptViewerRight is false', () => {
        component.isTranscriptViewerEnabled = true
        component.hasTranscriptViewerRight = false

        const shouldShowTranscriptTab =
          component.isTranscriptViewerEnabled &&
          component.hasTranscriptViewerRight

        expect(shouldShowTranscriptTab).toBe(false)
      })
    })
  })
})
