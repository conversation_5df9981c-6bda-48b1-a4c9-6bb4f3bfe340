import { Injectable } from '@angular/core'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { catchError, from, map, mergeMap, of, switchMap } from 'rxjs'
import { CaseConvertorService } from '@venio/util/utilities'
import * as ControlNumberConfigActions from './control-number.actions'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { ControlNumberConfigService } from '../../services/control-number-config.service'
import {
  ControlNumberConfigStatus,
  CustodianMediaControlNumberConfig,
} from '../../models/interfaces'

@Injectable()
export class ControlNumberConfigEffects {
  constructor(
    private actions$: Actions,
    private controlNumberConfigServ: ControlNumberConfigService
  ) {}

  public fetchMediaInfoForControlNumberConfig = createEffect(() =>
    this.actions$.pipe(
      ofType(ControlNumberConfigActions.fetchCustodianMedia$),
      mergeMap(({ projectId }) =>
        this.controlNumberConfigServ.fetchCustodianMedia$(projectId).pipe(
          switchMap((response: ResponseModel) => {
            const camelCaseConvertorService = new CaseConvertorService()
            const convertedData = camelCaseConvertorService.convertToCase<
              CustodianMediaControlNumberConfig[]
            >(response.data, 'camelCase')
            return from(convertedData)
          }),
          map((mediaList: CustodianMediaControlNumberConfig[]) =>
            ControlNumberConfigActions.fetchCustodianMediaSuccess$({
              medias: mediaList,
            })
          ),
          catchError((error: unknown) => {
            return of(
              ControlNumberConfigActions.fetchCustodianMediaFailure$({
                error,
              })
            )
          })
        )
      )
    )
  )

  public fetchControlNumberConfigStatus = createEffect(() =>
    this.actions$.pipe(
      ofType(ControlNumberConfigActions.fetchCustodianMediaStatus$),
      mergeMap(({ projectId }) =>
        this.controlNumberConfigServ.fetchCustodianMediaStatus$(projectId).pipe(
          switchMap((response: ResponseModel) => {
            const camelCaseConvertorService = new CaseConvertorService()
            const convertedData = camelCaseConvertorService.convertToCase<
              ControlNumberConfigStatus[]
            >(response.data, 'camelCase')
            return from(convertedData)
          }),
          map((status: ControlNumberConfigStatus[]) =>
            ControlNumberConfigActions.fetchCustodianMediaStatusSuccess$({
              status,
            })
          ),
          catchError((error: unknown) => {
            return of(
              ControlNumberConfigActions.fetchCustodianMediaStatusFailure$({
                error,
              })
            )
          })
        )
      )
    )
  )

  public generateControlNumber = createEffect(() =>
    this.actions$.pipe(
      ofType(ControlNumberConfigActions.generateControlNumber),
      mergeMap(({ projectId, payload }) =>
        this.controlNumberConfigServ
          .generateControlNumber$(projectId, payload)
          .pipe(
            map((response: ResponseModel) =>
              ControlNumberConfigActions.generateControlNumberSuccess({
                response,
              })
            ),
            catchError((error: unknown) => {
              return of(
                ControlNumberConfigActions.generateControlNumberFailure({
                  error,
                })
              )
            })
          )
      )
    )
  )
}
