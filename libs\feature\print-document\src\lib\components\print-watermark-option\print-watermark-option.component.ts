import {
  ChangeDetectionStrategy,
  Component,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
} from '@angular/core'
import { FormGroup, Validators } from '@angular/forms'
import { PrintDocumentFormService } from '../../services/print-document-form.service'
import { Subject, debounceTime, takeUntil } from 'rxjs'
import { ButtonGroupSelection } from '@progress/kendo-angular-buttons'
import {
  boldIcon,
  italicIcon,
  paletteIcon,
  SVGIcon,
  underlineIcon,
} from '@progress/kendo-svg-icons'

@Component({
  selector: 'venio-print-watermark-option',
  templateUrl: './print-watermark-option.component.html',
  styleUrls: ['./print-watermark-option.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PrintWatermarkOptionComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  public fontTextList = []

  public fontSizeList: number[] = [
    8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72,
  ]

  public fontStyleList = ['Regular', 'Italic', 'Bold']

  public selectionMode: ButtonGroupSelection = 'single'

  public boldSVG: SVGIcon = boldIcon

  public italicSVG: SVGIcon = italicIcon

  public underlineSVG: SVGIcon = underlineIcon

  public colorPalette: SVGIcon = paletteIcon

  public stampLocationList = [
    { text: 'Center', value: 'CENTER' },
    { text: 'Diagonal', value: 'DIAGONAL' },
  ]

  public get waterMarkOption(): FormGroup {
    return this.printDocumentFormService.printForm.get(
      'waterMarkOption'
    ) as FormGroup
  }

  constructor(private printDocumentFormService: PrintDocumentFormService) {}

  public ngOnInit(): void {
    this.enableDisableWatermark()

    //get font list from control settings KEY: WATERMARK_FONTS
    this.fontTextList =
      'Arial;Book Antiqua;Calibri;Cambria;Century;Century Gothic;Comic Sans MS;Courier;Courier New;Georgia;Lucida Console;Lucida Sans;Lucida Sans Unicode;Microsoft Sans Serif;MS Outlook;MS Sans Serif;Tahoma;Times New Roman;Trebuchet MS;Verdana'.split(
        ';'
      )
  }

  public isAddWatemarkChecked(): boolean {
    return this.waterMarkOption.get('addWaterMark').value ? true : false
  }

  public enableDisableWatermark(): void {
    const _waterMarkOption = this.waterMarkOption
    if (!_waterMarkOption) {
      return
    }

    _waterMarkOption
      .get('addWaterMark')
      .valueChanges.pipe(debounceTime(400), takeUntil(this.toDestroy$))
      .subscribe({
        next: (isChecked) => {
          // Controls
          const watermarkTextCtrl = _waterMarkOption.get('text')
          const watermarkOrientationCtrl = _waterMarkOption.get('orientation')
          const fontTextCtrl = _waterMarkOption.get('fontText')
          const fontSizeCtrl = _waterMarkOption.get('fontSize')
          const fontStyleCtrl = _waterMarkOption.get('fontStyle')
          const colorCtrl = _waterMarkOption.get('color')

          // controls are enabled/disabled using ngClass
          // Enable/disable based on checked value
          // isChecked ? watermarkTextCtrl.enable() : watermarkTextCtrl.disable()
          // isChecked
          //   ? watermarkOrientationCtrl.enable()
          //   : watermarkOrientationCtrl.disable()
          // isChecked ? fontTextCtrl.enable() : fontTextCtrl.disable()
          // isChecked ? fontSizeCtrl.enable() : fontSizeCtrl.disable()
          // isChecked ? fontStyleCtrl.enable() : fontStyleCtrl.disable()
          // isChecked ? colorCtrl.enable() : colorCtrl.disable()

          isChecked
            ? watermarkTextCtrl.setValidators([Validators.required])
            : watermarkTextCtrl.clearValidators()

          // Reset to default
          watermarkTextCtrl.reset('')
          watermarkOrientationCtrl.reset('CENTER')
          fontTextCtrl.reset('Calibri')
          fontSizeCtrl.reset('24')
          fontStyleCtrl.reset('Regular')
          colorCtrl.reset('#000000')
        },
      })
  }

  public onFontStyleChange(value: string): void {
    const currentValue = this.waterMarkOption.get('fontStyle')?.value

    if (currentValue === value) {
      // If the same button is clicked again, reset to 'Regular'
      this.waterMarkOption.get('fontStyle')?.setValue('Regular')
    } else {
      // Set the new value
      this.waterMarkOption.get('fontStyle')?.setValue(value)
    }
    // this.cdr.detectChanges()
  }

  public isSelected(value: string): boolean {
    // Return true only if the value matches the form's value and is not 'Regular'
    return this.waterMarkOption.get('fontStyle')?.value === value
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
