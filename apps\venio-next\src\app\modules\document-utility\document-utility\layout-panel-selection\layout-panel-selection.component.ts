import {
  ChangeDetectionStrategy,
  Component,
  computed,
  EventEmitter,
  inject,
  input,
  OnD<PERSON>roy,
  OnInit,
  Output,
  signal,
  WritableSignal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { DynamicHeightDirective } from '@venio/feature/shared/directives'
import {
  eyeIcon,
  chevronDownIcon,
  chevronLeftIcon,
} from '@progress/kendo-svg-icons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import {
  FormsModule,
  ReactiveFormsModule,
  FormControl,
  FormGroup,
  FormBuilder,
  Validators,
  AsyncValidatorFn,
  AbstractControl,
} from '@angular/forms'
import { GridModule } from '@progress/kendo-angular-grid'
import { LabelModule } from '@progress/kendo-angular-label'
import { ListViewModule } from '@progress/kendo-angular-listview'
import {
  DialogRef,
  DialogService,
  DialogsModule,
  WindowRef,
} from '@progress/kendo-angular-dialog'
import {
  ProjectInfo,
  ClientModel,
  CompositeLayoutFacade,
  CompositeLayoutState,
  GroupInfo,
  LayoutPanel,
  LayoutProjectUserGroups,
  VIEWER_PANELS,
  Layout,
  Field,
  LayoutField,
  LayoutCreateRequestModel,
  LayoutResponseModel,
  LayoutClientProjectUserGroups,
  LayoutClientProjectUserGroupModel,
  LayoutProjectUserGroupModel,
} from '@venio/data-access/review'
import {
  EMPTY,
  Subject,
  switchMap,
  takeUntil,
  take,
  catchError,
  of,
  debounceTime,
  map,
  filter,
  distinctUntilChanged,
  combineLatest,
  startWith,
} from 'rxjs'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { HttpErrorResponse } from '@angular/common/http'
import { VenioNotificationService } from '@venio/feature/notification'
import { PascalToSpacePipe } from '@venio/util/utilities'
import {
  CommonActionTypes,
  ExcludedPanelsFromRight,
  titleMapping,
} from '@venio/shared/models/constants'
import { UserFacade } from '@venio/data-access/common'
interface LayoutFormModel {
  layoutName: FormControl<string>
  sourceLayoutId: FormControl<number>
  isPrivate: FormControl<boolean>
  client: FormControl<ClientModel[]>
  case: FormControl<ProjectInfo[]>
  userGroup: FormControl<GroupInfo[]>
  isPanelSelected: FormControl<boolean>
  isViewerSelected: FormControl<boolean>
}

@Component({
  selector: 'venio-layout-panel-selection',
  standalone: true,
  imports: [
    CommonModule,
    ButtonsModule,
    InputsModule,
    IconsModule,
    DynamicHeightDirective,
    IconsModule,
    DropDownsModule,
    FormsModule,
    ReactiveFormsModule,
    ListViewModule,
    GridModule,
    LabelModule,
    DialogsModule,
    PascalToSpacePipe,
  ],
  templateUrl: './layout-panel-selection.component.html',
  styleUrl: './layout-panel-selection.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LayoutPanelSelectionComponent implements OnInit, OnDestroy {
  @Output() public readonly mainLayout: EventEmitter<string> =
    new EventEmitter<string>()

  private layoutFacade: CompositeLayoutFacade = inject(CompositeLayoutFacade)

  public layoutState: CompositeLayoutState = inject(CompositeLayoutState)

  public formBuilder: FormBuilder = inject(FormBuilder)

  private notificationFacade = inject(VenioNotificationService)

  private userFacade: UserFacade = inject(UserFacade)

  public selectedLayout = input<LayoutResponseModel>()

  public action = input<
    CommonActionTypes.CLONE | CommonActionTypes.EDIT | CommonActionTypes.CREATE
  >(CommonActionTypes.CREATE)

  public clientCaseGroupInfoOfLayout: LayoutClientProjectUserGroups

  private isEdit = computed(() => this.action() === CommonActionTypes.EDIT)

  private selectedLayoutId = computed(
    () => this.selectedLayout()?.layoutId ?? -1
  )

  public icons = {
    chevronDownIcon: chevronDownIcon,
    chevronLeftIcon: chevronLeftIcon,
    eyeIcon: eyeIcon,
  }

  public windowRef: WindowRef = inject(WindowRef) // Reference to the parent window

  public selectedPanelIds: number[] = []

  private toDestroy$: Subject<void> = new Subject<void>()

  private fetchCaseAction: Subject<number[]> = new Subject<number[]>()

  private fetchUseGroupAction: Subject<number[]> = new Subject<number[]>()

  private manualFormValidationAction = new Subject<void>()

  public clients: WritableSignal<Array<ClientModel>> = signal([])

  public cases: WritableSignal<Array<ProjectInfo>> = signal([])

  public userGroups: WritableSignal<Array<GroupInfo>> = signal([])

  public currentUserRole: WritableSignal<string> = signal('')

  public viewablePanels: WritableSignal<Array<LayoutPanel>> = signal([])

  public nonViewablePanels = computed(() => {
    return this.viewablePanels().filter((f) => !f.isSelected)
  })

  public viewerPanels: WritableSignal<Array<LayoutPanel>> = signal([])

  public enablePanelControls = signal(false)

  public layoutFormGroup: FormGroup<LayoutFormModel>

  public get formControls(): LayoutFormModel {
    return this.layoutFormGroup?.controls
  }

  private selectionState: LayoutClientProjectUserGroupModel = {}

  public clientFilterTerm = ''

  public caseFilterTerm = ''

  public userGroupFilterTerm = ''

  constructor(private dialogService: DialogService) {}

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public ngOnInit(): void {
    this.#initForm()
    this.#handlePrivateLayoutChange()
    this.#init()
    this.selectCurrentUserRole()
  }

  private selectCurrentUserRole(): void {
    this.userFacade.selectCurrentUserRole$
      .pipe(
        filter((role: string) => Boolean(role)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((role: string) => {
        this.currentUserRole.set(role)
        this.#handleIfReviewer()
      })
  }

  #initForm(): void {
    this.layoutFormGroup = this.formBuilder.group<LayoutFormModel>({
      layoutName: this.formBuilder.control('', {
        validators: [Validators.required],
        asyncValidators: [
          uniqueLayoutNameValidator(
            this.layoutFacade,
            this.isEdit() ? this.selectedLayoutId() : -1
          ),
        ],
      }),
      sourceLayoutId: this.formBuilder.control(-1),
      isPrivate: this.formBuilder.control(false),
      client: this.formBuilder.control([], {
        validators: [Validators.required],
      }),
      case: this.formBuilder.control([], {
        validators: [Validators.required],
      }),
      userGroup: this.formBuilder.control([], {
        validators: [Validators.required],
      }),
      isPanelSelected: this.formBuilder.control(true, {
        validators: Validators.requiredTrue,
      }),
      isViewerSelected: this.formBuilder.control(true, {
        validators: Validators.requiredTrue,
      }),
    })
  }

  #init(): void {
    this.#fetchLayoutPanels()
    this.#fetchClients()
    this.#fetchCases()
    this.#fetchUserGroups()
    this.#handleClientChange()
    this.#handleCaseChange()
    this.#handleUserGroupChange()
    this.#enableDisablePanelOptions()
  }

  /**
   * Enables grids only when:
   * 1. layout name is populated
   * 2. layout is either made private or all required dropdowns (client, case, user group) are populated.
   */
  #enableDisablePanelOptions(): void {
    const layoutNameControl = this.layoutFormGroup.get('layoutName')
    const isPrivateControl = this.layoutFormGroup.get('isPrivate')
    const clientControl = this.layoutFormGroup.get('client')
    const caseControl = this.layoutFormGroup.get('case')
    const userGroupControl = this.layoutFormGroup.get('userGroup')

    combineLatest([
      layoutNameControl.valueChanges.pipe(startWith(layoutNameControl.value)),
      isPrivateControl.valueChanges.pipe(startWith(isPrivateControl.value)),
      clientControl.valueChanges.pipe(startWith(clientControl.value)),
      caseControl.valueChanges.pipe(startWith(caseControl.value)),
      userGroupControl.valueChanges.pipe(startWith(userGroupControl.value)),
      this.manualFormValidationAction.pipe(startWith(undefined)),
    ])
      .pipe(
        debounceTime(50),
        map(() => {
          const hasName = !!layoutNameControl.value?.trim()
          const isPrivateChecked = isPrivateControl.value === true
          const allRequiredDropdownPopulated =
            clientControl.value?.length > 0 &&
            caseControl.value?.length > 0 &&
            userGroupControl.value?.length > 0
          return hasName && (isPrivateChecked || allRequiredDropdownPopulated)
        }),
        distinctUntilChanged(),
        takeUntil(this.toDestroy$)
      )
      .subscribe((shouldEnable) => {
        this.enablePanelControls.set(shouldEnable)
      })
  }

  #handleIfReviewer(): void {
    if (this.currentUserRole() === 'Reviewer') {
      this.formControls.isPrivate.setValue(true)
      this.formControls.isPrivate.disable()
    }
  }

  #fetchClients(): void {
    this.layoutFacade
      .fetchClients$()
      .pipe(
        switchMap((clientsResponse: ResponseModel) => {
          this.clients.set(
            clientsResponse?.data.map((client) => ({
              ...client,
              parentId: null,
            }))
          )
          if (this.selectedLayout())
            return this.layoutFacade.fetchLayoutClientProjectUsergroups(
              this.selectedLayoutId()
            )
          return of(undefined)
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel | undefined) => {
        if (response) {
          this.selectionState = response.data
          this.#selectDefaultClientsFromSelectedLayout()
          this.#selectDefaultValuesOfSelectedLayout()
        }
      })
  }

  #selectDefaultValuesOfSelectedLayout(): void {
    this.formControls.isPrivate.setValue(
      this.selectedLayout()?.isPrivate ?? false
    )
    if (this.isEdit()) this.formControls.isPrivate.disable()

    if (this.action() === CommonActionTypes.CLONE)
      this.formControls.layoutName.setValue(
        `${this.selectedLayout()?.layoutName}_Clone`
      )
    else
      this.formControls.layoutName.setValue(
        this.selectedLayout()?.layoutName ?? ''
      )
  }

  #selectDefaultClientsFromSelectedLayout(): void {
    // Extract client IDs from selectionState
    const selectedClientIds = Object.keys(this.selectionState).map(
      (clientIdStr) => Number(clientIdStr)
    )

    // Find the actual client objects from the clients array
    const selectedClients = this.clients().filter((client) =>
      selectedClientIds.includes(client.clientId)
    )

    if (selectedClients?.length > 0) {
      this.formControls.client.setValue(selectedClients, {
        emitEvent: false,
      })
      this.manualFormValidationAction.next() // update validation state

      // Fetch cases for selected clients
      this.fetchCaseAction.next(selectedClientIds)
    }
  }

  #fetchCases(): void {
    this.fetchCaseAction
      .pipe(
        filter((clients) => clients?.length > 0),
        debounceTime(200),
        distinctUntilChanged(),
        switchMap((clientIds: number[]) =>
          this.layoutFacade.fetchCasesByClientIds({ clientIds })
        ),
        takeUntil(this.toDestroy$)
      )

      .subscribe((projects: ProjectInfo[]) => {
        this.cases.set(projects.map((p) => ({ ...p, parentId: null })))
        if (projects?.length > 0) {
          this.#selectDefaultCasesOfSelectedLayout()
        }
      })
  }

  #selectDefaultCasesOfSelectedLayout(): void {
    const selectedProjectsIds: number[] = []

    // Iterate through each client in the selection state
    Object.keys(this.selectionState).forEach((clientIdStr) => {
      const clientId = Number(clientIdStr)

      // Iterate through each project of this client and collect project IDs
      this.selectionState[clientId].projects.forEach((projectObj) => {
        const projectId = Number(Object.keys(projectObj)[0])
        selectedProjectsIds.push(projectId)
      })
    })

    // Find the actual project objects from the cases array
    const selectedProjects = this.cases().filter((project) =>
      selectedProjectsIds.includes(project.projectId)
    )

    if (selectedProjects?.length > 0) {
      this.formControls.case.setValue(selectedProjects, {
        emitEvent: false,
      })
      this.manualFormValidationAction.next() // update validation state
      this.fetchUseGroupAction.next(selectedProjectsIds)
    }
  }

  #fetchUserGroups(): void {
    this.fetchUseGroupAction
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        switchMap((caseIds) => {
          return this.layoutFacade.fetchProjectUserGroups$(caseIds)
        }),

        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        const userGroups: GroupInfo[] = response.data
        this.userGroups.set(userGroups.map((ug) => ({ ...ug, parentId: null })))
        if (userGroups?.length > 0) {
          this.#selectDefaultUserGroupsOfSelectedLayout()
        }
      })
  }

  #selectDefaultUserGroupsOfSelectedLayout(): void {
    const selectedUserGroups: GroupInfo[] = []

    // Iterate through each client in the selection state
    Object.keys(this.selectionState).forEach((clientIdStr) => {
      const clientId = Number(clientIdStr)

      // Iterate through each project of this client
      this.selectionState[clientId].projects.forEach((projectObj) => {
        const projectId = Number(Object.keys(projectObj)[0])

        // Get userGroupIds for this project and add them to our collection
        const userGroupIds = projectObj[projectId].userGroupIds || []
        const projectUserGroups = this.userGroups().filter(
          (group) =>
            group.projectId === projectId &&
            userGroupIds.includes(group.groupId)
        )
        selectedUserGroups.push(...projectUserGroups)
      })
    })

    this.formControls.userGroup.setValue(selectedUserGroups, {
      emitEvent: false,
    })
    this.manualFormValidationAction.next() // update validation state
  }

  #handleClientChange(): void {
    this.formControls.client.valueChanges
      .pipe(
        filter((clients) => Boolean(clients)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((clients: ClientModel[]) => {
        // Reset dependent data
        this.cases.set([])
        this.userGroups.set([])

        const selectedClientIds = clients.map((c) => c.clientId)

        // Remove deselected clients from the state
        Object.keys(this.selectionState).forEach((clientIdStr) => {
          const clientId = Number(clientIdStr)
          if (!selectedClientIds.includes(clientId)) {
            delete this.selectionState[clientId]
          }
        })

        // Add newly selected clients
        selectedClientIds.forEach((clientId) => {
          if (!this.selectionState[clientId]) {
            this.selectionState[clientId] = {
              projects: [],
            }
          }
        })

        // Update form control without triggering another change event
        this.formControls.client.setValue(clients, { emitEvent: false })

        // Fetch cases for selected clients
        this.fetchCaseAction.next(selectedClientIds)
      })
  }

  #handleCaseChange(): void {
    this.formControls.case.valueChanges
      .pipe(
        filter((projects) => projects.length > 0),
        takeUntil(this.toDestroy$)
      )
      .subscribe((projectInfos: ProjectInfo[]) => {
        // Reset usergroup data
        this.userGroups.set([])

        const selectedProjects = projectInfos.reduce((map, project) => {
          if (!map[project.clientId]) {
            map[project.clientId] = []
          }
          map[project.clientId].push(project.projectId)
          return map
        }, {} as Record<number, number[]>)

        // Update selectionState with selected projects
        Object.keys(this.selectionState).forEach((clientIdStr) => {
          const clientId = Number(clientIdStr)
          const clientProjects = selectedProjects[clientId] || []

          // For each client, filter out deselected projects
          this.selectionState[clientId].projects = this.selectionState[
            clientId
          ].projects.filter((projectObj) => {
            const projectId = Number(Object.keys(projectObj)[0])
            return clientProjects.includes(projectId)
          })

          // Add newly selected projects
          clientProjects.forEach((projectId) => {
            if (
              !this.selectionState[clientId].projects.some(
                (p) => Number(Object.keys(p)[0]) === projectId
              )
            ) {
              const newProjectObj: LayoutProjectUserGroupModel = {}
              newProjectObj[projectId] = { userGroupIds: [] }
              this.selectionState[clientId].projects.push(newProjectObj)
            }
          })
        })

        // Update form control without triggering another change event
        this.formControls.case.setValue(projectInfos, { emitEvent: false })

        // Fetch usergroups for selected projects
        this.fetchUseGroupAction.next(projectInfos.map((p) => p.projectId))
      })
  }

  #handleUserGroupChange(): void {
    this.formControls.userGroup.valueChanges
      .pipe(
        filter((layouts) => layouts.length > 0),
        takeUntil(this.toDestroy$)
      )

      .subscribe((selectedGroups: GroupInfo[]) => {
        // Group usergroups by project
        const groupsByProject = selectedGroups.reduce((acc, group) => {
          if (!acc[group.projectId]) {
            acc[group.projectId] = []
          }
          acc[group.projectId].push(group.groupId)
          return acc
        }, {} as Record<number, number[]>)

        // Update usergroups in selection state
        Object.keys(this.selectionState).forEach((clientIdStr) => {
          const clientId = Number(clientIdStr)

          this.selectionState[clientId].projects.forEach((projectObj) => {
            const projectId = Number(Object.keys(projectObj)[0])

            // Set usergroup IDs to those that are selected for this project
            projectObj[projectId].userGroupIds =
              groupsByProject[projectId] || []
          })
        })

        // Update form control without triggering another change event
        this.formControls.userGroup.setValue(selectedGroups, {
          emitEvent: false,
        })
      })
  }

  #fetchLayoutPanels(): void {
    this.layoutFacade
      .fetchLayoutPanels$(this.selectedLayoutId())
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((response: ResponseModel) => {
        const layoutPanels: Array<LayoutPanel> = response?.data?.map((p) => {
          if (p.panelName === 'NearNativeViewer')
            return { ...p, panelName: 'NativeViewer' }
          return p
        })
        this.viewablePanels.set(
          layoutPanels
            ?.filter((f) => !ExcludedPanelsFromRight.includes(f.panelName))
            .map((p) => ({ ...p, panelName: titleMapping[p.panelName] }))
        )
        this.viewerPanels.set(
          layoutPanels?.filter((f) => VIEWER_PANELS.includes(f.panelName))
        )
        //check grid checkboxes for selected panels
        this.selectedPanelIds = this.viewablePanels()
          .filter((p) => p.isSelected)
          .map((p) => p.panelId)
      })
  }

  public onSelectionChange(event: any): void {
    const selectedItems = event.selectedRows.map((row) => row.dataItem)
    const deselectedItems = event.deselectedRows.map((row) => row.dataItem)

    if (selectedItems.length > 0)
      this.viewablePanels.set(
        this.viewablePanels().map((f) => ({
          ...f,
          isSelected: selectedItems.some((s) => s.panelName === f.panelName)
            ? true
            : f.isSelected,
        }))
      )
    if (deselectedItems.length > 0)
      this.viewablePanels.set(
        this.viewablePanels().map((f) => ({
          ...f,
          isSelected: deselectedItems.some((s) => s.panelName === f.panelName)
            ? false
            : f.isSelected,
        }))
      )

    this.#updatePanelSelectedFormControlValue()
  }

  #updatePanelSelectedFormControlValue(): void {
    const selectedReviewPanels = [
      ...this.viewablePanels().filter((f) => f.isSelected),
    ]
    const selectedViewerPanels: LayoutPanel[] = this.viewerPanels().filter(
      (f) => f.isSelected
    )

    //const selectedPanels = [...selectedReviewPanels, ...selectedViewerPanels]
    this.formControls.isPanelSelected.setValue(selectedReviewPanels.length > 0)
    this.formControls.isPanelSelected.updateValueAndValidity()
    this.formControls.isViewerSelected.setValue(selectedViewerPanels.length > 0)
    this.formControls.isViewerSelected.updateValueAndValidity()
  }

  public closeWindow(): void {
    if (this.windowRef) {
      this.windowRef.close()
      this.layoutState.showLayoutListing.set(false)
    }
  }

  public openFieldSelectionDialog(data: LayoutPanel): void {
    import(
      '../layout-panel-field-selection/layout-panel-field-selection.component'
    ).then((td) => {
      let availableProjectIds: number[] | null = null
      // if layout is not private, limit projects available in the LayoutPanelFieldSelectionComponent to ones selected in this component
      if (!this.formControls?.['isPrivate']?.value) {
        availableProjectIds = this.formControls?.['case']?.value.map(
          (project) => project.projectId
        )
      }

      const dialog: DialogRef = this.dialogService.open({
        content: td.LayoutPanelFieldSelectionComponent,
        maxWidth: '1100px',
        maxHeight: '710px',
        width: '80%',
        height: '90vh',
      })
      dialog.content.instance.selectedLayoutId = this.selectedLayoutId()
      dialog.content.instance.selectedLayoutPanel = data
      dialog.content.instance.availableProjectIds = availableProjectIds

      dialog.result.subscribe((result) => {
        // if (result instanceof DialogCloseResult) {
        // }
      })
    })
  }

  #handlePrivateLayoutChange(): void {
    this.formControls.isPrivate.valueChanges
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((value) => {
        const formcontrols = [
          this.formControls.case,
          this.formControls.client,
          this.formControls.userGroup,
        ]
        formcontrols.forEach((ctrl) => {
          if (value) {
            ctrl.disable()
            ctrl.removeValidators(Validators.required)
          } else {
            ctrl.enable()
            ctrl.addValidators(Validators.required)
          }
          ctrl.updateValueAndValidity()
        })
      })
  }

  public oncheckChanged(data: LayoutPanel, event: any): void {
    const isChecked = (event.target as HTMLInputElement).checked
    const viewers = this.viewerPanels().map((p) => {
      if (p.panelId === data.panelId) return { ...p, isSelected: isChecked }
      return p
    })
    this.viewerPanels.set(viewers)
    this.#updatePanelSelectedFormControlValue()
  }

  public saveLayout(): void {
    Object.entries(this.formControls).forEach(([key, control]) => {
      control.markAsTouched()
      control.markAsDirty()
    })

    const selectedViewablePanels: LayoutPanel[] = this.viewablePanels().filter(
      (f) => f.isSelected
    )
    const selectedViewerPanels: LayoutPanel[] = this.viewerPanels().filter(
      (f) => f.isSelected
    )
    const selectedPanels = [...selectedViewablePanels, ...selectedViewerPanels]

    if (!this.layoutFormGroup.valid) return

    const allSelectedPanels = selectedPanels.map(
      (p) =>
        ({
          hasField: p.hasField,
          panelId: p.panelId,
          panelName: p.panelName,
          fields: p.hasField
            ? this.#getLayoutFields(
                this.layoutState.layoutPanelFieldMapping()[p.panelId]
              )
            : null,
        } as LayoutPanel)
    )

    const layoutFormValues = this.layoutFormGroup.getRawValue()
    const layout: Layout = {
      layoutName: layoutFormValues.layoutName,
      isPrivate: layoutFormValues.isPrivate,
      layoutPanels: allSelectedPanels,
    }
    const payload: LayoutCreateRequestModel = {
      layoutModel: layout,
      sourceLayoutId: this.selectedLayoutId(),
      projectUserGroups: this.#convertToLayoutProjectUserGroups(
        layoutFormValues.userGroup
      ),
    }

    let obs = this.layoutFacade.createLayout$(payload)
    if (this.isEdit())
      obs = this.layoutFacade.updateLayout$(this.selectedLayoutId(), payload)
    obs
      .pipe(
        catchError((error: unknown) => {
          const httpError = error as HttpErrorResponse
          this.notificationFacade.showError(httpError.error.message)
          return EMPTY
        }),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        this.notificationFacade.showSuccess(response.message)
        if (
          this.isEdit() &&
          this.selectedLayoutId() ===
            this.layoutState.userSelectedLayout().layoutId
        )
          this.layoutFacade.loadLayoutAction$.next(this.selectedLayoutId())
        this.closeWindow()
      })
  }

  #getLayoutFields(fields: Field[] = []): LayoutField[] {
    return fields.map((f: Field) => ({
      isSelected: true,
      fieldId: f.venioFieldId,
      fieldName: f.displayFieldName,
      fieldOrder: f.displayOrder,
      isCustomField: f.isCustomField,
    }))
  }

  #convertToLayoutProjectUserGroups(
    groupInfoArray: GroupInfo[]
  ): LayoutProjectUserGroups[] {
    const grouped = groupInfoArray.reduce((acc, curr) => {
      // Check if the projectId already exists in the accumulator
      const existingGroup = acc.find(
        (item) => item.projectId === curr.projectId
      )
      if (existingGroup) {
        // If it exists, add the groupId to the userGroupIds array
        existingGroup.userGroupIds.push(curr.groupId)
      } else {
        // Otherwise, create a new entry for this projectId
        acc.push({
          projectId: curr.projectId,
          userGroupIds: [curr.groupId],
        })
      }
      return acc
    }, [] as LayoutProjectUserGroups[])

    return grouped
  }

  public goBack(): void {
    this.mainLayout.emit('true')
  }

  public toggleDropdown(multiselecttree: any, event: MouseEvent): void {
    event.preventDefault()
    event.stopPropagation()
    multiselecttree.toggle()
  }

  // Returns true if all filtered (or all) items are checked for the given type
  public isAllChecked(
    type: 'client' | 'case' | 'userGroup',
    tree: any
  ): boolean {
    const filteredData = tree.filteredData || []
    const allData = tree.data || []
    const items = filteredData.length > 0 ? filteredData : allData
    const valueField =
      type === 'client'
        ? 'clientId'
        : type === 'case'
        ? 'projectId'
        : 'groupName'
    const selected = this.layoutFormGroup.get(type).value || []
    if (!items.length) return false
    return items.every((item: any) =>
      selected.some((s: any) => s[valueField] === item[valueField])
    )
  }

  public isIndeterminate(
    type: 'client' | 'case' | 'userGroup',
    tree: any
  ): boolean {
    const valueField =
      type === 'client'
        ? 'clientId'
        : type === 'case'
        ? 'projectId'
        : 'groupName'
    const selected = this.layoutFormGroup.get(type).value || []
    let allData: any[] = []
    let filterTerm = ''
    let textField = ''
    if (type === 'client') {
      allData = this.clients()
      filterTerm = this.clientFilterTerm
      textField = 'clientName'
    } else if (type === 'case') {
      allData = this.cases()
      filterTerm = this.caseFilterTerm
      textField = 'projectName'
    } else if (type === 'userGroup') {
      allData = this.userGroups()
      filterTerm = this.userGroupFilterTerm
      textField = 'groupName'
    }
    let items: any[] = []
    if (typeof filterTerm === 'string' && filterTerm.trim().length > 0) {
      items = allData.filter((item) =>
        (item[textField] || '')
          .toLowerCase()
          .includes(filterTerm.trim().toLowerCase())
      )
    } else {
      items = allData
    }
    if (!items.length) return false
    const selectedCount = items.filter((item) =>
      selected.some((s: any) => s[valueField] === item[valueField])
    ).length
    return selectedCount > 0 && selectedCount < items.length
  }

  // Utility to flatten tree data to a flat array of visible nodes
  private flattenTree(data: any[], valueField: string): any[] {
    const result: any[] = []
    const stack = [...data]
    while (stack.length) {
      const node = stack.shift()
      result.push(node)
      if (node && node.items && Array.isArray(node.items)) {
        stack.push(...node.items)
      }
    }
    return result
  }

  public onFilterChange(
    type: 'client' | 'case' | 'userGroup',
    term: string
  ): void {
    if (type === 'client') this.clientFilterTerm = term
    else if (type === 'case') this.caseFilterTerm = term
    else if (type === 'userGroup') this.userGroupFilterTerm = term
  }

  public toggleCheckAll(
    type: 'client' | 'case' | 'userGroup',
    tree: any,
    event: any
  ): void {
    const checked = event.target.checked
    let items: any[] = []
    // Use the tracked filter term and original dataset
    const filterTerm =
      type === 'client'
        ? this.clientFilterTerm
        : type === 'case'
        ? this.caseFilterTerm
        : this.userGroupFilterTerm
    let allData: any[] = []
    if (type === 'client') allData = this.clients()
    else if (type === 'case') allData = this.cases()
    else if (type === 'userGroup') allData = this.userGroups()
    if (typeof filterTerm === 'string' && filterTerm.trim().length > 0) {
      // Filter manually by textField
      const textField =
        type === 'client'
          ? 'clientName'
          : type === 'case'
          ? 'projectName'
          : 'groupName'
      items = allData.filter((item) =>
        (item[textField] || '')
          .toLowerCase()
          .includes(filterTerm.trim().toLowerCase())
      )
    } else {
      items = allData
    }
    if (checked) {
      if (type === 'client') {
        this.layoutFormGroup.get(type).setValue(items as ClientModel[])
        tree.value = items as ClientModel[]
      } else if (type === 'case') {
        this.layoutFormGroup.get(type).setValue(items as ProjectInfo[])
        tree.value = items as ProjectInfo[]
      } else if (type === 'userGroup') {
        this.layoutFormGroup.get(type).setValue(items as GroupInfo[])
        tree.value = items as GroupInfo[]
      }
    } else {
      this.layoutFormGroup.get(type).setValue([])
      tree.value = []
    }
  }
}

export function uniqueLayoutNameValidator(
  layoutFacade: CompositeLayoutFacade,
  layoutId: number
): AsyncValidatorFn {
  return (control: AbstractControl) => {
    if (!control.value) {
      return of(null)
    }

    return of(control.value).pipe(
      debounceTime(300),
      switchMap((layoutName) =>
        layoutFacade.isLayoutNameTaken$(layoutId, layoutName).pipe(
          map((response: ResponseModel) =>
            response.data ? { layoutNameTaken: true } : null
          ),
          catchError(() => of(null))
        )
      )
    )
  }
}
