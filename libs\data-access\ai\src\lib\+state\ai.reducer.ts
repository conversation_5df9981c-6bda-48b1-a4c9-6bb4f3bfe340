import { Action, createReducer, on } from '@ngrx/store'
import * as AiActions from './ai.actions'
import { resetStateProperty } from '@venio/util/utilities'
import {
  AiDocument,
  AiSearchResult,
  AiSearchUiTypes,
} from '../models/interfaces/ai.model'
import {
  EciDashboardUiState,
  EciDashboardDataState,
  SunburstChartType,
  ChartDrillDownState,
} from '../models/interfaces/eci-dashboard.model'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { UtilityPanelType } from '@venio/shared/models/constants'

export const AI_FEATURE_KEY = 'aiStore'

/**
 * Helper function to create initial chart drill-down state
 * @param chartType - The type of chart to create initial state for
 * @returns Initial chart drill-down state
 */
function createInitialChartDrillDownState(
  chartType: SunburstChartType
): ChartDrillDownState {
  return {
    chartType,
    isExpanded: false,
    currentLevel: 0,
    drillDownHistory: [],
    currentData: [],
    tableData: [],
    showDetails: false,
    selectedNode: null,
  }
}

export interface AIState {
  isSearchLoading?: boolean | undefined
  activeUuid?: string | undefined
  selectedDocumentSummary: AiSearchResult | undefined
  searchSummaryList?: Record<string, AiSearchResult> | undefined
  aiSearchError?: any | undefined
  selectedSearchTab: AiSearchUiTypes | undefined
  allFileIds: number[] | undefined
  isResetTriggered: boolean | undefined
  isCreateJobEdaiLoading: boolean | undefined
  isEdaiStatusLoading: boolean | undefined
  isEdaiDocumentRelevanceLoading: boolean | undefined
  isEdaiDocumentPrivilegeLoading: boolean | undefined
  isEdaiDocumentPIILoading: boolean | undefined
  isEdaijobStatusDetailLoading: boolean | undefined
  isEdaiPIITemplateLoading: boolean | undefined
  isEdaiPIITEntitiesLoading: boolean | undefined
  createJobEdaiSuccess: ResponseModel | undefined
  createJobEdaiError: ResponseModel | undefined

  isEdaiRelevanceCompletedJobsLoading: boolean | undefined
  edaiRelevanceCompletedJobs: ResponseModel | undefined
  edaiRelevanceCompletedJobsError: ResponseModel | undefined

  edaiStatusResults: ResponseModel | undefined
  edaiDocumentRelevancySuccess: ResponseModel | undefined
  edaiDocumentRelevancyError: ResponseModel | undefined
  edaiDocumentPrivilegeSuccess: ResponseModel | undefined
  edaiDocumentPrivilegeError: ResponseModel | undefined
  edaiDocumentPIISuccess:
    | Partial<Record<UtilityPanelType, ResponseModel>>
    | undefined
  edaiDocumentPIIError:
    | Partial<Record<UtilityPanelType, ResponseModel>>
    | undefined
  edaiJobStatusDetailsSuccess: ResponseModel | undefined
  edaiJobStatusDetailsError: ResponseModel | undefined
  edaiPIITemplateSuccess: ResponseModel | undefined
  edaiPIITemplateError: ResponseModel | undefined
  edaiPIIEntitySuccess: ResponseModel | undefined
  edaiPIIEntityError: ResponseModel | undefined

  // ECI Dashboard State
  eciDashboard: EciDashboardUiState & EciDashboardDataState

  // ECA API State
  ecaRelevanceSuccess: ResponseModel | undefined
  ecaRelevanceError: ResponseModel | undefined
  isEcaRelevanceLoading: boolean
  ecaDocumentTypesSuccess: ResponseModel | undefined
  ecaDocumentTypesError: ResponseModel | undefined
  isEcaDocumentTypesLoading: boolean
  ecaTopicsRelevantSuccess: ResponseModel | undefined
  ecaTopicsRelevantError: ResponseModel | undefined
  isEcaTopicsRelevantLoading: boolean
  ecaTopicsNonRelevantSuccess: ResponseModel | undefined
  ecaTopicsNonRelevantError: ResponseModel | undefined
  isEcaTopicsNonRelevantLoading: boolean
  isDownloadingCSV: boolean | undefined
  downloadCSVError: ResponseModel | undefined
}

export const aiInitialState: AIState = {
  isSearchLoading: false,
  activeUuid: undefined,
  searchSummaryList: undefined,
  aiSearchError: undefined,
  selectedDocumentSummary: undefined,
  selectedSearchTab: AiSearchUiTypes.Progress,
  allFileIds: undefined,
  isResetTriggered: undefined,
  isCreateJobEdaiLoading: undefined,
  createJobEdaiSuccess: undefined,
  createJobEdaiError: undefined,
  edaiRelevanceCompletedJobs: undefined,
  isEdaiRelevanceCompletedJobsLoading: undefined,
  edaiRelevanceCompletedJobsError: undefined,
  edaiStatusResults: undefined,
  isEdaiStatusLoading: undefined,
  isEdaiDocumentRelevanceLoading: undefined,
  isEdaiDocumentPrivilegeLoading: undefined,
  isEdaiDocumentPIILoading: undefined,
  edaiDocumentRelevancySuccess: undefined,
  edaiDocumentRelevancyError: undefined,
  isEdaijobStatusDetailLoading: undefined,
  edaiJobStatusDetailsSuccess: undefined,
  edaiJobStatusDetailsError: undefined,
  edaiDocumentPrivilegeError: undefined,
  edaiDocumentPrivilegeSuccess: undefined,
  edaiDocumentPIISuccess: undefined,
  edaiDocumentPIIError: undefined,
  edaiPIITemplateSuccess: undefined,
  edaiPIITemplateError: undefined,
  edaiPIIEntitySuccess: undefined,
  edaiPIIEntityError: undefined,
  isEdaiPIITemplateLoading: undefined,
  isEdaiPIITEntitiesLoading: undefined,
  isDownloadingCSV: false,
  downloadCSVError: undefined,

  // ECI Dashboard Initial State
  eciDashboard: {
    // UI State
    isFocusedSectionOpened: false,
    isParentData: true,
    showDetails: false,
    showFilterPopup: false,
    showCustodianFilters: false,
    activeChartType: null,
    chartDrillDownStates: {
      [SunburstChartType.DocumentTypes]: createInitialChartDrillDownState(
        SunburstChartType.DocumentTypes
      ),
      [SunburstChartType.RelevantDocuments]: createInitialChartDrillDownState(
        SunburstChartType.RelevantDocuments
      ),
      [SunburstChartType.NotRelevantDocuments]:
        createInitialChartDrillDownState(
          SunburstChartType.NotRelevantDocuments
        ),
      [SunburstChartType.Relevance]: createInitialChartDrillDownState(
        SunburstChartType.Relevance
      ),
    },

    // Data State
    custodians: [],
    documentTypes: [],
    fileData: [],
    wordCloudData: [],
    relevanceData: null,
    barChartData: null,
    tableData: [],
    selectedDocuments: 0,
    totalDocuments: 276897,
    paginationState: {
      currentPage: 1,
      pageSize: 15,
      totalRecords: 0,
    },
  },

  // ECA API Initial State
  ecaRelevanceSuccess: undefined,
  ecaRelevanceError: undefined,
  isEcaRelevanceLoading: false,
  ecaDocumentTypesSuccess: undefined,
  ecaDocumentTypesError: undefined,
  isEcaDocumentTypesLoading: false,
  ecaTopicsRelevantSuccess: undefined,
  ecaTopicsRelevantError: undefined,
  isEcaTopicsRelevantLoading: false,
  ecaTopicsNonRelevantSuccess: undefined,
  ecaTopicsNonRelevantError: undefined,
  isEcaTopicsNonRelevantLoading: false,
}

/**
 * Extracts and returns an array of unique file_id from the searchSummaryList.
 * Ensures that all returned file_ids are distinct.
 *
 * @param { Record<string, AiSearchResult>} searchSummaryList - The list from which file_ids are extracted.
 * @returns {number[]} An array of unique file_id numbers, extracted from the search results.
 */
const extractFileIds = (
  searchSummaryList: Record<string, AiSearchResult>
): number[] => {
  if (!searchSummaryList) {
    return []
  }

  // Use a Set to collect unique file_ids
  const fileIdsSet: Set<number> = new Set()

  Object.values(searchSummaryList)
    .filter((c) => Boolean(c.content?.documents))
    .forEach((searchResult: AiSearchResult) => {
      searchResult.content.documents
        .filter(
          (doc: AiDocument) =>
            doc.file_id !== undefined && Number(doc.file_id) > 0
        )
        .forEach((doc: AiDocument) => {
          const fileId = doc.file_id
          fileIdsSet.add(fileId)
        })
    })

  // Convert Set to Array before returning
  return Array.from(fileIdsSet)
}

const reducer = createReducer<AIState>(
  aiInitialState,
  on(AiActions.resetAiState, (state, { stateKey }) =>
    resetStateProperty<AIState>(state, aiInitialState, stateKey)
  ),
  on(AiActions.performAiSearch, (state, { uuid, payload }) => ({
    ...state,
    isSearchLoading: true,
    activeUuid: uuid,
    selectedSearchTab: AiSearchUiTypes.Progress,
    selectedDocumentSummary: undefined,
    searchSummaryList: {
      ...state.searchSummaryList,
      [uuid]: {
        original_query: payload.query,
      } as AiSearchResult,
    },
  })),
  on(AiActions.performAiSearchSuccess, (state, { aiSearchSuccess, uuid }) => {
    const searchResult = state.searchSummaryList[uuid]

    const finalized = {
      ...searchResult,
      ...aiSearchSuccess,
    }
    const searchSummaryList = {
      ...state.searchSummaryList,
      [uuid]: finalized,
    } as Record<string, AiSearchResult>

    let allFileIds = extractFileIds(searchSummaryList)
    // merge any existing and dedupe it.
    allFileIds = [...new Set([...(state.allFileIds || []), ...allFileIds])]
    return {
      ...state,
      isSearchLoading: false,
      aiSearchError: undefined,
      activeUuid: undefined,
      searchSummaryList,
      allFileIds,
      selectedDocumentSummary: finalized,
      selectedSearchTab: AiSearchUiTypes.DocSummary,
    }
  }),
  on(AiActions.performAiSearchFailure, (state, { aiSearchError }) => ({
    ...state,
    isSearchLoading: false,
    activeUuid: undefined,
    searchSummaryList: undefined,
    aiSearchError,
  })),
  on(
    AiActions.performAiProgressMessageUpdate,
    (state, { uuid, progressMessage }) => {
      const searchResult = state.searchSummaryList[uuid]
      const messages = searchResult?.progress_messages || []

      const updatedProgressMessages =
        messages.length > 0
          ? messages.map((msg) => {
              if (msg.id === progressMessage.id) {
                return { ...msg, message: progressMessage.message }
              }
              return msg
            })
          : []

      // If the message wasn't found and updated, add it to the array
      if (
        !updatedProgressMessages.some((msg) => msg.id === progressMessage.id)
      ) {
        updatedProgressMessages.push(progressMessage)
      }

      const finalized = {
        ...searchResult,
        progress_messages: [...updatedProgressMessages],
      }

      return {
        ...state,
        searchSummaryList: {
          ...state.searchSummaryList,
          [uuid]: finalized,
        } as Record<string, AiSearchResult>,
      }
    }
  ),
  on(
    AiActions.setSelectedDocumentSummary,
    (state, { selectedDocumentSummary }) => ({
      ...state,
      selectedDocumentSummary,
    })
  ),
  on(AiActions.updateSelectedSearchTab, (state, { selectedSearchTab }) => ({
    ...state,
    selectedSearchTab,
  })),
  on(AiActions.resetTrigger, (state, { isResetTriggered }) => ({
    ...state,
    isResetTriggered,
  })),
  on(AiActions.createJobEdai, (state) => ({
    ...state,
    isCreateJobEdaiLoading: true,
  })),
  on(AiActions.createJobEdaiSuccess, (state, { createJobEdaiSuccess }) => ({
    ...state,
    createJobEdaiSuccess,
    createJobEdaiError: undefined,
    isCreateJobEdaiLoading: false,
  })),
  on(AiActions.createJobEdaiFailure, (state, { createJobEdaiError }) => ({
    ...state,
    createJobEdaiSuccess: undefined,
    createJobEdaiError,
    isCreateJobEdaiLoading: false,
  })),

  on(AiActions.fetchEdaiRelevanceCompletedJobs, (state) => ({
    ...state,
    isEdaiRelevanceCompletedJobsLoading: true,
    edaiRelevanceCompletedJobs: undefined,
  })),

  on(
    AiActions.fetchEdaiRelevanceCompletedJobsSuccess,
    (state, { edaiStatus }) => ({
      ...state,
      isEdaiRelevanceCompletedJobsLoading: false,
      edaiRelevanceCompletedJobs: edaiStatus,
    })
  ),

  on(
    AiActions.fetchEdaiRelevanceCompletedJobsFailure,
    (state, { edaiStatusError }) => ({
      ...state,
      isEdaiRelevanceCompletedJobsLoading: false,
      edaiRelevanceCompletedJobs: undefined,
      edaiRelevanceCompletedJobsError: edaiStatusError,
    })
  ),

  on(AiActions.fetchEdaiStatus, (state) => ({
    ...state,
    isEdaiStatusLoading: true,
    edaiStatusResults: undefined,
  })),
  on(AiActions.fetchEdaiStatusSuccess, (state, { edaiStatus }) => ({
    ...state,
    isEdaiStatusLoading: false,
    edaiStatusResults: edaiStatus,
  })),
  on(AiActions.fetchEdaiStatusFailure, (state) => ({
    ...state,
    isEdaiStatusLoading: false,
    edaiStatusResults: undefined,
  })),
  on(AiActions.fetchEdaiDocumentRelevancy, (state) => ({
    ...state,
    isEdaiDocumentRelevanceLoading: true,
    edaiDocumentRelevancyError: undefined,
    edaiDocumentRelevancySuccess: undefined,
  })),
  on(
    AiActions.fetchEdaiDocumentRelevancySuccess,
    (state, { edaiDocumentRelevancySuccess }) => ({
      ...state,
      isEdaiDocumentRelevanceLoading: false,
      edaiDocumentRelevancyError: undefined,
      edaiDocumentRelevancySuccess,
    })
  ),
  on(
    AiActions.fetchEdaiDocumentRelevancyFailure,
    (state, { edaiDocumentRelevancyError }) => ({
      ...state,
      isEdaiDocumentRelevanceLoading: false,
      edaiDocumentRelevanceSuccess: undefined,
      edaiDocumentRelevancyError,
    })
  ),
  on(AiActions.fetchJobStatusDetails, (state) => ({
    ...state,
    isEdaijobStatusDetailLoading: true,
    edaiJobStatusDetailsSuccess: undefined,
    edaiJobStatusDetailsError: undefined,
  })),
  on(
    AiActions.fetchJobStatusDetailsSuccess,
    (state, { edaiJobStatusDetailsSuccess }) => ({
      ...state,
      isEdaijobStatusDetailLoading: false,
      edaiJobStatusDetailsSuccess,
    })
  ),
  on(
    AiActions.fetchJobStatusDetailsFailure,
    (state, { edaiJobStatusDetailsError }) => ({
      ...state,
      isEdaijobStatusDetailLoading: false,
      edaiJobStatusDetailsSuccess: undefined,
      edaiJobStatusDetailsError,
    })
  ),
  on(AiActions.fetchEdaiDocumentPrivilege, (state) => ({
    ...state,
    isEdaiDocumentPrivilegeLoading: true,
    edaiDocumentPrivilegeError: undefined,
    edaiDocumentPrivilegeSuccess: undefined,
  })),
  on(
    AiActions.fetchEdaiDocumentPrivilegeSuccess,
    (state, { edaiDocumentPrivilegeSuccess }) => ({
      ...state,
      isEdaiDocumentPrivilegeLoading: false,
      edaiDocumentPrivilegeSuccess,
    })
  ),
  on(
    AiActions.fetchEdaiDocumentPrivilegeFailure,
    (state, { edaiDocumentPrivilegeError }) => ({
      ...state,
      isEdaiDocumentPrivilegeLoading: false,
      edaiDocumentPrivilegeSuccess: undefined,
      edaiDocumentPrivilegeError,
    })
  ),
  on(AiActions.fetchEdaiPiiTemplate, (state) => ({
    ...state,
    isEdaiPIITemplateLoading: true,
    edaiPIITemplateSuccess: undefined,
    edaiPIITemplateError: undefined,
  })),
  on(
    AiActions.fetchEdaiPiiTemplateSuccess,
    (state, { edaiPIITemplateSuccess }) => ({
      ...state,
      isEdaiPIITemplateLoading: false,
      edaiPIITemplateError: undefined,
      edaiPIITemplateSuccess,
    })
  ),
  on(
    AiActions.fetchEdaiPiiTemplateFailure,
    (state, { edaiPIITemplateError }) => ({
      ...state,
      isEdaiPIITemplateLoading: false,
      edaiPIITemplateSuccess: undefined,
      edaiPIITemplateError,
    })
  ),

  on(AiActions.fetchEdaiPiiEntities, (state) => ({
    ...state,
    isEdaiPIITEntitiesLoading: true,
    edaiPIIEntitySuccess: undefined,
    edaiPIIEntityError: undefined,
  })),

  on(
    AiActions.fetchEdaiPiiEntitiesSuccess,
    (state, { edaiPIIEntitySuccess }) => ({
      ...state,
      isEdaiPIITEntitiesLoading: false,
      edaiPIIEntityError: undefined,
      edaiPIIEntitySuccess,
    })
  ),

  on(
    AiActions.fetchEdaiPiiEntitiesFailure,
    (state, { edaiPIIEntityError }) => ({
      ...state,
      isEdaiPIITEntitiesLoading: false,
      edaiPIIEntitySuccess: undefined,
      edaiPIIEntityError,
    })
  ),
  on(AiActions.fetchEdaiDocumentPii, (state) => ({
    ...state,
    isEdaiDocumentPIILoading: true,
    edaiDocumentPIISuccess: undefined,
    edaiDocumentPIIError: undefined,
  })),
  on(
    AiActions.fetchEdaiDocumentPiiSuccess,
    (state, { edaiDocumentPIISuccess, isPiiDetect }) => ({
      ...state,
      isEdaiDocumentPIILoading: false,
      edaiDocumentPIIError: undefined,
      edaiDocumentPIISuccess: {
        ...(state.edaiDocumentPIISuccess || {}),
        [isPiiDetect
          ? UtilityPanelType.EDAI_AI_PII_DETECT
          : UtilityPanelType.EDAI_AI_PII_EXTRACT]: edaiDocumentPIISuccess,
      },
    })
  ),
  on(
    AiActions.fetchEdaiDocumentPiiFailure,
    (state, { edaiDocumentPIIError, isPiiDetect }) => ({
      ...state,
      isEdaiDocumentPIILoading: false,
      edaiDocumentPIISuccess: undefined,
      edaiDocumentPIIError: {
        ...(state.edaiDocumentPIIError || {}),
        [isPiiDetect
          ? UtilityPanelType.EDAI_AI_PII_DETECT
          : UtilityPanelType.EDAI_AI_PII_EXTRACT]: edaiDocumentPIIError,
      },
    })
  ),

  // ECI Dashboard Reducers
  on(AiActions.setEciFocusedSectionOpened, (state, { isOpened }) => ({
    ...state,
    eciDashboard: {
      ...state.eciDashboard,
      isFocusedSectionOpened: isOpened,
      selectedDocuments: isOpened ? 13479 : 0,
    },
  })),

  on(AiActions.setEciParentDataView, (state, { isParentData }) => ({
    ...state,
    eciDashboard: {
      ...state.eciDashboard,
      isParentData,
    },
  })),

  on(AiActions.setEciShowDetails, (state, { showDetails }) => ({
    ...state,
    eciDashboard: {
      ...state.eciDashboard,
      showDetails,
    },
  })),

  on(AiActions.setEciFilterPopupVisibility, (state, { isVisible }) => ({
    ...state,
    eciDashboard: {
      ...state.eciDashboard,
      showFilterPopup: isVisible,
    },
  })),

  on(AiActions.setEciCustodianFiltersVisibility, (state, { isVisible }) => ({
    ...state,
    eciDashboard: {
      ...state.eciDashboard,
      showCustodianFilters: isVisible,
    },
  })),

  on(
    AiActions.fetchEciDashboardDataSuccess,
    (
      state,
      {
        custodians,
        documentTypes,
        fileData,
        wordCloudData,
        relevanceData,
        barChartData,
      }
    ) => ({
      ...state,
      eciDashboard: {
        ...state.eciDashboard,
        custodians,
        documentTypes,
        fileData,
        wordCloudData,
        relevanceData,
        barChartData,
        paginationState: {
          ...state.eciDashboard.paginationState,
          totalRecords: fileData.length,
        },
      },
    })
  ),

  on(AiActions.storeEciTableData, (state, { tableData }) => ({
    ...state,
    eciDashboard: {
      ...state.eciDashboard,
      tableData,
    },
  })),

  on(AiActions.storeEciSelectedDocuments, (state, { selectedDocuments }) => ({
    ...state,
    eciDashboard: {
      ...state.eciDashboard,
      selectedDocuments,
    },
  })),

  on(AiActions.updateEciPagination, (state, { paginationState }) => ({
    ...state,
    eciDashboard: {
      ...state.eciDashboard,
      paginationState: {
        ...state.eciDashboard.paginationState,
        ...paginationState,
      },
    },
  })),

  on(AiActions.resetEciDashboardState, (state) => ({
    ...state,
    eciDashboard: aiInitialState.eciDashboard,
  })),

  // Chart-specific drill-down reducers
  on(AiActions.setActiveChartType, (state, { chartType }) => ({
    ...state,
    eciDashboard: {
      ...state.eciDashboard,
      activeChartType: chartType,
    },
  })),

  on(
    AiActions.initializeChartDrillDown,
    (state, { chartType, initialData }) => ({
      ...state,
      eciDashboard: {
        ...state.eciDashboard,
        chartDrillDownStates: {
          ...state.eciDashboard.chartDrillDownStates,
          [chartType]: {
            ...state.eciDashboard.chartDrillDownStates[chartType],
            currentData: initialData,
            drillDownHistory: [initialData],
            currentLevel: 0,
            isExpanded: false,
          },
        },
      },
    })
  ),

  on(
    AiActions.drillDownToNextLevel,
    (state, { chartType, selectedNode, nextLevelData }) => ({
      ...state,
      eciDashboard: {
        ...state.eciDashboard,
        chartDrillDownStates: {
          ...state.eciDashboard.chartDrillDownStates,
          [chartType]: {
            ...state.eciDashboard.chartDrillDownStates[chartType],
            currentData: nextLevelData,
            drillDownHistory: [
              ...state.eciDashboard.chartDrillDownStates[chartType]
                .drillDownHistory,
              nextLevelData,
            ],
            currentLevel:
              state.eciDashboard.chartDrillDownStates[chartType].currentLevel +
              1,
            selectedNode,
            isExpanded: true,
          },
        },
      },
    })
  ),

  on(AiActions.drillBackToPreviousLevel, (state, { chartType }) => {
    const currentState = state.eciDashboard.chartDrillDownStates[chartType]
    const newHistory = [...currentState.drillDownHistory]
    newHistory.pop() // Remove current level
    const previousData = newHistory[newHistory.length - 1] || []

    return {
      ...state,
      eciDashboard: {
        ...state.eciDashboard,
        chartDrillDownStates: {
          ...state.eciDashboard.chartDrillDownStates,
          [chartType]: {
            ...currentState,
            currentData: previousData,
            drillDownHistory: newHistory,
            currentLevel: Math.max(0, currentState.currentLevel - 1),
            selectedNode: null,
            isExpanded: newHistory.length > 1,
          },
        },
      },
    }
  }),

  on(AiActions.resetChartDrillDown, (state, { chartType }) => ({
    ...state,
    eciDashboard: {
      ...state.eciDashboard,
      chartDrillDownStates: {
        ...state.eciDashboard.chartDrillDownStates,
        [chartType]: createInitialChartDrillDownState(chartType),
      },
    },
  })),

  on(AiActions.updateChartTableData, (state, { chartType, tableData }) => ({
    ...state,
    eciDashboard: {
      ...state.eciDashboard,
      chartDrillDownStates: {
        ...state.eciDashboard.chartDrillDownStates,
        [chartType]: {
          ...state.eciDashboard.chartDrillDownStates[chartType],
          tableData,
        },
      },
    },
  })),

  on(AiActions.setChartSelectedNode, (state, { chartType, selectedNode }) => ({
    ...state,
    eciDashboard: {
      ...state.eciDashboard,
      chartDrillDownStates: {
        ...state.eciDashboard.chartDrillDownStates,
        [chartType]: {
          ...state.eciDashboard.chartDrillDownStates[chartType],
          selectedNode,
        },
      },
    },
  })),

  // ECA API Reducers
  on(AiActions.fetchEcaRelevance, (state) => ({
    ...state,
    isEcaRelevanceLoading: true,
    ecaRelevanceError: undefined,
  })),

  on(AiActions.fetchEcaRelevanceSuccess, (state, { ecaRelevanceSuccess }) => ({
    ...state,
    isEcaRelevanceLoading: false,
    ecaRelevanceSuccess,
    ecaRelevanceError: undefined,
  })),

  on(AiActions.fetchEcaRelevanceFailure, (state, { ecaRelevanceError }) => ({
    ...state,
    isEcaRelevanceLoading: false,
    ecaRelevanceSuccess: undefined,
    ecaRelevanceError,
  })),

  on(AiActions.fetchEcaDocumentTypes, (state) => ({
    ...state,
    isEcaDocumentTypesLoading: true,
    ecaDocumentTypesError: undefined,
  })),

  on(
    AiActions.fetchEcaDocumentTypesSuccess,
    (state, { ecaDocumentTypesSuccess }) => ({
      ...state,
      isEcaDocumentTypesLoading: false,
      ecaDocumentTypesSuccess,
      ecaDocumentTypesError: undefined,
    })
  ),

  on(
    AiActions.fetchEcaDocumentTypesFailure,
    (state, { ecaDocumentTypesError }) => ({
      ...state,
      isEcaDocumentTypesLoading: false,
      ecaDocumentTypesSuccess: undefined,
      ecaDocumentTypesError,
    })
  ),

  on(AiActions.fetchEcaTopicsRelevant, (state) => ({
    ...state,
    isEcaTopicsRelevantLoading: true,
    ecaTopicsRelevantError: undefined,
  })),

  on(
    AiActions.fetchEcaTopicsRelevantSuccess,
    (state, { ecaTopicsRelevantSuccess }) => ({
      ...state,
      isEcaTopicsRelevantLoading: false,
      ecaTopicsRelevantSuccess,
      ecaTopicsRelevantError: undefined,
    })
  ),

  on(
    AiActions.fetchEcaTopicsRelevantFailure,
    (state, { ecaTopicsRelevantError }) => ({
      ...state,
      isEcaTopicsRelevantLoading: false,
      ecaTopicsRelevantSuccess: undefined,
      ecaTopicsRelevantError,
    })
  ),

  on(AiActions.fetchEcaTopicsNonRelevant, (state) => ({
    ...state,
    isEcaTopicsNonRelevantLoading: true,
    ecaTopicsNonRelevantError: undefined,
  })),

  on(
    AiActions.fetchEcaTopicsNonRelevantSuccess,
    (state, { ecaTopicsNonRelevantSuccess }) => ({
      ...state,
      isEcaTopicsNonRelevantLoading: false,
      ecaTopicsNonRelevantSuccess,
      ecaTopicsNonRelevantError: undefined,
    })
  ),

  on(
    AiActions.fetchEcaTopicsNonRelevantFailure,
    (state, { ecaTopicsNonRelevantError }) => ({
      ...state,
      isEcaTopicsNonRelevantLoading: false,
      ecaTopicsNonRelevantSuccess: undefined,
      ecaTopicsNonRelevantError,
    })
  ),
  on(AiActions.downloadCSVSuccess, (state) => ({
    ...state,
    isDownloadingCSV: false,
    downloadCSVError: undefined,
  })),
  on(AiActions.downloadCSVFailure, (state, { downloadCSVError }) => ({
    ...state,
    isDownloadingCSV: false,
    downloadCSVError,
  }))
)

export function aiReducer(state: AIState | undefined, action: Action): AIState {
  return reducer(state, action)
}
