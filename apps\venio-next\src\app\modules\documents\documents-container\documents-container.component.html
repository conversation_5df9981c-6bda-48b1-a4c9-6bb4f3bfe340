@defer {
<venio-document-actions-toolbar />
} @placeholder {
<div class="t-flex t-items-center t-gap-4 t-flex-row t-w-full t-px-2">
  <kendo-skeleton
    [width]="30"
    [height]="30"
    shape="circle"
    *ngFor="let x of [1, 2, 3, 4, 5]"></kendo-skeleton>
  <kendo-skeleton
    width="10rem"
    [height]="40"
    *ngFor="let y of [1, 2, 3]"></kendo-skeleton>
</div>
}

<div
  class="t-w-full t-flex t-relative t-min-w-0 {{
    !isDefaultLayout()
      ? 't-h-[calc(100vh_-_6.4rem)]'
      : 't-h-[calc(100vh_-_3.79rem)]'
  }}">
  <kendo-splitter
    #mainSplitter
    orientation="horizontal"
    class="t-w-full t-h-full t-relative">
    <!-- Left pane: Browse and Breadcrumb panels -->
    <kendo-splitter-pane min="350px" [resizable]="true">
      <!-- Vertical splitter for left pane -->
      <kendo-splitter orientation="vertical" class="t-w-full t-h-full">
        <!-- Browse Panel -->
        <kendo-splitter-pane size="50%" min="20%" [resizable]="true">
          @defer{
          <venio-browse-panel-container />
          }
        </kendo-splitter-pane>

        <!-- Breadcrumb Panel - flexible size -->
        <kendo-splitter-pane min="20%" [resizable]="true">
          @defer{
          <venio-breadcrumb-container />
          }
        </kendo-splitter-pane>
      </kendo-splitter>
    </kendo-splitter-pane>

    <!-- Right pane: Document Panel - flexible size -->
    <kendo-splitter-pane
      size="76%"
      min="200px"
      [scrollable]="false"
      [resizable]="true"
      class="t-relative">
      @defer{
      <venio-document-table-container />
      }
    </kendo-splitter-pane>
  </kendo-splitter>
</div>
<div kendoDialogContainer></div>
