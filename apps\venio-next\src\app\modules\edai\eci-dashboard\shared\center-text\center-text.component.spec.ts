import { ComponentFixture, TestBed } from '@angular/core/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { AiFacade, SunburstChartType } from '@venio/data-access/ai'

import { CenterTextComponent } from './center-text.component'

describe('CenterTextComponent', () => {
  let component: CenterTextComponent
  let fixture: ComponentFixture<CenterTextComponent>

  const mockAiFacade = {
    setActiveChartType: jest.fn(),
    setEciFocusedSectionOpened: jest.fn(),
  } satisfies Partial<AiFacade>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CenterTextComponent],
      providers: [
        provideMockStore({}),
        { provide: AiFacade, useValue: mockAiFacade },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(CenterTextComponent)
    component = fixture.componentInstance

    // Set required input
    fixture.componentRef.setInput('centerText', SunburstChartType.DocumentTypes)

    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
