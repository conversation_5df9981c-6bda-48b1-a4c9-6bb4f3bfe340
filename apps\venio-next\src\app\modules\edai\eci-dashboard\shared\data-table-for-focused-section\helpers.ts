export interface DataTableForFocusedSection {
  category: string
  count: number
  subcategories: SubcategoryData[]
}
interface SubcategoryData {
  subcategory: string
  count: number
}
export const calcAllCounts = (data: DataTableForFocusedSection[]): number => {
  return data.reduce((total, item) => total + item.count, 0)
}

export const formatParentData = (data: DataTableForFocusedSection[]): any => {
  const grandTotal = calcAllCounts(data)
  console.log('Grand Total changed:', grandTotal)
  return data.map((item) => ({
    label: item.category,
    count: item.count,
    percent: Number((+item.count / +grandTotal) * 100).toFixed(2),
  }))
}

export const formatChildData = (data: DataTableForFocusedSection): any => {
  console.log('Data received for child formatting:', data)
  const total = data.count
  return data.subcategories.map((sub) => ({
    label: sub.subcategory,
    count: sub.count,
    percent: Number((+sub.count / +total) * 100).toFixed(2),
  }))
}
