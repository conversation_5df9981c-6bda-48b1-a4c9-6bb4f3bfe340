import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
  inject,
  Type,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  ButtonsModule,
  DropDownButtonModule,
} from '@progress/kendo-angular-buttons'
import { plusIcon, SVGIcon } from '@progress/kendo-svg-icons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { SvgIconNames } from '../../../../constants/svg-icon-names'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import {
  PanelComponentTypes,
  CommandEventTypes,
} from '@venio/shared/models/constants'
import { CommandsFacade } from '@venio/data-access/common'
import { UserGroupRightCheckDirective } from '@venio/feature/shared/directives'
import { IframeMessengerFacade } from '@venio/data-access/iframe-messenger'
import { filter, Subject, takeUntil } from 'rxjs'
import {
  ReviewSetStateService,
  SearchFacade,
  UserRights,
} from '@venio/data-access/review'
import { VenioNotificationService } from '@venio/feature/notification'
import { ShortcutKeyDictionaryComponent } from '@venio/ui/shortcut-key-dictionary'
import { RedactionProgressBarComponent } from '../../../layout/layout-toolbar/redaction-progress-bar/redaction-progress-bar.component'

@Component({
  selector: 'venio-document-actions-toolbar',
  standalone: true,
  imports: [
    CommonModule,
    DropDownButtonModule,
    IconsModule,
    DropDownListModule,
    ButtonsModule,
    UserGroupRightCheckDirective,
    ShortcutKeyDictionaryComponent,
    RedactionProgressBarComponent,
  ],
  templateUrl: './document-actions-toolbar.component.html',
  styleUrls: ['./document-actions-toolbar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentActionsToolbarComponent implements OnInit, OnDestroy {
  public iconCollection = signal<Array<SVGIcon>>(null)

  public readonly toDestroy$ = new Subject<void>()

  private panelVisibility: Record<string, boolean> = {}

  public UserRights = UserRights

  private iconNamesForActions = [
    SvgIconNames.browseIcon,
    SvgIconNames.searchPanel,
    SvgIconNames.documentPanel,
    SvgIconNames.documentLocation,
    SvgIconNames.upload,
    SvgIconNames.widget,
  ]

  public widgetSvgIcon: SVGIcon

  public svgPlusIcon = plusIcon

  public svgScatterIcon: SVGIcon

  public historyLogIconComp: Promise<Type<unknown>>

  public documentDesignerDropdownLazyComp: Promise<Type<unknown>>

  public projectSelectorComp: Promise<Type<unknown>>

  public projectInfoComp: Promise<Type<unknown>>

  public reviewSetState = inject(ReviewSetStateService)

  constructor(
    private changeDetectorRef: ChangeDetectorRef,
    private commandsFacade: CommandsFacade,
    private iframeMessengerFacade: IframeMessengerFacade,
    private notificationService: VenioNotificationService,
    private searchFacade: SearchFacade
  ) {}

  #loadSvgContentAndInit(): void {
    import('../../../../constants/svg-icon-contents').then((svgModule) => {
      const { svgIconContents, svgScatterIcon } = svgModule
      this.svgScatterIcon = svgScatterIcon
      const icons = svgIconContents()
      const mapped = Object.keys(icons)
        .filter((key: SvgIconNames) => this.iconNamesForActions.includes(key))
        .reduce((all, key) => {
          const svgIcon = {
            viewBox: icons[key].viewBox,
            name: key,
            content: icons[key].content,
          } as SVGIcon
          if (key === SvgIconNames.widget) {
            this.widgetSvgIcon = svgIcon
          } else {
            all.push(svgIcon)
          }

          return all
        }, [] as Array<SVGIcon>)
      this.iconCollection.set(mapped)
      this.changeDetectorRef.markForCheck()
    })
  }

  #loadHistoryLogIconForMicroApp(): void {
    this.iframeMessengerFacade.selectLoadedAsMicroApp$
      .pipe(filter((isMicro) => isMicro))
      .subscribe(() => {
        this.historyLogIconComp = import(
          '../../../layout/layout-toolbar/toolbar-history-log/toolbar-history-log.component'
        ).then(({ ToolbarHistoryLogComponent }) => ToolbarHistoryLogComponent)
      })
  }

  #loadDocumentDesignerDropdownComp(): void {
    this.documentDesignerDropdownLazyComp = import(
      '../document-view-designer/document-view-designer-dropdown/document-view-designer-dropdown.component'
    ).then(
      ({ DocumentViewDesignerDropdownComponent }) =>
        DocumentViewDesignerDropdownComponent
    )
  }

  #loadProjectSelectorComp(): void {
    this.projectSelectorComp = import(
      '../../../layout/layout-toolbar/project-selector/project-selector.component'
    ).then(({ ProjectSelectorComponent }) => ProjectSelectorComponent)
  }

  #loadProjectInfoComp(): void {
    this.projectInfoComp = import(
      '../../../layout/layout-toolbar/project-info/project-info.component'
    ).then(({ ProjectInfoComponent }) => ProjectInfoComponent)
  }

  public ngOnInit(): void {
    this.#loadSvgContentAndInit()
    this.#loadHistoryLogIconForMicroApp()
    this.#loadDocumentDesignerDropdownComp()
    this.#loadProjectSelectorComp()
    this.#loadProjectInfoComp()
    //Subscribe success and failure of search history deletion here to show success/error message instead of inside the component itself to avoid multiple subscription
    this.#selectSearchHistoryDeletionResponses()
  }

  #selectSearchHistoryDeletionResponses(): void {
    this.searchFacade.getDeleteSearchHistorySuccessResponse$
      .pipe(
        filter((success) => !!success),
        takeUntil(this.toDestroy$)
      )
      .subscribe((success) => {
        this.notificationService.showSuccess(success.message)
        this.#resetDelSearchHistoryState()
      })

    this.searchFacade.getDeleteSearchHistoryFailureResponse$
      .pipe(
        filter((error) => !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        this.notificationService.showError(error.message)
        this.#resetDelSearchHistoryState()
      })
  }

  #resetDelSearchHistoryState(): void {
    this.searchFacade.resetSearchState([
      'delSearchHistorySuccessResponse',
      'delSearchHistoryFailureResponse',
    ])
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public actionClicked(item: SVGIcon): void {
    this.panelVisibility[item.name] = !this.panelVisibility[item.name]
    let componentType: PanelComponentTypes
    switch (item.name) {
      case SvgIconNames.browseIcon:
        componentType = PanelComponentTypes.BROWSE_PANEL
        break
      case SvgIconNames.searchPanel:
        componentType = PanelComponentTypes.SEARCH_PANEL
        break
      case SvgIconNames.documentPanel:
        componentType = PanelComponentTypes.DOCUMENT_PREVIEW_PANEL
        break
    }
    this.commandsFacade.dispatchCommand({
      type: CommandEventTypes.TogglePanelVisibility,
      data: {
        componentType,
        visible: this.panelVisibility[item.name],
      },
    })
  }
}
