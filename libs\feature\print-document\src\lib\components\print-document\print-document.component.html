<form [formGroup]="printForm" class="t-h-full" #printDocumentForm>
  <div class="t-flex t-flex-col t-h-full">
    <div class="t-py-1 t-pl-1">
      <div class="t-flex t-items-center" formGroupName="tiffSourceOption">
        <input
          type="checkbox"
          class="custom-checkbox"
          id="queue-files-for-imaging-if-not-available"
          kendoCheckBox
          formControlName="queueFilesForImagingIfNotAvailable"
          #queueFilesCheckbox />
        <kendo-label
          class="k-checkbox-label"
          for="queue-files-for-imaging-if-not-available"
          [ngClass]="{
            't-text-primary t-font-medium':
              printForm.get(
                'tiffSourceOption.queueFilesForImagingIfNotAvailable'
              )?.value &&
              !printForm.get(
                'tiffSourceOption.queueFilesForImagingIfNotAvailable'
              )?.disabled
          }"
          text="Queue files for imaging if not available">
          <span
            class="t-text-xs t-text-gray-900 t-ml-2"
            [ngClass]="{
              't-text-primary t-font-medium':
                printForm.get(
                  'tiffSourceOption.queueFilesForImagingIfNotAvailable'
                )?.value &&
                !printForm.get(
                  'tiffSourceOption.queueFilesForImagingIfNotAvailable'
                )?.disabled
            }"
            >({{ imagedDocuments }} document has image out of
            {{ selectedDocuments }} document)</span
          >
        </kendo-label>
      </div>
    </div>
    <div
      class="t-mt-2 t-h-full t-overflow-y-auto t-flex-grow v-custom-grey-bg t-border-gray-300-temp t-rounded-sm t-h-full t-pb-1 t-border t-border-solid t-pt-1">
      <div class="t-flex t-flex-col t-h-full" #mainComponent>
        <div class="t-grow-0 t-mx-1 t-mt-2" #imageSourceComponent>
          <ng-container
            *ngComponentOutlet="
              printImageSourceComponent | async
            "></ng-container>
        </div>

        <div class="t-grow">
          <kendo-expansionpanel
            class="v-custom-expansion-panel"
            [expanded]="isExpanded"
            (stateChange)="onPanelToggle()">
            <ng-template kendoExpansionPanelTitleDirective>
              <div class="t-text-[#1DBADC] t-text-sm">
                <span>Advance Settings</span>
              </div>
            </ng-template>
            <div
              #advancedOptions
              class="t-overflow-y-auto t-flex-grow k-border-t t-border-t-gray t-border-solid t-pt-1">
              <div class="t-flex t-flex-row t-px-4 t-my-5 t-mx-1">
                <div class="t-w-3/5">
                  <ng-container
                    *ngComponentOutlet="
                      printSlipsheetOptionComponent | async
                    "></ng-container>
                </div>

                <div class="t-flex t-pt-2 t-mt-5 t-mb-1">
                  <div
                    class="t-bg-green-200 t-w-10 custom-svg-line t-bg-no-repeat t-bg-center"></div>
                </div>

                <div class="t-w-2/5">
                  <ng-container
                    *ngComponentOutlet="
                      printEndorsementOptionComponent | async
                    "></ng-container>
                </div>
              </div>

              <div class="t-flex t-flex-row t-px-4 t-my-5 t-mx-1">
                <div class="t-w-1/2">
                  <ng-container
                    *ngComponentOutlet="
                      printWatermarkOptionComponent | async
                    "></ng-container>
                </div>

                <div class="t-flex t-pt-2 t-mt-5 t-mb-1">
                  <div
                    class="t-bg-green-200 t-w-10 custom-svg-line t-bg-no-repeat t-bg-center"></div>
                </div>

                <div class="t-w-1/2">
                  <ng-container
                    *ngComponentOutlet="
                      printOutputOptionComponent | async
                    "></ng-container>
                </div>
              </div>

              <div class="t-px-4 t-my-5 t-mx-1">
                @defer {
                <venio-print-resize-image></venio-print-resize-image>
                }
              </div>
            </div>
          </kendo-expansionpanel>
        </div>
      </div>
    </div>
  </div>
</form>
<ng-template #printDocumentActionTemplate>
  <div class="t-flex t-items-center t-justify-between">
    <div class="t-flex t-flex-row">
      <div class="">
        <div class="t-m-1">
          <button
            kendoButton
            id="tool-tip"
            class="t-p-1"
            kendoTooltip
            kendoButton
            kendoPopoverAnchor
            fillMode="clear"
            [popover]="printToolTip">
            <span
              venioSvgLoader
              [svgUrl]="infoSvgUrl"
              height="1.5rem"
              width="1.5rem"></span>
            <!--info icon-->
          </button>
          <kendo-popover #printToolTip position="top">
            <ng-template kendoPopoverTitleTemplate>
              <p class="t-flex t-flex-col t-text-sm">
                <strong>Note:</strong
                ><span>
                  All tiff pages of selected documents will be converted to a
                  PDF.</span
                >
                <span
                  >Zip file will be created as print job name when multiple PDF
                  files are created.</span
                >
                PDF/Zip file created can be downloaded from print status.
              </p>
            </ng-template>
          </kendo-popover>
        </div>
      </div>
    </div>
    <div class="t-flex-1 t-mx-4">
      <div
        class="t-flex t-flex-row t-m-1 t-text-[10px] t-font-medium"
        *ngIf="showWarning">
        <span class="t-mr-2 t-text-[#ED7428]">NOTE</span>
        <span
          >Exceeded Document Limit! You've selected
          {{ selectedDocuments }} documents, but the allocated limit is
          {{ printLimit?.TiffPrintDocLimit }}. Please review and reduce your
          selection accordingly or contact admin.
        </span>
      </div>
    </div>
    <div class="t-flex t-flex-row t-justify-end t-gap-4">
      <div>
        <button
          kendoButton
          id="summary"
          class="t-m-1 v-custom-secondary-button ml-2"
          themeColor="secondary"
          fillMode="outline"
          type="submit"
          kendoPopoverAnchor
          [popover]="summaryPopover"
          (click)="onSummaryClicked()">
          Summary
        </button>
        <kendo-popover #summaryPopover [width]="550" position="top">
          <ng-template kendoPopoverTitleTemplate>
            <ng-container
              *ngComponentOutlet="printSummaryComponent | async"></ng-container>
          </ng-template>
        </kendo-popover>
        <button
          kendoButton
          id="print-submit"
          class="t-m-1 v-custom-secondary-button ml-2"
          [disabled]="printForm.invalid || checkName || disableQueueButton"
          themeColor="secondary"
          fillMode="outline"
          (click)="onPrintSubmit()">
          <span class="k-icon k-i-print"></span> Print
        </button>
      </div>
    </div>
  </div>
</ng-template>
