import { ComponentFixture, TestBed } from '@angular/core/testing'
import { InviteUploadFormComponent } from './invite-upload-form.component'
import { of } from 'rxjs'
import { signal } from '@angular/core'
import { UserFacade } from '@venio/data-access/common'
import { FormBuilder, ReactiveFormsModule } from '@angular/forms'
import { provideAnimations } from '@angular/platform-browser/animations'
import { provideMockStore } from '@ngrx/store/testing'
import {
  UsersListModel,
  InviteUploadUserGridModel,
} from '@venio/shared/models/interfaces'
import { InviteUploadLocalState } from '../invite-upload-container/invite-upload-local-state'
import { provideHttpClient } from '@angular/common/http'
import { ControlSettingService } from '@venio/data-access/control-settings'

describe('InviteUploadFormComponent', () => {
  let component: InviteUploadFormComponent
  let fixture: ComponentFixture<InviteUploadFormComponent>

  const mockInviteUploadLocalState: Partial<InviteUploadLocalState> = {
    shareToExternalUsers: signal(false),
    filteredInternalUsers: signal<UsersListModel[]>([
      { email: '<EMAIL>', userName: 'Internal User' },
    ]),
    filteredExternalUsers: signal<UsersListModel[]>([
      { email: '<EMAIL>', userName: 'External User' },
    ]),
    selectedExternalUsers: signal<string[]>([]),
    selectedInternalUsers: signal<string[]>([]),
    userMessage: signal(''),
    gridColumnMap: signal(new Map<string, InviteUploadUserGridModel[]>()),

    setSharedToExternalUsers: jest.fn(),
    setGridColumns: jest.fn(),
    setFilteredInternalUsers: jest.fn(),
    setFilteredExternalUsers: jest.fn(),
    setUserMessage: jest.fn(),
    addFilteredExternalUser: jest.fn(),
    addSelectedInternalUser: jest.fn(),
    addSelectedExternalUser: jest.fn(),
  }

  const mockUserFacade = {
    fetchUserListToInvite: jest.fn(),
    fetchExternalUserListToInvite: jest.fn(),
    selectUserListToInviteSuccessResponse$: of([
      { email: '<EMAIL>', userName: 'Internal User' },
    ]),
    selectUserListToInviteErrorResponse$: of(null),
    selectExternalUserToInviteSuccessResponse$: of([
      { email: '<EMAIL>', userName: 'External User' },
    ]),
    selectIsInvitationInProgress$: of(false),
  }

  const mockControlSettings = {
    getControlSetting: {
      ALLOW_EXTERNAL_USER_DATA_UPLOAD: true,
    },
  }

  const mockProjectId = 1

  const mockInviteUploadForm = new FormBuilder().group({
    shareToExternalUsers: {
      value: false,
      disabled: false,
    },
    instruction: '',
    newEmail: {
      value: '',
      disabled: true,
    },
    validity: 1,
  })

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [InviteUploadFormComponent, ReactiveFormsModule],
      providers: [
        provideHttpClient(),
        provideMockStore({}),
        provideAnimations(),
        { provide: UserFacade, useValue: mockUserFacade },
        { provide: ControlSettingService, useValue: mockControlSettings },
        {
          provide: InviteUploadLocalState,
          useValue: mockInviteUploadLocalState,
        },
        FormBuilder,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(InviteUploadFormComponent)
    component = fixture.componentInstance
    fixture.componentRef.setInput('inviteUploadForm', mockInviteUploadForm)
    fixture.componentRef.setInput('selectedProjectId', mockProjectId)
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should initialize the form with default values', () => {
    // GIVEN: Form is already initialized

    // WHEN: Retrieving form values
    const formValue = component.inviteUploadForm().getRawValue()

    // THEN: Verify default form values
    expect(formValue).toEqual({
      shareToExternalUsers: false,
      instruction: '',
      newEmail: '',
      validity: 1,
    })
  })

  it('should validate controls with properly disabled/enabled', () => {
    // GIVEN: Form controls are initialized

    // WHEN: Retrieving control states
    const shareToExternalUsers = component
      .inviteUploadForm()
      .get('shareToExternalUsers')
    const newEmail = component.inviteUploadForm().get('newEmail')

    // THEN: Verify controls' disabled/enabled state
    expect(shareToExternalUsers?.disabled).toBe(false) // Enabled
    expect(newEmail?.disabled).toBe(true) // Disabled
  })

  it('should enable newEmail control when shareToExternalUsers is checked', () => {
    // GIVEN: shareToExternalUsers is initially false
    const shareToExternalUsersControl = component
      .inviteUploadForm()
      .get('shareToExternalUsers')

    // WHEN: shareToExternalUsers is toggled to true
    shareToExternalUsersControl?.setValue(true)
    fixture.detectChanges()

    // THEN: newEmail control should be enabled
    const newEmailControl = component.inviteUploadForm().get('newEmail')
    expect(newEmailControl?.disabled).toBe(false)
  })

  it('should disable newEmail control when shareToExternalUsers is unchecked', () => {
    // GIVEN: shareToExternalUsers is initially true
    const shareToExternalUsersControl = component
      .inviteUploadForm()
      .get('shareToExternalUsers')
    shareToExternalUsersControl?.setValue(true)
    fixture.detectChanges()

    // WHEN: shareToExternalUsers is toggled to false
    shareToExternalUsersControl?.setValue(false)
    fixture.detectChanges()

    // THEN: newEmail control should be disabled
    const newEmailControl = component.inviteUploadForm().get('newEmail')
    expect(newEmailControl?.disabled).toBe(true)
  })

  it('should add a valid external user to the list', () => {
    // GIVEN: A valid email is entered
    const newEmailControl = component.inviteUploadForm().get('newEmail')
    newEmailControl?.setValue('<EMAIL>')

    // WHEN: Adding the external user
    component.addExternalUser()

    // THEN: The user should be added to the filtered external users
    expect(
      mockInviteUploadLocalState.addFilteredExternalUser
    ).toHaveBeenCalledWith({
      email: '<EMAIL>',
      userName: '<EMAIL>',
    })
  })
})
