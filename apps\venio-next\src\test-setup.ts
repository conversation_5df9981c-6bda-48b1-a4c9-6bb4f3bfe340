import 'jest-preset-angular/setup-jest'
import '@angular/localize/init'
/**
 * @link https://stackoverflow.com/a/48750402/4444844
 */
import <PERSON><PERSON> from 'dexie'
import indexedDB from 'fake-indexeddb'
import { TextEncoder, TextDecoder } from 'util'

global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

Dexie.dependencies.indexedDB = indexedDB
;(global as any).URL.createObjectURL = jest.fn(() => 'dummy-url')
;(global as any).URL.revokeObjectURL = jest.fn()
global.encryptStr = jest.fn()
global.decryptStr = jest.fn()

global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// Mock PlotlyJS globally for all tests
const mockPlotly = {
  newPlot: jest.fn().mockResolvedValue({}),
  react: jest.fn().mockResolvedValue({}),
  restyle: jest.fn().mockResolvedValue({}),
  relayout: jest.fn().mockResolvedValue({}),
  redraw: jest.fn().mockResolvedValue({}),
  purge: jest.fn().mockResolvedValue({}),
  downloadImage: jest.fn().mockResolvedValue({}),
  toImage: jest.fn().mockResolvedValue({}),
  validate: jest.fn().mockReturnValue(true),
  Plots: {
    resize: jest.fn(),
  },
}

// Set up global Plotly object
;(global as any).Plotly = mockPlotly
;(global as any).window = global.window || {}
;(global as any).window.Plotly = mockPlotly
