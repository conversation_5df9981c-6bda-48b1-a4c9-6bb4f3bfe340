import {
  ChangeDetectionStrategy,
  Component,
  ViewChild,
  TemplateRef,
  inject,
  On<PERSON><PERSON><PERSON>,
  computed,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { PopupModule } from '@progress/kendo-angular-popup'
import { IconsModule } from '@progress/kendo-angular-icons'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import {
  SvgLoaderDirective,
  UserGlobalRightCheckDirective,
} from '@venio/feature/shared/directives'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import {
  WindowModule,
  WindowRef,
  WindowService,
} from '@progress/kendo-angular-dialog'
import { FormsModule } from '@angular/forms'
import { chevronDownIcon } from '@progress/kendo-svg-icons'
import { filter, Subject, switchMap, take, takeUntil } from 'rxjs'
import {
  CompositeLayoutFacade,
  CompositeLayoutState,
  Layout,
  LayoutResponseModel,
  UserRights,
} from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'
import { ResponseModel } from '@venio/shared/models/interfaces'

@Component({
  selector: 'venio-layout-menu',
  standalone: true,
  imports: [
    CommonModule,
    PopupModule,
    ButtonsModule,
    InputsModule,
    IconsModule,
    TooltipsModule,
    SvgLoaderDirective,
    LoaderModule,
    WindowModule,
    FormsModule,
    UserGlobalRightCheckDirective,
    TooltipsModule,
  ],
  templateUrl: './layout-menu.component.html',
  styleUrl: './layout-menu.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LayoutMenuComponent implements OnInit, OnDestroy {
  @ViewChild('windowTitleBar', { static: true })
  public windowTitleBar!: TemplateRef<unknown>

  public icons = {
    chevronDownIcon: chevronDownIcon,
  }

  public get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  private toDestroy$: Subject<void> = new Subject<void>()

  private layoutFacade: CompositeLayoutFacade = inject(CompositeLayoutFacade)

  public layoutState: CompositeLayoutState = inject(CompositeLayoutState)

  public readonly rights = UserRights

  private activatedRoute: ActivatedRoute = inject(ActivatedRoute)

  public readonly selectedLayoutName = computed(() => {
    return this.layoutState.userSelectedLayout().layoutName
  })

  constructor(private windowService: WindowService) {}

  public ngOnInit(): void {
    this.#loadLayout()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  #loadLayout(): void {
    this.layoutFacade.loadLayoutAction$
      .pipe(
        switchMap((layoutId) => {
          return this.layoutFacade.fetchLayout$(layoutId)
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        const layout: Layout = response.data
        this.layoutState.userSelectedLayout.set(layout)
        this.layoutFacade.notifyLoadLayout.next()
      })
  }

  public CreateNewLayout(): void {
    import('../layout-panel-selection/layout-panel-selection.component').then(
      (d) => {
        const windowRef: WindowRef = this.windowService.open({
          content: d.LayoutPanelSelectionComponent,
          state: 'maximized',
          top: 100,
          cssClass: 'v-custom-window',
          titleBarContent: this.windowTitleBar,
          resizable: false,
          draggable: false,
        })

        windowRef.result
          .pipe(
            filter((res) => !!res),
            take(1)
          )
          .subscribe(() => {
            windowRef.content.destroy()
          })
      }
    )
  }

  public viewAllLayouts(): void {
    import('../layout-listing/layout-listing.component').then((d) => {
      const windowRef: WindowRef = this.windowService.open({
        content: d.LayoutListingComponent,
        state: 'maximized',
        top: 100,
        cssClass: 'v-custom-window v-custom-window-padding',
        titleBarContent: this.windowTitleBar,
        resizable: false,
        draggable: false,
      })

      windowRef.result
        .pipe(
          filter((res) => !!res),
          take(1)
        )
        .subscribe(() => {
          windowRef.content.destroy()
        })
    })
  }

  public fetchAllLayouts(): void {
    this.layoutFacade
      .fetchLayouts$(this.projectId)
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((response: ResponseModel) => {
        const layouts: LayoutResponseModel[] = response.data
        this.layoutState.userLayouts.set([
          {
            isPrivate: false,
            layoutName: 'Default',
            layoutId: -1,
            isFavorite: layouts.every((f) => !f.isFavorite),
            allowManageBasedOnClient: false,
          },
          ...layouts,
        ])
      })
  }

  public loadLayout(layoutId: number): void {
    this.layoutFacade.loadLayoutAction$.next(layoutId)
  }
}
