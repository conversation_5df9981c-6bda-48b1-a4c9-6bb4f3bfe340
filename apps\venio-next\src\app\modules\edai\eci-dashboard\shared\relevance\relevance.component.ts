import { Component, inject } from '@angular/core'
import { PlotlyModule } from 'angular-plotly.js'
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component'
import { CenterTextComponent } from '../center-text/center-text.component'
import { NgFor } from '@angular/common'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { toSignal } from '@angular/core/rxjs-interop'
import {
  AiFacade,
  ECADashboardType,
  SunburstChartType,
} from '@venio/data-access/ai'
import { relevanceChartColors } from '../../constants/colors'

@Component({
  selector: 'venio-relevance',
  standalone: true,
  imports: [
    PlotlyModule,
    TitleAndDownloadComponent,
    CenterTextComponent,
    NgFor,
    LoaderModule,
  ],
  templateUrl: './relevance.component.html',
  styleUrl: './relevance.component.scss',
})
export class RelevanceComponent {
  public readonly sunburstChartType = SunburstChartType
  public readonly dashboardType = ECADashboardType

  private readonly aiFacade = inject(AiFacade)

  public readonly relevanceData = toSignal(
    this.aiFacade.selectEciRelevanceData$,
    { initialValue: null }
  )

  // ECA API data
  public readonly ecaRelevanceData = toSignal(
    this.aiFacade.selectEcaRelevanceSuccess$,
    { initialValue: null }
  )

  public readonly isEcaRelevanceLoading = toSignal(
    this.aiFacade.selectIsEcaRelevanceLoading$,
    { initialValue: false }
  )

  public readonly ecaRelevanceError = toSignal(
    this.aiFacade.selectEcaRelevanceError$,
    { initialValue: null }
  )

  public readonly sunburstChartColors = relevanceChartColors

  public getColor(i: number): string {
    return this.sunburstChartColors[i + 1]
  }

  public get labels(): string[] {
    // Use ECA API data if available, otherwise fallback to mock data
    const ecaData = this.ecaRelevanceData()
    if (ecaData?.data) {
      return ecaData.data.map((item: any) => item.relevanceType)
    }
    return this.relevanceData()?.labels || []
  }

  public get graph(): any {
    try {
      // Use ECA API data if available, otherwise fallback to mock data
      const ecaData = this.ecaRelevanceData()
      let values: number[] = []
      let colors: string[] = []

      if (ecaData?.data && Array.isArray(ecaData.data)) {
        values = ecaData.data
          .map((item: any) => item?.docCount || 0)
          .filter((count) => count > 0)

        if (values.length > 0) {
          colors = this.#generateColors(values.length)
        }
      } else {
        const data = this.relevanceData()
        values = data?.values || []
        colors = data?.colors || []
      }

      // Ensure we have valid data
      if (!values.length) {
        values = [1]
        colors = ['#e5e7eb']
      }

      return {
        data: [
          {
            values,
            type: 'pie',
            hole: 0.5,
            marker: { colors },
            textinfo: 'none',
          },
        ],
        layout: {
          autosize: true,
          title: '',
          automargin: false,
          margin: { t: 20, r: 20, b: 20, l: 20 },
          height: 400,
          width: undefined,
          showlegend: false,
          plot_bgcolor: 'rgba(0,0,0,0)',
          paper_bgcolor: 'rgba(0,0,0,0)',
        },
      }
    } catch (error) {
      console.error('Error generating relevance chart:', error)
      return {
        data: [
          {
            values: [1],
            type: 'pie',
            hole: 0.5,
            marker: { colors: ['#e5e7eb'] },
            textinfo: 'none',
          },
        ],
        layout: {
          autosize: true,
          title: '',
          automargin: false,
          margin: { t: 20, r: 20, b: 20, l: 20 },
          height: 400,
          width: undefined,
          showlegend: false,
          plot_bgcolor: 'rgba(0,0,0,0)',
          paper_bgcolor: 'rgba(0,0,0,0)',
        },
      }
    }
  }

  #generateColors(count: number): string[] {
    // Use the same color scheme as the legend (excluding 'transparent' at index 0)
    const chartColors = this.sunburstChartColors.slice(1)

    // Repeat colors if we need more than the available set
    const colors: string[] = []
    for (let i = 0; i < count; i++) {
      colors.push(chartColors[i % chartColors.length])
    }
    return colors
  }

  public config = {
    responsive: true,
    displayModeBar: false,
    displaylogo: false,
    modeBarButtonsToRemove: [
      'toImage',
      'sendDataToCloud',
      'editInChartStudio',
      'zoom2d',
      'select2d',
      'pan2d',
      'lasso2d',
      'autoScale2d',
      'resetScale2d',
    ],
    // Ensure proper resizing behavior
    autosizable: true,
    fillFrame: false,
    frameMargins: 0,
  }
}
