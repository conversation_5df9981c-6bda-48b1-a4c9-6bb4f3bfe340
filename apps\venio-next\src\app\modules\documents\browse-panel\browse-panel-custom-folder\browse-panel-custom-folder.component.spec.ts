import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BrowsePanelCustomFolderComponent } from './browse-panel-custom-folder.component'
import { ActivatedRoute } from '@angular/router'
import {
  CustomFolderTabTreeState,
  DynamicFolderFacade,
  FieldFacade,
  FolderFacade,
  ReviewParamService,
  SearchFacade,
  StartupsFacade,
} from '@venio/data-access/review'
import { BehaviorSubject, of } from 'rxjs'
import { provideMockStore } from '@ngrx/store/testing'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import { DialogService } from '@progress/kendo-angular-dialog'
import { NotificationService } from '@progress/kendo-angular-notification'
import { By } from '@angular/platform-browser'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { FolderTabType } from '@venio/shared/models/constants'

describe('BrowsePanelCustomFolderComponent', () => {
  let component: BrowsePanelCustomFolderComponent
  let fixture: ComponentFixture<BrowsePanelCustomFolderComponent>
  let mockSearchFacade: SearchFacade

  const mockFolderTabTreeState$ = new BehaviorSubject<CustomFolderTabTreeState>(
    {
      actionType: FolderTabType.CUSTOM,
      expandedIds: ['-1', '-2'],
      rowIndex: 0,
    }
  )

  const dynamicFolders = [
    {
      folderId: 10,
      folderName: 'Folder 1',
      folderNote: '',
      isPlaceholder: false,
      clientId: 2,
      lineage: '',
      parentFolderId: -1,
      sortOrder: 0,
      isGlobal: true,
      id: 'G_1',
      parentId: '-1',
      searchSettings: {},
      groupPermissions: [],
      isDynamicFolder: true,
    },
  ]

  const staticFolders = [
    {
      folderId: 1,
      folderName: 'Folder 2',
      parentFolderId: -1,
      accessType: 'PUBLIC',
      folderLineage: '\\Folder 2',
      folderIdlineage: '\\1',
      fileCount: 10,
      isSystemFolder: false,
      isRelativePath: null,
      customFieldInfoId: null,
      folderProjectGroupAssociations: [],
      groupAccess: null,
      userGroupPermissionFolder: 'READ_WRITE',
      parentFileCount: 0,
      description: 'test search',
      folderOrder: 1,
      belowFolderId: 0,
      lineage: '\\Folder 2',
      id: 'F_1',
      parentId: '-2',
      isPlaceholder: false,
      isDynamicFolder: false,
    },
  ]

  const dynamicFolderSearchScope$ = new BehaviorSubject<any>([])

  const staticFolderSearchScope$ = new BehaviorSubject<any>(staticFolders)

  beforeEach(async () => {
    const mockDynamicFolderFacade = {
      getDynamicFolders$: of(dynamicFolders), // Provide appropriate mock data
    }

    const mockFolderFacade = {
      getStaticFolders$: of(staticFolders), // Provide appropriate mock data
      getSelectedFolderTreeState$: mockFolderTabTreeState$.asObservable(),
    }

    const mockStartupsFacade = {
      hasGroupRight$: jest.fn().mockReturnValue(of(true)),
    }

    const mockTotalHitCount$ = of(10) // Mock total hit count to match fileCount

    mockSearchFacade = {
      getTotalHitCount$: mockTotalHitCount$, // Mock the observable
      getDynamicFolderSearchScope$: dynamicFolderSearchScope$.asObservable(),
      getStaticFolderSearchScope$: staticFolderSearchScope$.asObservable(),
    } as any

    await TestBed.configureTestingModule({
      imports: [BrowsePanelCustomFolderComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: BreadcrumbFacade,
          useValue: jest.fn(),
        },
        { provide: DynamicFolderFacade, useValue: mockDynamicFolderFacade },
        { provide: FolderFacade, useValue: mockFolderFacade },
        { provide: SearchFacade, useValue: mockSearchFacade }, // Provide the mock SearchFacade
        FieldFacade,
        { provide: StartupsFacade, useValue: mockStartupsFacade },
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({ projectId: '2' }),
            snapshot: {
              queryParams: {
                projectId: '2',
              },
            },
          },
        },
        {
          provide: DialogService,
          useValue: jest.fn().mockImplementation(() => ({
            open: jest.fn().mockReturnValue({ result: Promise.resolve(true) }),
            content: jest.fn(),
            close: jest.fn(),
            // Add other methods and properties you use
          })),
        },
        {
          provide: NotificationService,
          useValue: jest.fn().mockImplementation(() => ({
            show: jest.fn().mockImplementation((message, type, duration) => {
              // Optionally, simulate some behavior or state change
            }),
            hide: jest.fn(),
            // Add other methods as necessary
          })),
        },
        ReviewParamService,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(BrowsePanelCustomFolderComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should display folder name with file count when fileCount > 0', () => {
    component.ngOnInit() // Trigger the component initialization
    fixture.detectChanges() // Trigger the change detection to apply the data

    // Select all elements that match the selector
    const folderCells = fixture.debugElement.queryAll(By.css('.t-flex'))

    // Find the correct cell containing "Folder 2 (10)"
    const matchingCell = folderCells.find((cell) => {
      const textContent = cell.nativeElement.textContent
        .replace(/\s+/g, ' ')
        .trim()
      return textContent.includes('Folder 2 (10)')
    })

    // Ensure the cell was found
    expect(matchingCell).toBeTruthy()

    // If found, validate the content
    const cellContent = matchingCell?.nativeElement.textContent
      .replace(/\s+/g, ' ')
      .trim()
    expect(cellContent).toContain('Folder 2 (10)')
  })

  it('should display folder name only when fileCount is 0', () => {
    component.ngOnInit() // Trigger the component initialization
    fixture.detectChanges() // Trigger the change detection to apply the data

    // Select all elements that match the selector
    const folderCells = fixture.debugElement.queryAll(By.css('.t-flex'))

    // Find the correct cell containing "Folder 1"
    const matchingCell = folderCells.find((cell) => {
      const textContent = cell.nativeElement.textContent
        .replace(/\s+/g, ' ')
        .trim()
      return textContent.includes('Folder 1')
    })

    expect(matchingCell).toBeTruthy()

    // If found, validate the content
    const cellContent = matchingCell?.nativeElement.textContent
      .replace(/\s+/g, ' ')
      .trim()
    expect(cellContent).toMatch(/^Folder 1$/)
  })

  it('should match fileCount with totalHitCount', () => {
    const expectedFileCount = staticFolders[0].fileCount
    let actualTotalHitCount: number

    // Subscribe to the total hit count
    mockSearchFacade.getTotalHitCount$.subscribe((count) => {
      actualTotalHitCount = count
    })

    // Trigger component initialization
    component.ngOnInit()
    fixture.detectChanges()

    // Validate that fileCount matches totalHitCount
    expect(actualTotalHitCount).toEqual(expectedFileCount)
  })
})
