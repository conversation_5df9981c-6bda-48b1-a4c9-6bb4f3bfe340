import { inject, Injectable } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import {
  DocumentCodingFacade,
  DocumentTagFacade,
  SaveDocumentCodingPayloadModel,
  SaveDocumentTagPayloadModel,
  SaveTagPayloadModel,
} from '@venio/data-access/document-utility'
import {
  DocumentsFacade,
  FileTaggedResponseModel,
  MarkAsReviewedRequestModel,
  ReviewsetFacade,
  ReviewSetStateService,
  SearchFacade,
  SearchResponseData,
  SearchResultFacade,
} from '@venio/data-access/review'
import { PageControlActionType } from '@venio/shared/models/constants'
import { combineLatest, filter, Observable, switchMap, take } from 'rxjs'
import {
  AppIdentitiesTypes,
  IframeMessengerService,
  MessageType,
} from '@venio/data-access/iframe-messenger'

@Injectable({ providedIn: 'root' })
export class DocumentTagUtilityService {
  private readonly activatedRoute = inject(ActivatedRoute)

  private get docShareToken(): string {
    return (this.activatedRoute.snapshot.queryParams['docShareToken'] ||
      '') as string
  }

  constructor(
    private documentsFacade: DocumentsFacade,
    private documentTagFacade: DocumentTagFacade,
    private documentCodingFacade: DocumentCodingFacade,
    private searchResultFacade: SearchResultFacade,
    private searchFacade: SearchFacade,
    private reviewSetFacade: ReviewsetFacade,
    private reviewSetState: ReviewSetStateService,
    private iframeMessengerService: IframeMessengerService
  ) {}

  /**
   * Common method to get reviewed file data
   * @param projectId The project ID
   * @returns Observable of SearchResponseData[] for the reviewed files
   */
  private getReviewedFileData(
    projectId: number
  ): Observable<SearchResponseData[]> {
    return this.searchResultFacade.getSearchResultFileIds.pipe(
      take(1),
      switchMap((currentPageFileIds) => {
        return this.reviewSetFacade
          .reviewedFileStatus$<FileTaggedResponseModel>(projectId, {
            reviewSetId: this.reviewSetState.reviewSetId(),
            currentPageFileIds: currentPageFileIds,
            isFileTagged: false,
          })
          .pipe(
            switchMap((response: FileTaggedResponseModel) => {
              // Use the reviewedFileIds from the response
              return this.searchResultFacade.getSearchResultEntityByFileIds(
                response.reviewedFileIds
              )
            })
          )
      }),
      take(1)
    )
  }

  public saveTag(
    projectId: number,
    navigationType: PageControlActionType
  ): void {
    combineLatest([
      this.documentsFacade.selectDocumentPayloadData,
      this.documentTagFacade.selectDocumentTagPayloadData$,
      this.documentCodingFacade.selectDocumentCodingPayloadData$,
      this.searchResultFacade.getSearchResultFileIds,
      this.searchFacade.getSearchTempTables$,
      this.documentTagFacade.selectIsTagDataModified$,
      this.documentCodingFacade.selectIsCodingDataModified$,
    ])
      .pipe(
        filter(
          ([
            documentPayload,
            documentTagPayload,
            documentCodingPayload,
            currentPageFileIds,
            tempTables,
          ]) =>
            Boolean(
              documentPayload &&
                documentTagPayload &&
                documentCodingPayload &&
                currentPageFileIds &&
                currentPageFileIds.length > 0 &&
                tempTables
            )
        ),
        take(1)
      )
      .subscribe(
        ([
          documentPayload,
          documentTagPayload,
          documentCodingPayload,
          currentPageFileIds,
          tempTables,
          isTagDataModified,
          isCodingDataModified,
        ]) => {
          const saveDocumentTagPayload: SaveDocumentTagPayloadModel = {
            projectTags: documentTagPayload.projectTags,
            tagSettings: documentTagPayload.projectTagSettings,
            selectChangedTags: documentTagPayload.changedTags,
          }

          const saveDocumentCodingPayload: SaveDocumentCodingPayloadModel = {
            fieldCoding: documentCodingPayload.fieldCodingModel,
            updatedCodingFieldInfoIds:
              documentCodingPayload.updatedCodingFieldInfoIds,
          }

          const tagPayload: SaveTagPayloadModel = {
            documentPayload: documentPayload,
            documentTagPayload: saveDocumentTagPayload,
            documentCodingTagPayload: saveDocumentCodingPayload,
            currentPageFileIds: currentPageFileIds,
            projectId: projectId,
            navigationType: navigationType,
            searchResultTempTable: tempTables?.searchResultTempTable,
            documentShareToken: this.docShareToken,
          }

          // If tag / coding data is modified, save the data else we will navigate to next / previous document.
          if (isTagDataModified || isCodingDataModified) {
            this.documentTagFacade.saveTag(tagPayload)
            if (
              this.reviewSetState.reviewSetId() > 0 &&
              this.reviewSetState.reviewSetBasicInfo().markTaggedDocsAsReviewed
            ) {
              const reviewPayload: MarkAsReviewedRequestModel = {
                fileIds: documentPayload.selectedDocuments,
                globalTempTable: tempTables?.searchResultTempTable,
                isBatchSelected: documentPayload.isBatchSelected,
                unSelectedFileIds: documentPayload.unselectedDocuments,
              }
              this.handleMarkAsReviewedBulk(projectId, reviewPayload)
            }
            this.#notifyTaggingChangeEventToParentWindow()
          } else if (navigationType === PageControlActionType.NEXT_PAGE) {
            this.documentsFacade.setDocumentNavigation = navigationType
          } else if (navigationType === PageControlActionType.PREV_PAGE)
            this.documentsFacade.setDocumentNavigation = navigationType
        }
      )
  }

  /**
   * When operations like tagging, coding are done, we need to notify the parent window
   * so that the parent window can update the UI states as needed.
   * @see DocumentTableComponent
   * @see refreshDocumentTableFormChildWindow
   * @returns {void}
   */
  #notifyTaggingChangeEventToParentWindow(): void {
    this.iframeMessengerService.sendMessage({
      type: 'MICRO_APP_DATA_CHANGE',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      eventTriggeredFor: 'ALL_WINDOW',
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      payload: {
        type: MessageType.UI_STATE_CHANGE,
        content: {
          isTaggingUpdated: true,
        },
      },
    })
  }

  private handleMarkAsReviewedBulk(
    projectId: number,
    reviewPayload: MarkAsReviewedRequestModel
  ): void {
    this.reviewSetFacade
      .markAsReviewedBulk$(
        projectId,
        this.reviewSetState.reviewSetId(),
        this.reviewSetState.batchId(),
        reviewPayload
      )
      .pipe(switchMap(() => this.getReviewedFileData(projectId)))
      .subscribe((selectedFileData) => {
        this.updateReviewedFilesInUI(selectedFileData)
        this.reviewSetFacade.checkBatchReviewCompletedAction$.next(
          this.reviewSetState.batchId()
        )
      })
  }

  public updateIsReviewedFlag(): void {
    this.searchResultFacade.updateIsReviewedFlag$
      .pipe(
        switchMap((projectId: number) => this.getReviewedFileData(projectId)),
        take(1)
      )
      .subscribe((selectedFileData: SearchResponseData[]) => {
        this.updateReviewedFilesInUI(selectedFileData)
      })
  }

  /**
   * Common method to update UI with reviewed files
   * @param selectedFileData The file data to update
   */
  private updateReviewedFilesInUI(
    selectedFileData: SearchResponseData[]
  ): void {
    const updatedRows = selectedFileData.map((row) => ({
      ...row,
      metadata: row.metadata.map((item) => {
        if (item.key === '__isReviewed') {
          return { ...item, value: 'Yes' }
        }
        return item
      }),
    }))
    this.searchResultFacade.updateManySearchResult(updatedRows)
  }
}
