import {
  ChangeDetectionStrategy,
  Component,
  inject,
  input,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
  viewChild,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  DialogsModule,
  DialogRef,
  DialogService,
} from '@progress/kendo-angular-dialog'
import { IconsModule } from '@progress/kendo-angular-icons'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { filter, Subject, takeUntil, tap } from 'rxjs'
import { UserFacade } from '@venio/data-access/common'
import { NotificationService } from '@progress/kendo-angular-notification'
import { ControlSettingService } from '@venio/data-access/control-settings'
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  Validators,
} from '@angular/forms'
import { toSignal } from '@angular/core/rxjs-interop'
import {
  IndicatorsModule,
  LoaderModule,
  SkeletonModule,
} from '@progress/kendo-angular-indicators'
import {
  InviteUploadFormModel,
  InviteUploadForm,
  InviteUserModel,
} from '@venio/shared/models/interfaces'
import { UserMessageModel } from '@venio/data-access/review'
import { InviteUploadFormComponent } from '../invite-upload-form/invite-upload-form.component'
import { InviteUploadLocalState } from './invite-upload-local-state'
import { EmailUtil } from '@venio/util/utilities'
import { NotificationDialogComponent } from '@venio/feature/notification'

@Component({
  selector: 'venio-invite-upload-container',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    IconsModule,
    ButtonsModule,
    LayoutModule,
    LoaderModule,
    IndicatorsModule,
    ReactiveFormsModule,
    SkeletonModule,
    InviteUploadFormComponent,
  ],
  providers: [InviteUploadLocalState],
  templateUrl: './invite-upload-container.component.html',
  styleUrl: './invite-upload-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InviteUploadContainerComponent implements OnInit, OnDestroy {
  public readonly appendTo = viewChild('appendNotification', {
    read: ViewContainerRef,
  })

  public readonly selectedProjectId = input.required<number>()

  public readonly tabsViewContainerRef = input.required<ViewContainerRef>()

  private readonly userFacade = inject(UserFacade)

  private readonly inviteUploadLocalState = inject(InviteUploadLocalState)

  private readonly dialogRef = inject(DialogRef)

  private readonly dialogService = inject(DialogService)

  private readonly notificationService = inject(NotificationService)

  private readonly controlSettingService = inject(ControlSettingService)

  private readonly formBuilder = inject(FormBuilder)

  public readonly invitationInProgress = toSignal(
    this.userFacade.selectIsInvitationInProgress$,
    {
      initialValue: false,
    }
  )

  public inviteUploadForm: FormGroup<InviteUploadForm> = null

  private initialFormValue: InviteUploadFormModel

  public readonly allowExternalUserUpload = signal(false)

  public readonly shareToExternalUsers =
    this.inviteUploadLocalState.shareToExternalUsers

  public readonly internalUsersList =
    this.inviteUploadLocalState.internalUsersList

  private readonly selectedExternalUsers =
    this.inviteUploadLocalState.selectedExternalUsers

  private readonly hasNoUsersSelected =
    this.inviteUploadLocalState.hasNoUsersSelected

  public readonly userMessage = this.inviteUploadLocalState.userMessage

  // Default and Minimum validity period (in days) for the invitation
  public readonly defaultValidity = 1

  public readonly currentUser = toSignal(
    this.userFacade.selectCurrentUserDetails$
  )

  private toDestroy$: Subject<void> = new Subject<void>()

  public ngOnInit(): void {
    this.#initializeForm()
    this.#selectSendInvitationResponse()
    this.#handleErrorResponses()
  }

  #initializeForm(): void {
    this.allowExternalUserUpload.set(
      this.controlSettingService.getControlSetting
        .ALLOW_EXTERNAL_USER_DATA_UPLOAD
    )

    this.inviteUploadForm = this.formBuilder.group({
      shareToExternalUsers: {
        value: false,
        disabled: !this.allowExternalUserUpload(),
      },
      instruction: '',
      newEmail: [
        {
          value: '',
          disabled: !this.shareToExternalUsers(),
        },
        [
          this.#validateUniqueEmail.bind(this),
          this.#validateEmailFormat.bind(this),
        ],
      ],
      validity: [this.defaultValidity, Validators.min(1)],
    })

    this.initialFormValue =
      this.inviteUploadForm.getRawValue() as InviteUploadFormModel
  }

  #handleErrorResponses(): void {
    this.userFacade.selectUserListToInviteErrorResponse$
      .pipe(
        filter((error) => Boolean(error)),
        tap(() => {
          this.userFacade.clearInternalUserFetchError()
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        this.#showErrorMessage(
          error?.message || 'Failed to fetch internal users.'
        )
      })

    this.userFacade.selectExternalUserToInviteErrorResponse$
      .pipe(
        filter((error) => Boolean(error)),
        tap(() => {
          this.userFacade.clearExternalUserFetchError()
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        this.#showErrorMessage(
          error?.message || 'Failed to fetch external users.'
        )
      })
  }

  #selectSendInvitationResponse(): void {
    this.userFacade.selectSendInvitationResponseMessage$
      .pipe(
        filter((response) => Boolean(response)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: UserMessageModel) => {
        const { message, success } = response
        if (success) this.#showNotificationMessage(message)
        else this.#showErrorMessage(message)
      })
  }

  #prepareInvitationPayload(): InviteUserModel {
    const instruction = this.inviteUploadForm.get('instruction')?.value
    const inviteToUpload: InviteUserModel = {
      senderUserId: this.currentUser().userId,
      recipientUserIds: [...this.internalUsersList().keys()],
      uploadExpirationPeriod: this.inviteUploadForm.get('validity')?.value,
      projectId: this.selectedProjectId(),
      instruction: `<pre> ${instruction} </pre>`,
      invitedExtUserInfo: this.selectedExternalUsers(),
      invitedIntUserInfo: [...this.internalUsersList().values()],
    }
    return inviteToUpload
  }

  #validateUniqueEmail(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null

    const existingEmails = this.inviteUploadLocalState
      .externalUsers()
      .map((user) => user.email)

    return existingEmails.includes(control.value) ? { emailExists: true } : null
  }

  #validateEmailFormat(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null

    return !EmailUtil.validateEmail(control.value)
      ? { invalidEmail: true }
      : null
  }

  #validateData(): boolean {
    const isValidityValid = this.inviteUploadForm.get('validity')?.valid
    this.inviteUploadLocalState.updatedUserSelectedStatus()

    return !this.hasNoUsersSelected() && isValidityValid
  }

  public sendInvite(): void {
    this.inviteUploadLocalState.setUserMessage('')
    this.inviteUploadForm.markAllAsTouched()
    if (this.#validateData()) {
      const payload = this.#prepareInvitationPayload()
      this.userFacade.sendInvitation(payload)
    }
  }

  public close(): void {
    this.dialogRef.close()
  }

  /**
   * This function shows a notification message.
   * @param {string} content The content of the notification message.
   * @returns {void}
   */
  #showNotificationMessage(content = ''): void {
    const notificationDialogRef = this.dialogService.open({
      content: NotificationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-save',
      width: '25rem',
      appendTo: this.tabsViewContainerRef(),
    })

    // Set the dialog input
    this.#setDialogInput(notificationDialogRef.content.instance, content)
    this.close()
  }

  /**
   * This function sets the input for the dialog.
   * @param instance The instance of the NotificationDialogComponent.
   * @param content The content of the notification message.
   */

  #setDialogInput(instance: NotificationDialogComponent, content = ''): void {
    // Set the message of the dialog
    instance.title = 'Success'
    instance.message = content
  }

  #resetInviteFormWithDefaultValues(): void {
    this.inviteUploadForm.reset(this.initialFormValue)
    this.userFacade.resetUserState([
      'invitationInProgress',
      'userListToInviteSuccessResponse',
      'sendInvitationResponseMessage',
      'externalUserToInviteSuccessResponse',
    ])
  }

  public ngOnDestroy(): void {
    this.#resetInviteFormWithDefaultValues()
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  #showErrorMessage(content: string): void {
    this.notificationService.show({
      content,
      type: { style: 'error' },
      animation: { type: 'fade', duration: 300 },
      hideAfter: 3500,
      width: 300,
    })
  }
}
