<!-- Tab header controls for browse panel -->
<div
  class="t-w-full t-flex-shrink-0 t-px-2 t-py-1 t-border-b t-border-opacity-10 t-border-b-primary">
  @defer{
  <venio-browser-panel-action-controls />
  }
</div>

<!-- Browse panel content area -->
<div class="t-flex-1 t-overflow-auto">
  @defer {
  <!--rest of the components-->
  <venio-browse-panel-system-folder-tab
    *ngIf="selectedFolderType() === allFolderTypes.SYSTEM" />
  <venio-browse-panel-auto-folder
    *ngIf="selectedFolderType() === allFolderTypes.AUTO" />
  <venio-browse-panel-custom-folder
    *ngIf="selectedFolderType() === allFolderTypes.CUSTOM" />
  }
  <!--maybe more components or children-->
</div>
