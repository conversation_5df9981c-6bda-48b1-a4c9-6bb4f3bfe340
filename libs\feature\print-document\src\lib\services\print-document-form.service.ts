import { DatePipe } from '@angular/common'
import { Inject, Injectable, LOCALE_ID } from '@angular/core'
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms'
import {
  BulkPrintOptions,
  DocumentSelectionType,
} from '@venio/data-access/review'
import { FontSetting } from '../models/print-summary.models'

/**
 * <PERSON>les populating print document form, validating it, manipulating it, and getting user selected options.
 * This service also acts a bridge between the production form components
 */
@Injectable({ providedIn: 'root' })
export class PrintDocumentFormService {
  public printForm!: FormGroup

  public fontSelectionForm: FormGroup

  private dateStamp: Date = new Date()

  private datePipe: DatePipe

  private initialValue: BulkPrintOptions

  private readonly initialFontSetting: FontSetting = {
    fontName: 'Tahoma',
    fontSize: 12,
    fontStyle: 'Regular',
  }

  public fontSetting: FontSetting

  constructor(private fb: FormBuilder, @Inject(LOCALE_ID) locale: string) {
    this.datePipe = new DatePipe(locale)
    this.fontSetting = this.initialFontSetting
    this.initializeForm()
    this.initilaizeFontSelectionform()
  }

  private initilaizeFontSelectionform(): void {
    this.initForm()
  }

  private initForm(): void {
    this.fontSelectionForm = this.fb.group({
      fontName: [this.fontSetting?.fontName || ''],
      fontStyle: [this.fontSetting?.fontStyle || ''],
      fontSize: [this.fontSetting?.fontSize || ''],
    })
  }

  /**
   * Returns the print form
   * @returns {void}
   */
  public resetForm(): void {
    this.dateStamp = new Date()
    this.printForm.reset(this.initialValue)
    this.printForm
      .get('printName')
      .setValue(
        'PrintJob_' + this.datePipe.transform(this.dateStamp, 'yyyyMMddHHmmss')
      )
    this.initilaizeFontSelectionform()
    this.fontSetting = this.initialFontSetting
  }

  /**
   * Initializes the print form
   * @returns {void}
   */
  private initializeForm(): void {
    this.printForm = this.fb.group({
      printName: [
        'PrintJob_' + this.datePipe.transform(this.dateStamp, 'yyyyMMddHHmmss'),
        [Validators.required, PrintDocumentFormService.fileNameValidator],
      ],
      SelectionType: [DocumentSelectionType.PRINT_ALL_SELECTED_DOCUMENTS],
      tiffSourceOption: this.fb.group({
        printOriginal: ['PRINT_ORIGINAL'],
        printOption: ['PRINT_ORIGINAL_TIFF_WITH_REDACTION'],
        exportOrder: [''],
        endorsementLocation: ['TOP_RIGHT'],
        endorsementValue: ['FILE_ID_WITH_PAGE_NUMBER'],
        isConfidentialStamping: [false],
        confidentialStampingLocation: ['TOP_LEFT'],
        confidentialStampingText: [{ value: '', disabled: false }],
        queueFilesForImagingIfNotAvailable: [true],
      }),
      slipSheetOption: this.fb.group({
        slipsheetSettings: ['ENABLE_SLIPSHEET'],
        slipSheetText: ['Image for this document is not available'],
        placeHolderPosition: ['Center'],
        placeHolderTextFont: ['Tahoma, 12pt'],
      }),
      waterMarkOption: this.fb.group({
        addWaterMark: [false],
        text: [{ value: '', disabled: false }],
        orientation: [{ value: 'CENTER', disabled: false }],
        fontText: [{ value: 'Calibri', disabled: false }],
        fontSize: [{ value: '24', disabled: false }],
        fontStyle: [{ value: 'Regular', disabled: false }],
        color: [{ value: '#000000', disabled: false }],
        font: ['Calibri, 24pt'],
      }),
      outputOption: this.fb.group({
        outputOptionButton: ['true'],
        fieldValue: [{ value: 'INTERNAL_FILE_ID', disabled: false }],
        outputSettings: [
          { value: 'PRINT_COMBINED_PDF_WITH_BOOKMARK', disabled: true },
        ],
      }),
      imageResizeOption: this.fb.group({
        resizeImage: [{ value: false, disabled: false }],
        paperSize: [{ value: 0, disabled: true }],
        dimension: [{ value: 'BOTH', disabled: true }],
        width: [{ value: 8.5, disabled: true }],
        height: [{ value: 11, disabled: true }],
        sizeUnit: [{ value: 'INCH', disabled: true }],
        maintainAspectRatio: [{ value: false, disabled: true }],
      }),
    })

    this.initialValue = this.printForm.getRawValue() as BulkPrintOptions
  }

  public getPrintSettings(): BulkPrintOptions {
    const printSettings = this.printForm.getRawValue()
    printSettings.slipSheetOption.placeHolderTextFont =
      this.getFontStyleString()
    printSettings.waterMarkOption.font =
      printSettings.waterMarkOption.fontText +
      ', ' +
      printSettings.waterMarkOption.fontSize +
      'pt' +
      (printSettings.waterMarkOption.fontStyle === 'Regular'
        ? ''
        : `, style=${printSettings.waterMarkOption.fontStyle}`)

    if (printSettings.outputOption.outputOptionButton === 'true')
      printSettings.outputOption.outputSettings = 'PRINT_INDIVIDUAL_PDF'

    return printSettings as BulkPrintOptions
  }

  public getFontStyleString(): string {
    const fontString =
      this.fontSelectionForm.get('fontName').value +
      ', ' +
      this.fontSelectionForm.get('fontSize').value +
      'pt' +
      (this.fontSelectionForm.get('fontStyle').value === 'Regular'
        ? ''
        : `, style=${this.fontSelectionForm.get('fontStyle').value}`)
    return fontString
  }

  public static fileNameValidator(
    control: AbstractControl
  ): ValidationErrors | null {
    const forbiddenChars = /[\\\/:\*\?"<>\|]/g
    if (control.value && forbiddenChars.test(control.value)) {
      return { invalidFileName: true }
    }
    return null
  }
}
