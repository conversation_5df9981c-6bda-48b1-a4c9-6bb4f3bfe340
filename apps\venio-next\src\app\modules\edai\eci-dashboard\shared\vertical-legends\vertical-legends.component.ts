import {
  Component,
  inject,
  Input,
  computed,
  signal,
  input,
  output,
} from '@angular/core'
import { otherChartColors } from '../../constants/colors'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>For } from '@angular/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { AiFacade, SunburstChartType } from '@venio/data-access/ai'
import { formatChildData } from '../data-table-for-focused-section/helpers'

@Component({
  selector: 'venio-vertical-legends',
  standalone: true,
  imports: [<PERSON><PERSON>ty<PERSON>, NgFor],
  templateUrl: './vertical-legends.component.html',
})
export class VerticalLegendsComponent {
  @Input()
  public set chartType(value: string) {
    this.chartTypeSignal.set(value)
  }

  public get chartType(): string {
    return this.chartTypeSignal()
  }

  private readonly chartTypeSignal = signal<string>(
    SunburstChartType.DocumentTypes
  )

  // Colors input from parent component
  public readonly colors = input<string[]>(otherChartColors)

  // Input to determine if we're in focused mode
  public readonly isFocusedMode = input<boolean>(false)

  // Output event for legend clicks in normal mode (local expansion)
  public readonly legendClick = output<{
    legendName: string
    selectedItem: any
  }>()

  private readonly aiFacade = inject(AiFacade)

  // Data signals
  public readonly documentTypesData = toSignal(
    this.aiFacade.selectEcaDocumentTypesSuccess$,
    { initialValue: null }
  )

  public readonly relevantTopicsData = toSignal(
    this.aiFacade.selectEcaTopicsRelevantSuccess$,
    { initialValue: null }
  )

  public readonly nonRelevantTopicsData = toSignal(
    this.aiFacade.selectEcaTopicsNonRelevantSuccess$,
    { initialValue: null }
  )

  public readonly relevanceData = toSignal(
    this.aiFacade.selectEcaRelevanceSuccess$,
    { initialValue: null }
  )

  // Computed legends based on chart type
  public readonly legends = computed(() => {
    try {
      const chartType = this.chartTypeSignal()
      switch (chartType) {
        case SunburstChartType.DocumentTypes: {
          const docTypesData = this.documentTypesData()
          if (
            docTypesData?.data?.documentTypes &&
            Array.isArray(docTypesData.data.documentTypes)
          ) {
            return docTypesData.data.documentTypes
              .map((docType: any) => docType?.docTypeName)
              .filter((name: string) => name && typeof name === 'string')
          }
          break
        }
        case SunburstChartType.RelevantDocuments: {
          const relevantData = this.relevantTopicsData()
          if (
            relevantData?.data?.topics &&
            Array.isArray(relevantData.data.topics)
          ) {
            return relevantData.data.topics
              .map((topic: any) => topic?.topicName)
              .filter((name: string) => name && typeof name === 'string')
          }
          break
        }
        case SunburstChartType.NotRelevantDocuments: {
          const nonRelevantData = this.nonRelevantTopicsData()
          if (
            nonRelevantData?.data?.topics &&
            Array.isArray(nonRelevantData.data.topics)
          ) {
            return nonRelevantData.data.topics
              .map((topic: any) => topic?.topicName)
              .filter((name: string) => name && typeof name === 'string')
          }
          break
        }
        case SunburstChartType.Relevance: {
          // For relevance chart, show relevance types as legends
          const relevanceData = this.relevanceData()
          if (relevanceData?.data && Array.isArray(relevanceData.data)) {
            return relevanceData.data
              .map((relevanceItem: any) => relevanceItem?.relevanceType)
              .filter((name: string) => name && typeof name === 'string')
          }
          break
        }
      }
      return []
    } catch (error) {
      console.error('Error processing legends data:', error)
      return []
    }
  })

  // Use colors from input, fallback to default other chart colors
  public readonly chartColors = computed(() => this.colors())

  // Get chart data for drill-down functionality
  public readonly chartData = computed(() => {
    try {
      const chartType = this.chartTypeSignal()
      switch (chartType) {
        case SunburstChartType.DocumentTypes: {
          const docTypesData = this.documentTypesData()
          if (
            docTypesData?.data?.documentTypes &&
            Array.isArray(docTypesData.data.documentTypes)
          ) {
            return this.#transformEcaDocumentTypes(
              docTypesData.data.documentTypes
            )
          }
          break
        }
        case SunburstChartType.RelevantDocuments: {
          const relevantData = this.relevantTopicsData()
          if (
            relevantData?.data?.topics &&
            Array.isArray(relevantData.data.topics)
          ) {
            return this.#transformEcaTopicsToDocumentTypes(
              relevantData.data.topics
            )
          }
          break
        }
        case SunburstChartType.NotRelevantDocuments: {
          const nonRelevantData = this.nonRelevantTopicsData()
          if (
            nonRelevantData?.data?.topics &&
            Array.isArray(nonRelevantData.data.topics)
          ) {
            return this.#transformEcaTopicsToDocumentTypes(
              nonRelevantData.data.topics
            )
          }
          break
        }
        case SunburstChartType.Relevance: {
          // For relevance chart, use actual relevance data
          const relevanceData = this.relevanceData()
          if (relevanceData?.data && Array.isArray(relevanceData.data)) {
            return this.#transformEcaRelevanceData(relevanceData.data)
          }
          break
        }
      }
      return []
    } catch (error) {
      console.error('Error processing chart data for legends:', error)
      return []
    }
  })

  public onLegendClick(legendName: string, index: number): void {
    try {
      const data = this.chartData()
      if (!data || !Array.isArray(data) || data.length === 0) {
        console.warn('No chart data available for legend interaction')
        return
      }

      // Find the data item that matches the legend
      const selectedItem = data.find(
        (item: any) =>
          item?.category === legendName ||
          item?.name === legendName ||
          item?.docTypeName === legendName ||
          item?.topicName === legendName
      )

      if (!selectedItem) {
        console.warn('Legend data not found for:', legendName)
        return
      }

      console.log(`Legend clicked: ${legendName}`, selectedItem)

      // Handle differently based on mode
      if (this.isFocusedMode()) {
        // Focused mode: Use global facade drill-down
        this.#handleFocusedModeLegendClick(selectedItem, legendName)
      } else {
        // Normal mode: Emit event for local expansion
        this.legendClick.emit({ legendName, selectedItem })
      }
    } catch (error) {
      console.error('Error handling legend click:', error)
    }
  }

  #handleFocusedModeLegendClick(selectedItem: any, legendName: string): void {
    // Check if we have subcategories to drill down to
    if (selectedItem.subcategories && selectedItem.subcategories.length > 0) {
      // Drill down to next level
      const chartType = this.chartTypeSignal() as SunburstChartType
      this.aiFacade.drillDownToNextLevel(
        chartType,
        selectedItem,
        selectedItem.subcategories
      )

      // Update table data
      const formatted = formatChildData(selectedItem)
      if (formatted && Array.isArray(formatted)) {
        this.aiFacade.updateChartTableData(chartType, formatted)
      }
    } else {
      // No more drill levels - highlight the selected node
      const chartType = this.chartTypeSignal() as SunburstChartType
      this.aiFacade.setChartSelectedNode(chartType, selectedItem)
      console.log(`Highlighting node: ${legendName}`)
    }
  }

  #transformEcaDocumentTypes(ecaDocumentTypes: any[]): any[] {
    try {
      if (!ecaDocumentTypes || !Array.isArray(ecaDocumentTypes)) {
        console.warn('Invalid ECA document types data for transformation')
        return []
      }

      return ecaDocumentTypes
        .map((docType) => {
          if (!docType) return null

          return {
            id: docType.docTypeId || 0,
            name: docType.docTypeName || 'Unknown',
            count: docType.docCount || 0,
            percentage: docType.percentage || 0,
            category: docType.docTypeName || 'Unknown',
            subcategories: Array.isArray(docType.children)
              ? docType.children.map((child: any) => ({
                  subcategory: child?.docTypeName || 'Unknown',
                  count: child?.docCount || 0,
                  percentage: child?.percentage || 0,
                }))
              : [],
          }
        })
        .filter((item) => item !== null)
    } catch (error) {
      console.error('Error transforming ECA document types:', error)
      return []
    }
  }

  #transformEcaTopicsToDocumentTypes(ecaTopics: any[]): any[] {
    try {
      if (!ecaTopics || !Array.isArray(ecaTopics)) {
        console.warn('Invalid ECA topics data for transformation')
        return []
      }

      return ecaTopics
        .map((topic) => {
          if (!topic) return null

          return {
            id: topic.topicId || 0,
            name: topic.topicName || 'Unknown',
            count: topic.docCount || 0,
            percentage: topic.percentage || 0,
            category: topic.topicName || 'Unknown',
            subcategories: Array.isArray(topic.children)
              ? topic.children.map((child: any) => ({
                  subcategory: child?.topicName || 'Unknown',
                  count: child?.docCount || 0,
                  percentage: child?.percentage || 0,
                }))
              : [],
          }
        })
        .filter((item) => item !== null)
    } catch (error) {
      console.error('Error transforming ECA topics:', error)
      return []
    }
  }

  #transformEcaRelevanceData(ecaRelevanceData: any[]): any[] {
    try {
      if (!ecaRelevanceData || !Array.isArray(ecaRelevanceData)) {
        console.warn('Invalid ECA relevance data for transformation')
        return []
      }

      // Calculate total for percentage calculation
      const total = ecaRelevanceData.reduce(
        (sum, item) => sum + (item?.docCount || 0),
        0
      )

      return ecaRelevanceData
        .map((relevanceItem, index) => {
          if (!relevanceItem) return null

          const count = relevanceItem.docCount || 0
          const percentage = total > 0 ? (count / total) * 100 : 0

          return {
            id: index + 1, // Use index + 1 as ID like other charts
            category: relevanceItem.relevanceType || 'Unknown',
            name: relevanceItem.relevanceType || 'Unknown',
            count: count,
            percentage: percentage,
            // For relevance, no subcategories - it's a single layer structure
            subcategories: [],
          }
        })
        .filter((item) => item !== null && item.count > 0)
    } catch (error) {
      console.error('Error transforming ECA relevance data:', error)
      return []
    }
  }
}
