import {
  AfterViewInit,
  Component,
  computed,
  inject,
  Injector,
  input,
  On<PERSON><PERSON>roy,
  OnInit,
  output,
  signal,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DialogService,
  WindowCloseActionDirective,
  WindowComponent,
  WindowTitleBarComponent,
} from '@progress/kendo-angular-dialog'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import { ReviewsetFormToolbarComponent } from '../reviewset-form-toolbar/reviewset-form-toolbar.component'
import { ReviewsetFormService } from '../reviewset-form.service'
import {
  ExpansionPanelComponent,
  ExpansionPanelTitleDirective,
} from '@progress/kendo-angular-layout'
import {
  chevronDownIcon,
  chevronUpIcon,
  exclamationCircleIcon,
} from '@progress/kendo-svg-icons'
import { ReviewsetFormGeneralSettingsComponent } from '../reviewset-form-general-settings/reviewset-form-general-settings.component'
import { ReviewsetFormSourceComponent } from '../reviewset-form-source/reviewset-form-source.component'
import { ReviewsetFormReviewersComponent } from '../reviewset-form-reviewers/reviewset-form-reviewers.component'
import { ReviewsetFormDocumentSortOptionsComponent } from '../reviewset-form-document-sort-options/reviewset-form-document-sort-options.component'
import { ReviewsetFormAdvancedOptionsComponent } from '../reviewset-form-advanced-options/reviewset-form-advanced-options.component'
import { ReviewSetPayloadService } from '../reviewset-payload.service'
import {
  ResponseModel,
  ReviewSetDialogData,
  ReviewSetDialogType,
  SelectedReviewSetActionEvent,
} from '@venio/shared/models/interfaces'
import {
  distinctUntilChanged,
  filter,
  of,
  Subject,
  take,
  takeUntil,
} from 'rxjs'
import { isEqual } from 'lodash'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import { ProjectFacade } from '@venio/data-access/common'
import { LoaderComponent } from '@progress/kendo-angular-indicators'
import { DebounceTimer } from '@venio/util/utilities'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { ConfirmationDialogComponent } from '@venio/feature/notification'
import { catchError } from 'rxjs/operators'
import { HttpErrorResponse } from '@angular/common/http'

enum PanelTitle {
  GeneralSetting = 'General Setting',
  Source = 'Source',
  Reviewers = 'Reviewers',
  DocumentSortOptions = 'Document Sort Options',
  AdvancedOptions = 'Advanced Options',
}
interface PanelItem {
  title: PanelTitle
  expanded: boolean
}

@Component({
  selector: 'venio-reviewset-form-dialog-container',
  standalone: true,
  imports: [
    CommonModule,
    WindowComponent,
    ButtonComponent,
    WindowCloseActionDirective,
    WindowTitleBarComponent,
    ReviewsetFormToolbarComponent,
    ExpansionPanelComponent,
    ReviewsetFormGeneralSettingsComponent,
    ReviewsetFormSourceComponent,
    ReviewsetFormReviewersComponent,
    ReviewsetFormDocumentSortOptionsComponent,
    ReviewsetFormAdvancedOptionsComponent,
    LoaderComponent,
    ExpansionPanelTitleDirective,
  ],
  templateUrl: './reviewset-form-dialog-container.component.html',
  styleUrl: './reviewset-form-dialog-container.component.scss',
  providers: [ReviewsetFormService, ReviewSetPayloadService],
})
export class ReviewsetFormDialogContainerComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  public readonly selectedReviewSetActionEvent =
    input<SelectedReviewSetActionEvent>()

  public readonly dialogCLose = output()

  private readonly toDestroy$ = new Subject<void>()

  public readonly projectFacade = inject(ProjectFacade)

  public readonly injector = inject(Injector)

  public readonly reviewSetFormService = inject(ReviewsetFormService)

  private readonly dialogService = inject(DialogService)

  private readonly viewContainerRef = inject(ViewContainerRef)

  public readonly reviewSetPayloadBuilder = inject(ReviewSetPayloadService)

  private readonly notificationService = inject(NotificationService)

  public readonly icons = {
    downIcon: chevronDownIcon,
    upIcon: chevronUpIcon,
    errorIcon: exclamationCircleIcon,
  }

  public readonly panelTitle = PanelTitle

  public expansionPanelItems: PanelItem[] = [
    {
      title: PanelTitle.GeneralSetting,
      expanded: true,
    },
    {
      title: PanelTitle.Source,
      expanded: false,
    },
    {
      title: PanelTitle.Reviewers,
      expanded: false,
    },
    {
      title: PanelTitle.DocumentSortOptions,
      expanded: false,
    },
    {
      title: PanelTitle.AdvancedOptions,
      expanded: false,
    },
  ]

  public readonly dirtyCheckFormValidationMessage = signal(false)

  public readonly isReviewSetSaving = signal(false)

  /**
   * Computes the appropriate title for the review set form based on the action type.
   *
   * Returns:
   * - "Edit" when editing an existing review set
   * - "Clone" when cloning an existing review set
   * - "Create" when creating a new review set
   *
   * @returns {string} - The form title
   */
  public readonly reviewSetFormTitle = computed(() => {
    const { selectedReviewSet, actionType } =
      this.selectedReviewSetActionEvent()
    return selectedReviewSet?.reviewSetId > 0 &&
      actionType === CommonActionTypes.REVIEW_SET_EDIT
      ? 'Edit'
      : actionType === CommonActionTypes.REVIEW_SET_CLONE
      ? 'Clone'
      : 'Create'
  })

  /**
   * Manages the expansion state of panels in the form.
   *
   * Implements an accordion-like behavior where only one panel can be expanded at a time.
   * When expanding a panel, collapses all others. When collapsing a panel, only affects
   * the specified panel.
   *
   * @param {boolean} isExpanding - Whether the panel is being expanded (true) or collapsed (false)
   * @param {PanelItem} currentPanel - The panel being toggled
   * @returns {void}
   */
  public panelExpansionState(
    isExpanding: boolean,
    currentPanel: PanelItem
  ): void {
    this.expansionPanelItems.forEach((panel) => {
      if (isExpanding) {
        panel.expanded = panel === currentPanel
      } else {
        if (panel === currentPanel) {
          panel.expanded = false
        }
      }
    })
  }

  public closeDialog(): void {
    this.dialogCLose.emit()
  }

  public ngOnInit(): void {
    this.reviewSetFormService.initReviewSetForm()

    this.#clearMessageWhenFormChangeEvent()

    this.reviewSetFormService.loadLayoutDetails()
    this.reviewSetFormService.reloadWhenCaseChanges()
    this.reviewSetFormService.reloadWhenSourceChanges()
    this.reviewSetFormService.autoCollectControlEnableToggle()
    this.reviewSetFormService.reviewSetTemplateControlValidation()
    this.reviewSetFormService.validateDocumentSortControlWhenCalChange()
    this.reviewSetFormService.reloadSamplingCountWhenSourceChanges()
    this.reviewSetFormService.reviewSetLayoutControlValidation()
  }

  public ngAfterViewInit(): void {
    this.initializeSelectedReviewSet()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  /**
   * Initiates the review set submission process with validation.
   *
   * Process:
   * 1. Sets saving state and clears previous validation messages
   * 2. Marks all form controls as touched and dirty to trigger validation display
   * 3. Updates form validity state
   * 4. Handles pending async validations by waiting for their completion
   * 5. Proceeds to save once validation state is resolved
   *
   * If async validators are pending, waits for them to complete before saving.
   * Otherwise, proceeds directly to saveValidChanges.
   * @returns {void}
   */
  public submitReviewSet(): void {
    this.isReviewSetSaving.set(true)
    this.dirtyCheckFormValidationMessage.set(false)

    // Mark all the controls as touched and dirty to trigger messages in the UI.
    Object.keys(this.reviewSetFormService.reviewSetForm.controls).forEach(
      (key) => {
        const control = this.reviewSetFormService.reviewSetForm.controls[key]
        control.markAsTouched()
        control.markAsDirty()
        control.updateValueAndValidity()
      }
    )

    // Mark form group as touched and dirty to trigger changes and status updates
    this.reviewSetFormService.reviewSetForm.markAllAsTouched()
    this.reviewSetFormService.reviewSetForm.markAsDirty()
    this.reviewSetFormService.reviewSetForm.updateValueAndValidity()

    // If there are any pending changes, wait for the form to be valid
    // For example, if there is an async validator is in progress, becomes pending state
    // so we need to wait for the form to be valid.
    if (this.reviewSetFormService.reviewSetForm.pending) {
      this.reviewSetFormService.reviewSetForm.statusChanges
        .pipe(
          filter((status) => status !== 'PENDING'),
          take(1),
          takeUntil(this.toDestroy$)
        )
        .subscribe(() => this.saveValidChanges())
    } else {
      this.saveValidChanges()
    }
  }

  /**
   * Completes the review set submission after validation.
   *
   * Process:
   * 1. Verifies form is valid, shows error message and exits if invalid
   * 2. Transforms form values into API payload with additional properties
   * 3. Submits to API and handles error responses
   * 4. Updates saving state when complete
   * 5. Displays appropriate success/error message
   * 6. On success, closes dialog and refreshes review set data
   *
   * @returns {void}
   */
  private saveValidChanges(): void {
    // Ensure the form is valid and has all the required fields filled
    // If any of the required fields are not filled, show the error message
    if (!this.reviewSetFormService.reviewSetForm.valid) {
      this.dirtyCheckFormValidationMessage.set(true)
      this.isReviewSetSaving.set(false)
      return
    }

    this.reviewSetFormService
      .processFormForSubmission()
      .pipe(take(1), takeUntil(this.toDestroy$))
      .subscribe((data) => {
        this.isReviewSetSaving.set(false)
        const { dialogData } = data
        if (!data.shouldProceed) {
          this.#launchAndSetupConfirmationDialog(dialogData)
        } else {
          this.#submitForm()
        }
      })
  }

  #submitForm(): void {
    this.isReviewSetSaving.set(true)
    this.reviewSetFormService
      .submitReviewSet()
      .pipe(
        catchError((ex: unknown) => {
          const error = (ex as HttpErrorResponse).error as ResponseModel
          return of(error)
        }),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response) => {
        this.isReviewSetSaving.set(false)
        const style =
          response?.status?.toLowerCase() === 'success' ? 'success' : 'error'
        const message = response?.message

        this.#showMessage(message, { style })

        if (response?.status?.toLowerCase() === 'success') {
          this.closeDialog()
          this.projectFacade.fetchReviewSetSummaryDetail()
        }
      })
  }

  /**
   * Clears validation messages when form values change.
   *
   * Sets up a subscription to form value changes that clears the validation
   * message banner whenever the form is modified. Uses distinctUntilChanged
   * with deep comparison to prevent unnecessary updates.
   *
   * @returns {void}
   */
  #clearMessageWhenFormChangeEvent(): void {
    this.reviewSetFormService.reviewSetForm.valueChanges
      .pipe(
        distinctUntilChanged((a, b) => isEqual(a, b)),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.dirtyCheckFormValidationMessage.set(false)
      })
  }

  /**
   * Displays a notification message to the user.
   *
   * Shows a notification with the provided content and styling options.
   * The notification auto-hides after 10 seconds and can be dismissed by clicking.
   * Does nothing if the content is empty.
   *
   * @param {string} content - The message to display
   * @param {Type} type - The notification styling type (success, error, etc.)
   * @returns {void}
   */
  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  /**
   * Initializes form with selected review set data.
   *
   * When a review set is selected for edit or clone, loads the complete
   * review set data using the event payload information.
   *
   * Uses debounce to prevent excessive processing when selection changes rapidly
   * allowing angular change detection to complete before loading the form.
   * Exit early if no review set is selected or project ID is invalid.
   *
   * @returns {void}
   */
  @DebounceTimer(0)
  private initializeSelectedReviewSet(): void {
    const eventPayload = this.selectedReviewSetActionEvent()

    if (!(eventPayload?.selectedReviewSet?.projectId > 0)) return

    this.reviewSetFormService.loadReviewSet(eventPayload)
  }

  #generateCountTableHTML(headers: string[], counts: string[]): string {
    if (!counts || counts.length === 0 || headers.length !== counts.length) {
      return '' // Return empty string if counts are not provided or headers and counts length mismatch
    }

    const headerHTML = headers
      .map((header) => `<th class=" t-p-2 t-text-left">${header}</th>`)
      .join('')
    const dataHTML = counts
      .map((count) => `<td class=" t-p-2">${count}</td>`)
      .join('')

    return `<div class="t-overflow-x-auto t-border t-border-[#f5f5f5] t-rounded t-mb-2">
      <table class="t-table-auto t-w-full">
        <thead>
          <tr class="t-bg-[#FBFBFB] t-text-info">
            ${headerHTML}
          </tr>
        </thead>
        <tbody>
          <tr>
            ${dataHTML}
          </tr>
        </tbody>
      </table>
    </div>`
  }

  #setDialogInput(
    instance: ConfirmationDialogComponent,
    data: ReviewSetDialogData
  ): void {
    instance.title = data?.title

    const tableConfigurations = [
      {
        condition: data?.counts && data.isNativeToHtmlChecked,
        headers: ['HTML', 'No HTML'],
        counts: [
          String(data?.counts?.htmlConversionCount),
          String(data?.counts?.htmlRemainingCount),
        ],
      },
      {
        condition: data?.counts && data.isNativeToImageChecked,
        headers: ['Imaged', 'Not Imaged'],
        counts: [
          String(data?.counts?.tiffConversionCount),
          String(data?.counts?.tiffRemainingCount),
        ],
      },
    ]

    const tableHTML = tableConfigurations
      .filter((c) => c.condition)
      .reduce((html, config) => {
        return (
          html + this.#generateCountTableHTML(config.headers, config.counts)
        )
      }, '')
    instance.message = ` <p class="t-mb-3 t-text-md">${data?.message}</p>
    ${tableHTML}
      `
    // No further process if the feature is not supported.
    instance.shouldShowCheckedButton.set(
      data?.type !== ReviewSetDialogType.NOT_SUPPORTED
    )
    // whether the icon should ve shown or not
    instance.shouldShowTitleIcon.set(!data?.counts)
  }

  #launchAndSetupConfirmationDialog(data: ReviewSetDialogData): void {
    const confirmationDialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-info',
      width: '35rem',
      appendTo: this.viewContainerRef,
    })

    this.#setDialogInput(confirmationDialogRef.content.instance, data)

    confirmationDialogRef.result
      .pipe(
        filter((action) => typeof action === 'boolean' && action),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => this.#submitForm())
  }
}
