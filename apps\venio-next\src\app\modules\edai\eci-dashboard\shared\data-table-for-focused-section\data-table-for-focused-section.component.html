<div class="t-relative t-bg-white t-rounded t-p-4 t-min-h-[485px]">
  <div
    class="t-grid t-grid-cols-12 t-text-[#5A5A5A] t-gap-4 t-pb-3 t-mb-4 t-text-sm t-font-medium t-border-b t-border-b-[#DCDCDC]">
    <div class="t-col-span-7"></div>
    <div class="t-col-span-2 t-text-right">%</div>
    <div class="t-col-span-3 t-text-right">Count</div>
  </div>
  <div class="t-space-y-0">
    <div class="t-space-y-0">
      <div
        *ngFor="let item of tableData(); let i = index; trackBy: trackById"
        [@rowCollapseExpand]
        class="t-grid t-grid-cols-12 t-gap-1 t-items-center t-cursor-pointer t-rounded t-p-2 t-transition-colors hover:t-bg-[#f7f7f7]"
        (click)="onLegendItemClick(item, i)">
        <div class="t-col-span-7 t-flex t-items-center">
          <div
            class="t-h-3 t-w-3 t-rounded-sm t-mr-3"
            [style]="{ background: getColorForIndex(i) }"></div>
          <span class="t-text-sm t-font-medium t-text-[#1ebadc]">{{
            item.label
          }}</span>
        </div>
        <div class="t-col-span-2 t-text-right t-text-[#5A5A5A]">
          <span class="t-text-sm">{{ item.percent }}</span>
        </div>
        <div class="t-col-span-3 t-text-right t-text-[#5A5A5A]">
          <span class="t-text-sm">{{ item.count | number }}</span>
        </div>
      </div>
    </div>
  </div>
</div>
