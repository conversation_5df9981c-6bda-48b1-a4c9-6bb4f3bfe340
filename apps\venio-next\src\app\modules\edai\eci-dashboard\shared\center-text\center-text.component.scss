.wrapper-center {
  // Complex positioning that can't be easily replicated with Tailwind
  transform: translate(-50%, -50%);
  height: 121px;
  width: 117px;
  transition: all 0.3s ease;

  // When chart is expanded (inner ring is smaller)
  &.expanded {
    height: 90px;
    width: 90px;
  }
}

.center-title {
  // Basic styles moved to Tailwind classes in template
  line-height: 1.2;
  transition: all 0.3s ease;

  &.expanded-title {
    font-size: 0.75rem; // t-text-xs equivalent
    font-weight: 600;
  }
}

.view-details-container {
  // Basic styles moved to Tailwind classes in template
  margin-top: 2px;
  transition: all 0.3s ease;

  &.expanded-details {
    margin-top: 1px;
  }
}

.view-details-text {
  // Basic styles moved to Tailwind classes in template
  transition: all 0.3s ease;

  &.expanded-text {
    font-size: 10px;
    font-weight: 500;
  }
}

.view-details-button {
  // Basic styles moved to Tailwind classes in template
  transition: all 0.3s ease;

  &.expanded-button {
    transform: scale(0.8);
  }
}

// Responsive adjustments for smaller screens
@media (max-width: 768px) {
  .wrapper-center {
    height: 80px;
    width: 80px;
    font-size: 0.875rem;

    &.expanded {
      height: 65px;
      width: 65px;
    }
  }

  .center-title.expanded-title {
    font-size: 10px;
  }

  .view-details-text.expanded-text {
    font-size: 9px;
  }
}
