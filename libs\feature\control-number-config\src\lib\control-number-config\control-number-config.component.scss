::ng-deep {
  .control-number-tree .k-grid-header-wrap {
    border: none;
  }
  .control-number-tree .k-grid-header {
    border: none;
    height: 2.25rem;
  }
  .control-number-tree .k-header .k-table-th .k-focus {
    box-shadow: none;
  }
  td:focus,
  td.k-focus,
  .k-table-td:focus,
  .k-table-td.k-focus,
  .k-table-th:focus,
  .k-table-th.k-focus,
  .k-master-row > td:focus,
  .k-grouping-row > td:focus,
  .k-detail-row > td:focus,
  .k-group-footer > td:focus,
  .k-master-row > .k-table-td:focus,
  .k-grouping-row > .k-table-td:focus,
  .k-detail-row > .k-table-td:focus,
  .k-group-footer > .k-table-td:focus,
  .k-grid-pager.k-focus {
    box-shadow: none !important;
  }

  // prevent pointer events on the empty icon
  kendo-treelist kendo-svgicon.k-svg-i-none {
    cursor: default;
  }
}
