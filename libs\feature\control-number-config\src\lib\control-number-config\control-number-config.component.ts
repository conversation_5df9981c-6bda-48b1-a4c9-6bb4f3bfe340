import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  Injector,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { TreeListModule } from '@progress/kendo-angular-treelist'
import { CheckBoxState, InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { TreeModel } from '../../models/control-number-ui.model'
import {
  CheckableSettings,
  CheckedState,
  TreeViewModule,
} from '@progress/kendo-angular-treeview'
import { IconsModule } from '@progress/kendo-angular-icons'
import { DialogModule } from '@progress/kendo-angular-dialog'
import { toSignal } from '@angular/core/rxjs-interop'
import {
  GenerateControlNumberRequestModel,
  CustodianMediaControlNumberConfig,
  GenerateControlNumberModel,
  ControlNumberConfigFacade,
} from '@venio/data-access/review'
import { Subject } from 'rxjs'

@Component({
  selector: 'venio-control-number-config',
  standalone: true,
  imports: [
    CommonModule,
    TreeViewModule,
    TreeListModule,
    InputsModule,
    LabelModule,
    FormsModule,
    IndicatorsModule,
    ButtonModule,
    TooltipsModule,
    SvgLoaderDirective,
    DynamicHeightDirective,
    IconsModule,
    DialogModule,
  ],
  templateUrl: './control-number-config.component.html',
  styleUrl: './control-number-config.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ControlNumberConfigComponent implements OnInit, OnDestroy {
  public controlNumberFacade = inject(ControlNumberConfigFacade)

  public injector = inject(Injector)

  @Input() public projectId: number

  private readonly toDestroy$ = new Subject<void>()

  public custodianMediaList = toSignal(
    this.controlNumberFacade.getCustodianMedia$,
    {
      initialValue: [],
    }
  )

  public isTreeLoading = signal<boolean>(false)

  public selectedIds: string[] = []

  public expandedIds: number[] = []

  public treeData = signal<TreeModel[]>([])

  public readonly custodianWarningMsg =
    'Control number may not be sequential as documents are deleted/reprocessed.'

  public editableTreeData = computed(() => {
    return this.treeData().filter((item) => item.isEditable && !item.isDisabled)
  })

  public checkableSettings: CheckableSettings = {
    checkChildren: true,
    checkParents: true,
    enabled: true,
    mode: 'multiple',
    checkOnClick: false,
    uncheckCollapsedChildren: true,
  }

  public editingPrefixes: { [id: string]: string } = {}

  public editingState: { [id: string]: boolean } = {}

  public updatedPrefixes: { [id: string]: string } = {}

  public showSpinner = signal<boolean>(false)

  public ngOnInit(): void {
    this.isTreeLoading.set(true)
    this.fetchCustodianMedia()
    this.prepareTreeData()
  }

  public ngOnDestroy(): void {
    this.controlNumberFacade.resetControlNumberConfigState()
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  private prepareTreeData(): void {
    effect(
      () => {
        const custodianMedia = this.custodianMediaList()
        if (!custodianMedia) {
          return
        }
        const transformedData = this.transformToTreeModel(custodianMedia)
        this.treeData.set(transformedData)
        this.expandedIds = transformedData
          .filter((item) => item.isCustodian)
          .map((item) => item.custodianId)
        this.isTreeLoading.set(false)
      },
      { injector: this.injector, allowSignalWrites: true }
    )
  }

  private transformToTreeModel(
    data: CustodianMediaControlNumberConfig[]
  ): TreeModel[] {
    const tree: TreeModel[] = []
    const custodianMap = new Map<number, TreeModel>()

    data.forEach((item) => {
      const custodianKey = item.custodianId

      // Create a parent custodian node if not already present
      if (!custodianMap.has(custodianKey)) {
        const custodianNode: TreeModel = {
          id: `custodian-${item.custodianId}`,
          custodianId: item.custodianId,
          custodianName: item.custodianName,
          isCustodian: true,
          hasWarning: item.hasWarning,
          prefix: item.prefix,
          startNumber: item.startNumber,
          endNumber: item.endNumber,
          isEditable: false,
          isDisabled: false,
        }
        custodianMap.set(custodianKey, custodianNode)
        tree.push(custodianNode)
      }

      // Create the media node
      const mediaNode: TreeModel = {
        id: `media-${item.mediaId}`,
        parentId: `custodian-${item.custodianId}`,
        mediaId: item.mediaId,
        mediaName: item.mediaName,
        isCustodian: false,
        prefix: item.prefix,
        startNumber: item.startNumber,
        endNumber: item.endNumber,
        hasWarning: item.hasWarning,
        isEditable: item.isEditable,
        isDisabled: false,
      }
      tree.push(mediaNode)
    })

    return tree
  }

  private fetchCustodianMedia(): void {
    this.controlNumberFacade.fetchCustodianMedia(this.projectId)
  }

  public editPrefix(item: TreeModel): void {
    this.editingState[item.id] = true
    this.editingPrefixes[item.id] = item.prefix
    setTimeout(() => {
      const input: HTMLInputElement | null = document.querySelector(
        `kendo-textbox[id="prefix-${item.id}"] input`
      )
      if (input) {
        input.focus()
      }
    }, 0)
  }

  public savePrefix(item: TreeModel): void {
    const newPrefix = this.editingPrefixes[item.id]?.trim()
    if (
      newPrefix !== undefined &&
      newPrefix !== item.prefix &&
      newPrefix !== ''
    ) {
      item.prefix = newPrefix
      this.updatedPrefixes[item.id] = newPrefix
    }
    this.editingState[item.id] = false
  }

  public cancelEdit(item: TreeModel): void {
    this.editingState[item.id] = false
    delete this.editingPrefixes[item.id]
  }

  public getControlNumberDetails(): GenerateControlNumberRequestModel {
    const medias = this.treeData()
      .filter((item) => this.selectedIds.includes(item.id))
      .map((item) => ({
        mediaId: item.mediaId,
        prefix: item.prefix,
        startNumber: item.startNumber,
        endNumber: item.endNumber,
      })) as GenerateControlNumberModel[]
    return { medias }
  }

  public selectAllChecked(): CheckBoxState {
    if (this.selectedIds.length === 0) {
      return false
    } else if (
      this.selectedIds.length ===
      this.treeData().filter((t) => t.isEditable).length
    ) {
      return true
    }
    return 'indeterminate'
  }

  public onSelectAllChange(isChecked: string | boolean): void {
    if (isChecked) {
      this.selectedIds = this.editableTreeData().map((item) => item.id)
    } else {
      this.selectedIds = []
    }
  }

  public onCustodianCheckboxChange(item: TreeModel, event: any): void {
    const ids = this.editableTreeData()
      .filter((media) => media.parentId === item.id)
      .map((media) => media.id)
    if (event.target.checked) {
      this.selectedIds = this.selectedIds.filter((id) => !ids.includes(id))
      this.selectedIds.push(...ids)
    } else {
      this.selectedIds = this.selectedIds.filter((id) => !ids.includes(id))
    }
  }

  public isCustodianChecked = (dataItem: any): CheckedState => {
    if (this.selectedIds.length === 0) {
      return 'none'
    }
    const medias = this.editableTreeData().filter(
      (media) => media.parentId === dataItem.id
    )
    if (!medias.length) {
      return 'none'
    }
    const allChecked = medias.every(
      (media) => this.selectedIds.indexOf(media['id']) > -1
    )
    const anyChecked = medias.some(
      (media) => this.selectedIds.indexOf(media['id']) > -1
    )
    if (allChecked) {
      return 'checked'
    } else if (anyChecked) {
      return 'indeterminate'
    }
    return 'none'
  }

  public isCustodianDisabled = (dataItem: any): boolean => {
    // Disable custodian checkbox if no media items are editable under it
    return (
      this.editableTreeData().filter(
        (media) => media.parentId === dataItem.id && media.isEditable
      ).length === 0
    )
  }

  public onCheckboxChange(item: TreeModel, event: any): void {
    if (event.target.checked) {
      this.selectedIds.push(item.id)
    } else {
      this.selectedIds = this.selectedIds.filter((id) => id !== item.id)
    }
  }

  public getUpdatedPrefixes(): { [id: string]: string } {
    return this.updatedPrefixes
  }

  public treeViewTrackByFn(index: number, item: any): any {
    return item.id
  }
}
