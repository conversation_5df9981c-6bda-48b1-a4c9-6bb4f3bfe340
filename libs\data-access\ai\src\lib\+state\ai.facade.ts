import { Injectable } from '@angular/core'
import { select, Store } from '@ngrx/store'
import { Observable, Subject } from 'rxjs'
import * as AiActions from './ai.actions'
import * as AiSelectors from './ai.selectors'
import { AIState } from './ai.reducer'
import {
  AIJobType,
  AIRelevanceJobsResponseModel,
  AiSearchPayload,
  AiSearchResult,
  AiSearchUiTypes,
  EdaiStatusFilterCriteria,
  JobRequestModel,
} from '../models/interfaces/ai.model'
import {
  TableData,
  EciDashboardFilters,
  ECADashboardRequestModel,
  SunburstChartType,
} from '../models/interfaces/eci-dashboard.model'
import { WebSocketMessage } from '@venio/data-access/common'

type AIStateKeys = keyof AIState | Array<keyof AIState>
@Injectable({ providedIn: 'root' })
export class AiFacade {
  public loadRelevanceJob$: Subject<AIRelevanceJobsResponseModel> =
    new Subject<AIRelevanceJobsResponseModel>()

  public readonly selectIsAiSearchLoading$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('isSearchLoading'))
  )

  public readonly selectActiveUuid$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('activeUuid'))
  )

  public readonly selectAllFileId$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('allFileIds'))
  )

  public readonly selectAiSearchAiSummaryList$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('searchSummaryList'))
  )

  public readonly selectSelectedDocumentSummary$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('selectedDocumentSummary'))
  )

  public readonly selectAiSearchErrorResponse$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('aiSearchError'))
  )

  public readonly selectSelectedSearchTab$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('selectedSearchTab'))
  )

  public readonly selectIsResetTriggered$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('isResetTriggered'))
  )

  public readonly selectIsCreateJobEdaiLoading$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('isCreateJobEdaiLoading'))
  )

  public readonly selectCreateJobEdaiSuccess$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('createJobEdaiSuccess'))
  )

  public readonly selectCreateJobEdaiError$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('createJobEdaiError'))
  )

  public readonly isEdaiRelevanceCompletedJobsLoading$ = this.store.pipe(
    select(
      AiSelectors.getStateFromAiStore('isEdaiRelevanceCompletedJobsLoading')
    )
  )

  public readonly selectEdaiRelevanceCompletedJobs = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('edaiRelevanceCompletedJobs'))
  )

  public readonly isEdaiStatusLoading$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('isEdaiStatusLoading'))
  )

  public readonly isEdaiDocumentRelevanceLoading$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('isEdaiDocumentRelevanceLoading'))
  )

  public readonly isEdaiJobStatusDetailLoading$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('isEdaijobStatusDetailLoading'))
  )

  public readonly selectIsEdaiDocumentRelevancyLoading$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('isEdaiDocumentRelevanceLoading'))
  )

  public readonly selectEdaiDocumentRelevancySuccess$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('edaiDocumentRelevancySuccess'))
  )

  public readonly selectEdaiDocumentRelevancyError$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('edaiDocumentRelevancyError'))
  )

  public readonly selectIsEdaiDocumentPrivilegeLoading$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('isEdaiDocumentPrivilegeLoading'))
  )

  public readonly selectEdaiDocumentPrivilegeSuccess$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('edaiDocumentPrivilegeSuccess'))
  )

  public readonly selectEdaiDocumentPrivilegeError$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('edaiDocumentPrivilegeError'))
  )

  public readonly selectEdaiStatusResults$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('edaiStatusResults'))
  )

  public readonly selectEdaiJobStatusDetailsSuccess$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('edaiJobStatusDetailsSuccess'))
  )

  public readonly selectEdaiJobStatusDetailsError$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('edaiJobStatusDetailsError'))
  )

  public readonly isEdaiPIITemplatesLoading$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('isEdaiPIITemplateLoading'))
  )

  public readonly selectEdaiPIITemplates$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('edaiPIITemplateSuccess'))
  )

  public readonly selectIsEdaiPIIEntitiesLoading$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('isEdaiPIITEntitiesLoading'))
  )

  public readonly selectEdaiPIIEntities$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('edaiPIIEntitySuccess'))
  )

  public readonly selectEdaiDocumentPiiLoading$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('isEdaiDocumentPIILoading'))
  )

  public readonly selectEdaiDocumentPiiSuccess$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('edaiDocumentPIISuccess'))
  )

  public readonly selectEdaiDocumentPiiError$ = this.store.pipe(
    select(AiSelectors.getStateFromAiStore('edaiDocumentPIIError'))
  )

  constructor(private readonly store: Store) {}

  public performAiSearch(
    payload: AiSearchPayload,
    clientId: string,
    uuid: string
  ): void {
    this.store.dispatch(AiActions.performAiSearch({ payload, clientId, uuid }))
  }

  public updateAiProgressMessage(
    progressMessage: WebSocketMessage,
    uuid: string
  ): void {
    this.store.dispatch(
      AiActions.performAiProgressMessageUpdate({ progressMessage, uuid })
    )
  }

  public setSelectedDocumentSummary(
    selectedDocumentSummary: AiSearchResult
  ): void {
    this.store.dispatch(
      AiActions.setSelectedDocumentSummary({ selectedDocumentSummary })
    )
  }

  public updateSelectedSearchTab(selectedSearchTab: AiSearchUiTypes): void {
    this.store.dispatch(
      AiActions.updateSelectedSearchTab({ selectedSearchTab })
    )
  }

  public triggerReset(isResetTriggered: boolean): void {
    this.store.dispatch(AiActions.resetTrigger({ isResetTriggered }))
  }

  /**
   * Resetting a specific property of the AI State
   * @param {AIStateKeys} stateKey - state keys
   * @returns {void}
   */
  public resetAiState(stateKey: AIStateKeys): void {
    this.store.dispatch(AiActions.resetAiState({ stateKey }))
  }

  public createJobEdai(projectId: number, payload: JobRequestModel): void {
    this.store.dispatch(AiActions.createJobEdai({ projectId, payload }))
  }

  public fetchEdaiRelevanceCompletedJobs(projectId: number): void {
    this.store.dispatch(
      AiActions.fetchEdaiRelevanceCompletedJobs({ projectId })
    )
  }

  public fetchEdaiStatusResults(
    projectId: number,
    filterCriteria?: EdaiStatusFilterCriteria
  ): void {
    this.store.dispatch(
      AiActions.fetchEdaiStatus({ projectId, filterCriteria })
    )
  }

  public fetchEdaiDocumentRelevancy(projectId: number, fileId: number): void {
    this.store.dispatch(
      AiActions.fetchEdaiDocumentRelevancy({ projectId, fileId })
    )
  }

  public fetchEdaiDocumentPrivilege(projectId: number, fileId: number): void {
    this.store.dispatch(
      AiActions.fetchEdaiDocumentPrivilege({ projectId, fileId })
    )
  }

  public fetchEdaiDocumentPii(
    projectId: number,
    fileId: number,
    isPiiDetect: boolean
  ): void {
    this.store.dispatch(
      AiActions.fetchEdaiDocumentPii({ projectId, fileId, isPiiDetect })
    )
  }

  public fetchJobStatusDetails(
    projectId: number,
    jobId: number,
    jobType: AIJobType
  ): void {
    this.store.dispatch(
      AiActions.fetchJobStatusDetails({ projectId, jobId, jobType })
    )
  }

  public fetchPIITemplates(projectId: number): void {
    this.store.dispatch(AiActions.fetchEdaiPiiTemplate({ projectId }))
  }

  public fetchPIIEntities(projectId: number): void {
    this.store.dispatch(AiActions.fetchEdaiPiiEntities({ projectId }))
  }

  // ECI Dashboard Selectors
  public readonly selectEciIsFocusedSectionOpened$ = this.store.pipe(
    select(AiSelectors.getEciIsFocusedSectionOpened)
  )

  public readonly selectEciIsParentData$ = this.store.pipe(
    select(AiSelectors.getEciIsParentData)
  )

  public readonly selectEciShowDetails$ = this.store.pipe(
    select(AiSelectors.getEciShowDetails)
  )

  public readonly selectEciShowFilterPopup$ = this.store.pipe(
    select(AiSelectors.getEciShowFilterPopup)
  )

  public readonly selectEciShowCustodianFilters$ = this.store.pipe(
    select(AiSelectors.getEciShowCustodianFilters)
  )

  public readonly selectEciCustodians$ = this.store.pipe(
    select(AiSelectors.getEciCustodians)
  )

  public readonly selectEciDocumentTypes$ = this.store.pipe(
    select(AiSelectors.getEciDocumentTypes)
  )

  public readonly selectEciFileData$ = this.store.pipe(
    select(AiSelectors.getEciFileData)
  )

  public readonly selectEciWordCloudData$ = this.store.pipe(
    select(AiSelectors.getEciWordCloudData)
  )

  public readonly selectEciRelevanceData$ = this.store.pipe(
    select(AiSelectors.getEciRelevanceData)
  )

  public readonly selectEciBarChartData$ = this.store.pipe(
    select(AiSelectors.getEciBarChartData)
  )

  public readonly selectEciTableData$ = this.store.pipe(
    select(AiSelectors.getEciTableData)
  )

  public readonly selectEciSelectedDocuments$ = this.store.pipe(
    select(AiSelectors.getEciSelectedDocuments)
  )

  public readonly selectEciTotalDocuments$ = this.store.pipe(
    select(AiSelectors.getEciTotalDocuments)
  )

  public readonly selectEciPaginationState$ = this.store.pipe(
    select(AiSelectors.getEciPaginationState)
  )

  public readonly selectEciPagedFileData$ = this.store.pipe(
    select(AiSelectors.getEciPagedFileData)
  )

  public readonly selectEciSortedDocumentTypes$ = this.store.pipe(
    select(AiSelectors.getEciSortedDocumentTypes)
  )

  // ECA API Selectors
  public readonly selectEcaRelevanceSuccess$ = this.store.pipe(
    select(AiSelectors.getEcaRelevanceSuccess)
  )

  public readonly selectEcaRelevanceError$ = this.store.pipe(
    select(AiSelectors.getEcaRelevanceError)
  )

  public readonly selectIsEcaRelevanceLoading$ = this.store.pipe(
    select(AiSelectors.getIsEcaRelevanceLoading)
  )

  public readonly selectEcaDocumentTypesSuccess$ = this.store.pipe(
    select(AiSelectors.getEcaDocumentTypesSuccess)
  )

  public readonly selectEcaDocumentTypesError$ = this.store.pipe(
    select(AiSelectors.getEcaDocumentTypesError)
  )

  public readonly selectIsEcaDocumentTypesLoading$ = this.store.pipe(
    select(AiSelectors.getIsEcaDocumentTypesLoading)
  )

  public readonly selectEcaTopicsRelevantSuccess$ = this.store.pipe(
    select(AiSelectors.getEcaTopicsRelevantSuccess)
  )

  public readonly selectEcaTopicsRelevantError$ = this.store.pipe(
    select(AiSelectors.getEcaTopicsRelevantError)
  )

  public readonly selectIsEcaTopicsRelevantLoading$ = this.store.pipe(
    select(AiSelectors.getIsEcaTopicsRelevantLoading)
  )

  public readonly selectEcaTopicsNonRelevantSuccess$ = this.store.pipe(
    select(AiSelectors.getEcaTopicsNonRelevantSuccess)
  )

  public readonly selectEcaTopicsNonRelevantError$ = this.store.pipe(
    select(AiSelectors.getEcaTopicsNonRelevantError)
  )

  public readonly selectIsEcaTopicsNonRelevantLoading$ = this.store.pipe(
    select(AiSelectors.getIsEcaTopicsNonRelevantLoading)
  )

  // ECI Dashboard Actions
  public setEciFocusedSectionOpened(isOpened: boolean): void {
    this.store.dispatch(AiActions.setEciFocusedSectionOpened({ isOpened }))
  }

  public setEciParentDataView(isParentData: boolean): void {
    this.store.dispatch(AiActions.setEciParentDataView({ isParentData }))
  }

  public setEciShowDetails(showDetails: boolean): void {
    this.store.dispatch(AiActions.setEciShowDetails({ showDetails }))
  }

  public setEciFilterPopupVisibility(isVisible: boolean): void {
    this.store.dispatch(AiActions.setEciFilterPopupVisibility({ isVisible }))
  }

  public setEciCustodianFiltersVisibility(isVisible: boolean): void {
    this.store.dispatch(
      AiActions.setEciCustodianFiltersVisibility({ isVisible })
    )
  }

  public fetchEciDashboardData(
    projectId: number,
    jobId?: number,
    filters?: EciDashboardFilters
  ): void {
    this.store.dispatch(
      AiActions.fetchEciDashboardData({ projectId, jobId, filters })
    )
  }

  public storeEciTableData(tableData: any[]): void {
    this.store.dispatch(AiActions.storeEciTableData({ tableData }))
  }

  public storeEciSelectedDocuments(selectedDocuments: number): void {
    this.store.dispatch(
      AiActions.storeEciSelectedDocuments({ selectedDocuments })
    )
  }

  public handleEciSunburstClick(clickData: any, isParentData: boolean): void {
    this.store.dispatch(
      AiActions.handleEciSunburstClick({ clickData, isParentData })
    )
  }

  public updateEciPagination(paginationState: any): void {
    this.store.dispatch(AiActions.updateEciPagination({ paginationState }))
  }

  public resetEciDashboardState(): void {
    this.store.dispatch(AiActions.resetEciDashboardState())
  }

  // ECA API Actions
  public fetchEcaRelevance(
    projectId: number,
    requestModel: ECADashboardRequestModel
  ): void {
    this.store.dispatch(
      AiActions.fetchEcaRelevance({ projectId, requestModel })
    )
  }

  public fetchEcaDocumentTypes(
    projectId: number,
    requestModel: ECADashboardRequestModel
  ): void {
    this.store.dispatch(
      AiActions.fetchEcaDocumentTypes({ projectId, requestModel })
    )
  }

  public fetchEcaTopicsRelevant(
    projectId: number,
    requestModel: ECADashboardRequestModel
  ): void {
    this.store.dispatch(
      AiActions.fetchEcaTopicsRelevant({ projectId, requestModel })
    )
  }

  public fetchEcaTopicsNonRelevant(
    projectId: number,
    requestModel: ECADashboardRequestModel
  ): void {
    this.store.dispatch(
      AiActions.fetchEcaTopicsNonRelevant({ projectId, requestModel })
    )
  }

  // Helper method to build ECA request model
  public buildEcaRequestModel(
    dashboardType: ECADashboardRequestModel['dashboardType'],
    searchTempTable: string,
    selectedFileIds: number[],
    unSelectedFileIds: number[],
    isBatchSelection: boolean
  ): ECADashboardRequestModel {
    return {
      dashboardType,
      searchTempTable,
      selectedFileIds,
      unSelectedFileIds,
      isBatchSelection,
    }
  }

  // Chart-specific drill-down selectors
  public readonly selectActiveChartType$ = this.store.pipe(
    select(AiSelectors.getActiveChartType)
  )

  public readonly selectChartDrillDownStates$ = this.store.pipe(
    select(AiSelectors.getChartDrillDownStates)
  )

  public selectChartDrillDownState$(
    chartType: SunburstChartType
  ): Observable<any> {
    return this.store.pipe(
      select(AiSelectors.getChartDrillDownState(chartType))
    )
  }

  public selectChartCurrentData$(
    chartType: SunburstChartType
  ): Observable<any[]> {
    return this.store.pipe(select(AiSelectors.getChartCurrentData(chartType)))
  }

  public selectChartTableData$(
    chartType: SunburstChartType
  ): Observable<TableData[]> {
    return this.store.pipe(select(AiSelectors.getChartTableData(chartType)))
  }

  public selectChartIsExpanded$(
    chartType: SunburstChartType
  ): Observable<boolean> {
    return this.store.pipe(select(AiSelectors.getChartIsExpanded(chartType)))
  }

  public selectChartSelectedNode$(
    chartType: SunburstChartType
  ): Observable<any> {
    return this.store.pipe(select(AiSelectors.getChartSelectedNode(chartType)))
  }

  public selectChartCurrentLevel$(
    chartType: SunburstChartType
  ): Observable<number> {
    return this.store.pipe(select(AiSelectors.getChartCurrentLevel(chartType)))
  }

  public selectChartCanGoBack$(
    chartType: SunburstChartType
  ): Observable<boolean> {
    return this.store.pipe(select(AiSelectors.getChartCanGoBack(chartType)))
  }

  // Chart-specific drill-down actions
  public setActiveChartType(chartType: SunburstChartType): void {
    this.store.dispatch(AiActions.setActiveChartType({ chartType }))
  }

  public initializeChartDrillDown(
    chartType: SunburstChartType,
    initialData: any[]
  ): void {
    this.store.dispatch(
      AiActions.initializeChartDrillDown({ chartType, initialData })
    )
  }

  public drillDownToNextLevel(
    chartType: SunburstChartType,
    selectedNode: any,
    nextLevelData: any[]
  ): void {
    this.store.dispatch(
      AiActions.drillDownToNextLevel({ chartType, selectedNode, nextLevelData })
    )
  }

  public drillBackToPreviousLevel(chartType: SunburstChartType): void {
    this.store.dispatch(AiActions.drillBackToPreviousLevel({ chartType }))
  }

  public resetChartDrillDown(chartType: SunburstChartType): void {
    this.store.dispatch(AiActions.resetChartDrillDown({ chartType }))
  }

  public updateChartTableData(
    chartType: SunburstChartType,
    tableData: TableData[]
  ): void {
    this.store.dispatch(
      AiActions.updateChartTableData({ chartType, tableData })
    )
  }

  public setChartSelectedNode(
    chartType: SunburstChartType,
    selectedNode: any
  ): void {
    this.store.dispatch(
      AiActions.setChartSelectedNode({ chartType, selectedNode })
    )
  }
  public downloadCSV(
    projectId: number,
    requestModel: ECADashboardRequestModel
  ): void {
    this.store.dispatch(AiActions.downloadCSV({ projectId, requestModel }))
  }
}
