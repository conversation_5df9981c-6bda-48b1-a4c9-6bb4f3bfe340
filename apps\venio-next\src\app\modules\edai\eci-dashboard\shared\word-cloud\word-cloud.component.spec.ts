import { ComponentFixture, TestBed } from '@angular/core/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { AiFacade, WordCloudData } from '@venio/data-access/ai'
import { BehaviorSubject } from 'rxjs'

import { WordCloudComponent } from './word-cloud.component'
import { DocumentsFacade, SearchFacade } from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'

describe('WordCloudComponent', () => {
  let component: WordCloudComponent
  let fixture: ComponentFixture<WordCloudComponent>

  const mockAiFacade = {
    selectEciWordCloudData$: new BehaviorSubject<WordCloudData[]>([]),
    selectEcaTopicsRelevantSuccess$: new BehaviorSubject(null),
  } satisfies Partial<AiFacade>

  const mockSearchFacade = {
    getSearchResponse$: new BehaviorSubject(null),
  } satisfies Partial<SearchFacade>

  const mockDocumentsFacade = {
    getSelectedDocuments$: new BehaviorSubject([]),
    getUnselectedDocuments$: new BehaviorSubject([]),
    getIsBatchSelected$: new BehaviorSubject(false),
  } satisfies Partial<DocumentsFacade>
  const mockActivatedRoute = {
    snapshot: {
      queryParams: {
        projectId: '123',
      },
    } as any,
  } satisfies Partial<ActivatedRoute>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [WordCloudComponent],
      providers: [
        provideMockStore({}),
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: AiFacade, useValue: mockAiFacade },
        { provide: SearchFacade, useValue: mockSearchFacade },
        { provide: DocumentsFacade, useValue: mockDocumentsFacade },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(WordCloudComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
