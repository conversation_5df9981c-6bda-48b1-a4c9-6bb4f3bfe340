<div class="t-bg-white">
  <!-- Filters Section -->
  <div class="t-flex t-justify-end t-mb-4 t-p-6">
    <button
      kendoButton
      [svgIcon]="svgFilter"
      #anchor
      (click)="onToggle()"
      themeColor="secondary"
      class="t-text-sm">
      Filters
    </button>
    @if (showFilterPopup()) {
    <kendo-popup [anchor]="anchor.element">
      @if (showCustodianFilters()) {
      <kendo-listview [data]="custodians()">
        <ng-template
          kendoListViewItemTemplate
          let-dataItem="dataItem"
          let-isFirst="isFirst">
          <venio-checkbox-list-item
            [custodian]="dataItem"></venio-checkbox-list-item>
        </ng-template>
      </kendo-listview>
      } @else {
      <div class="t-p-6 t-bg-white t-shadow-lg t-rounded-lg">
        <button kendoButton (click)="onCustodianClick()" themeColor="primary">
          Custodians
        </button>
      </div>
      }
    </kendo-popup>
    }
  </div>

  <!-- Summary Section -->
  <venio-summary />

  <!-- Focused Section (appears when chart is clicked) -->
  @if (isFocusedSectionOpened()) {
  <venio-focused-section />
  }

  <!-- Main Dashboard Content -->
  <div class="t-flex t-flex-col t-gap-6">
    <!-- Relevance and Word Cloud Row -->
    <div class="t-w-full t-grid t-grid-cols-1 lg:t-grid-cols-3 t-gap-6">
      <div class="t-col-span-1">
        <venio-relevance />
      </div>
      <div class="t-col-span-2">
        <venio-word-cloud />
      </div>
    </div>

    <!-- Three Sunburst Charts Row -->
    <div class="t-w-full t-grid t-grid-cols-1 lg:t-grid-cols-3 t-gap-6">
      <div class="t-col-span-1">
        <venio-sunburst
          [showLegend]="true"
          [chartTitle]="sunburstChartType.DocumentTypes" />
      </div>
      <div class="t-col-span-1">
        <venio-sunburst
          [showLegend]="true"
          [chartTitle]="sunburstChartType.RelevantDocuments" />
      </div>
      <div class="t-col-span-1">
        <venio-sunburst
          [showLegend]="true"
          [chartTitle]="sunburstChartType.NotRelevantDocuments" />
      </div>
    </div>

    <!-- Content Filter Bar Chart Row -->
    <div class="t-w-full t-grid t-grid-cols-1">
      <div class="t-col-span-1">
        <venio-inappropriate-content />
      </div>
    </div>
  </div>
</div>
