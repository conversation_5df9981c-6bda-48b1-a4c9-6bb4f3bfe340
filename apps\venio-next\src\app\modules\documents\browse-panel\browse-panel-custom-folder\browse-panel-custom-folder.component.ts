import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  On<PERSON>estroy,
  OnInit,
  signal,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DynamicFolderFacade,
  DynamicFolderModel,
  FolderFacade,
  FolderModel,
  SearchFacade,
  SearchSettings,
  StartupsFacade,
  CustomFolderTabTreeState,
  UserRights,
  ReviewParamService,
  ViewFacade,
  SearchDupOption,
} from '@venio/data-access/review'
import {
  combineLatest,
  debounceTime,
  filter,
  map,
  Subject,
  take,
  takeUntil,
} from 'rxjs'
import {
  FlatBindingDirective,
  SelectionItem,
  TreeListComponent,
  TreeListItem,
  TreeListModule,
} from '@progress/kendo-angular-treelist'
import {
  CombinedRightsCheckDirective,
  SvgLoaderDirective,
  UserGroupRightCheckDirective,
} from '@venio/feature/shared/directives'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import {
  CommandEventTypes,
  CommonActionTypes,
  FolderTabType,
} from '@venio/shared/models/constants'
import { ConfirmationDialogComponent } from '@venio/feature/notification'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import {
  DialogContainerDirective,
  DialogRef,
  DialogService,
} from '@progress/kendo-angular-dialog'
import { ActivatedRoute } from '@angular/router'
import { CommandsFacade, UserFacade } from '@venio/data-access/common'
import {
  IframeMessengerFacade,
  MessageContent,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import {
  ConditionElement,
  ConditionType,
  GroupStackType,
  UserModel,
} from '@venio/shared/models/interfaces'
import { isEqual } from 'lodash'
import { UuidGenerator } from '@venio/util/uuid'

interface FolderActionIconModel {
  isLoading: boolean
  actionType: CommonActionTypes
  allowedPermission: UserRights | UserRights[]
  selectedAction: null
  iconPath: string
}

@Component({
  selector: 'venio-browse-panel-custom-folder',
  standalone: true,
  imports: [
    CommonModule,
    TreeListModule,
    SvgLoaderDirective,
    InputsModule,
    ButtonsModule,
    LoaderModule,
    TooltipsModule,
    UserGroupRightCheckDirective,
    CombinedRightsCheckDirective,
    DialogContainerDirective,
  ],
  templateUrl: './browse-panel-custom-folder.component.html',
  styleUrls: ['./browse-panel-custom-folder.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BrowsePanelCustomFolderComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  private unsubscribed$ = new Subject<void>()

  @ViewChild(FlatBindingDirective)
  public dataBinding: FlatBindingDirective

  @ViewChild(TreeListComponent)
  public customFolderTreeList: TreeListComponent

  public readonly userRights = UserRights

  private breadcrumbFacade = inject(BreadcrumbFacade)

  private notificationService = inject(NotificationService)

  private dialogService = inject(DialogService)

  private activatedRoute = inject(ActivatedRoute)

  private commandsFacade = inject(CommandsFacade)

  private iframeMessengerFacade = inject(IframeMessengerFacade)

  private viewFacade = inject(ViewFacade)

  private userFacade = inject(UserFacade)

  public treeData

  private dynamicFolderIds: string[] = []

  private staticFolderIds: string[] = []

  public expandedFolderIds: string[] = []

  public UserRights = UserRights

  private allowToViewExecuteDynamicFolders: boolean

  public folderActionIcons = signal<FolderActionIconModel[]>(
    this.#initializeFolderSvgIcons()
  )

  private currentUser = signal<UserModel>({} as UserModel)

  public selectedAction = new Map<number, CommonActionTypes>()

  public selectedFolder: FolderModel & DynamicFolderModel

  public modifiedSelectedFolderFromParent: FolderModel & DynamicFolderModel

  private confirmationDialogRef: DialogRef

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public rowIndex: number

  public selectedFolderItems: SelectionItem[]

  constructor(
    private dynamicFolderFacade: DynamicFolderFacade,
    private folderFacade: FolderFacade,
    private cdr: ChangeDetectorRef,
    private searchFacade: SearchFacade,
    private startupsFacade: StartupsFacade,
    private reviewParamService: ReviewParamService
  ) {}

  public ngOnInit(): void {
    this.#selectCurrentUser()
    this.#selectViewDynamicFoldersRights()
    this.fetchCustomFolders()
    this.initSlices()
    this.#selectFolderDeletionResponses()
    this.#selectDynamicFolderSearchScope()
    this.#selectStaticFolderSearchScope()
    this.#selectSelectedFolderTreeState()
    this.#selectAdvancedSearchCommand()
    this.#selectResetSelectedFolderCommand()
  }

  private fetchCustomFolders(): void {
    this.reviewParamService.projectId
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((projectId) => {
        this.dynamicFolderFacade.fetchDynamicFolders(projectId)
        this.folderFacade.fetchStaticFolders(projectId)
      })
  }

  public ngAfterViewInit(): void {
    this.#scrollToSelectedNode()
  }

  #selectViewDynamicFoldersRights(): void {
    this.startupsFacade
      .hasGroupRight$(UserRights.ALLOW_TO_VIEW_EXECUTE_DYNAMIC_FOLDER)
      .subscribe((result: boolean) => {
        this.allowToViewExecuteDynamicFolders = result
      })
  }

  private initSlices(): void {
    combineLatest([
      this.dynamicFolderFacade.getDynamicFolders$,
      this.folderFacade.getStaticFolders$,
    ])
      .pipe(
        filter(([d, s]) => Boolean(d && s)),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(([dynamicFolders, staticFolders]) => {
        this.cdr.markForCheck()

        //customized static folders model
        const modifiedStaticFolder = staticFolders
          .filter((f) => !!f)
          .map((f) => {
            return {
              ...f,
              lineage: f.folderLineage,
              id: 'F_' + f.folderId.toString(),
              parentId:
                f.parentFolderId < 0
                  ? '-2'
                  : 'F_' + f.parentFolderId.toString(),
              isPlaceholder: false,
              isDynamicFolder: false,
            }
          })

        //To be used during expand/collapse all
        this.staticFolderIds = modifiedStaticFolder
          .filter((f) => !!f)
          .map((folder) => folder.id)
          .concat('-2')

        //Dummy root node for Static Folder
        const rootStaticFolder = {
          folderId: -2,
          folderName: 'Static Folder',
          lineage: '',
          searchSettings: null,
          id: '-2',
          parentId: null,
          isplaceholder: true,
          isDynamicFolder: false,
        }

        if (this.allowToViewExecuteDynamicFolders) {
          //customized dynamic folders model
          const modifiedDynamicFolder = dynamicFolders
            .filter((f) => !!f)
            .map((f) => {
              return {
                ...f,
                isDynamicFolder: true,
              }
            })

          //To be used during expand/collapse all
          this.dynamicFolderIds = modifiedDynamicFolder
            .filter((f) => !!f)
            .map((folder) => folder.id)
            .concat('-1')

          //Dummy root node for Dynamic Folder
          const rootDynamicFolder = {
            folderId: -1,
            folderName: 'Dynamic Folder',
            lineage: '',
            searchSettings: null,
            id: '-1',
            parentId: null,
            isPlaceholder: true,
            isDyanmicFolder: true,
          }

          this.treeData = [
            rootDynamicFolder,
            ...modifiedDynamicFolder,
            rootStaticFolder,
            ...modifiedStaticFolder,
          ]
        } else this.treeData = [rootStaticFolder, ...modifiedStaticFolder]

        // If there are any previous-expanded folder Ids, then keep them as it is.
        const previousExpandedKeys = this.expandedFolderIds.slice()
        const hasExpandedFolderIds = this.expandedFolderIds?.[0]

        //Default expanded folder Ids
        this.expandedFolderIds = hasExpandedFolderIds
          ? [...previousExpandedKeys]
          : this.treeData.filter((f) => !f.parentId).map((f) => f.id)
      })
  }

  public ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }

  public expandAllChildNodes(dataItem): void {
    if (dataItem.folderName === 'Dynamic Folder') {
      this.expandedFolderIds = this.expandedFolderIds
        ? this.expandedFolderIds.concat(this.dynamicFolderIds)
        : this.dynamicFolderIds
    } else {
      this.expandedFolderIds = this.expandedFolderIds
        ? this.expandedFolderIds.concat(this.staticFolderIds)
        : this.staticFolderIds
    }
  }

  public collapseAllChildNodes(dataItem): void {
    if (dataItem.folderName === 'Dynamic Folder') {
      this.expandedFolderIds = this.expandedFolderIds?.filter(
        (id) => !this.dynamicFolderIds.includes(id)
      )
    } else {
      this.expandedFolderIds = this.expandedFolderIds?.filter(
        (id) => !this.staticFolderIds.includes(id)
      )
    }
  }

  /*
  Filter the tree list based on the input value
  */
  public onFilter(value: string): void {
    this.dataBinding.filter = {
      logic: 'or',
      filters: [
        {
          field: 'folderName',
          operator: 'contains',
          value: value,
        },
      ],
    }
    this.dataBinding.rebind()
  }

  public async onSelectionChange(e): Promise<void> {
    const selectedFolder = e.items[0].dataItem
    const eventFromAdvancedSearch = e.items[0].eventFromAdvancedSearch

    if (selectedFolder.folderId < 0 || selectedFolder.isPlaceholder) {
      // this.searchFacade.setDynamicFolderSearchScope(null)
      // this.searchFacade.setStaticFolderSearchScope(null)
      return
    }

    if (e.action === 'select') {
      // this.searchFacade.setStaticFolderSearchScope(null)
      // this.searchFacade.setDynamicFolderSearchScope(selectedFolder)
      const payload = {
        id: UuidGenerator.uuid,
        groupStackType: selectedFolder.isDynamicFolder
          ? GroupStackType.DYNAMIC_FOLDER
          : GroupStackType.FOLDERS,
        checked: true,
        conditionType: ConditionType.Group,
        conditions: [
          {
            conditionSyntax:
              selectedFolder.isDynamicFolder &&
              selectedFolder.searchSettings.searchExpression.isSqlMode
                ? `DYNAMIC_FOLDER="${selectedFolder.lineage}"`
                : selectedFolder.isDynamicFolder &&
                  !selectedFolder.searchSettings.searchExpression.isSqlMode
                ? selectedFolder.searchSettings.searchExpression.expression
                : `FOLDERS("${selectedFolder.lineage}")`,
          },
        ] as ConditionElement[],
      }
      // The breadcrumb stacks always need to be reset when a new folder is selected
      this.breadcrumbFacade.resetBreadcrumbCurrentStates()
      this.breadcrumbFacade.storeBreadcrumbs([payload])
      let searchPayload = null
      if (selectedFolder.isDynamicFolder) {
        this.searchFacade.setIncludePC(
          selectedFolder.searchSettings.includeParentChild
        )
        this.searchFacade.setSearchDupOption(
          SearchDupOption[
            selectedFolder.searchSettings
              .searchDuplicateOption as keyof typeof SearchDupOption
          ]
        )
        searchPayload = {
          isLoadFile: selectedFolder.searchSettings.isLoadFileSearch,
          isResetBaseGuid: true,
          dynamicFolderId: selectedFolder.folderId,
          isDynamicFolderGlobal: selectedFolder.isGlobal,
        }
      } else {
        searchPayload = {
          searchExpression: `FOLDERS("${selectedFolder.lineage}")`,
          isResetBaseGuid: true,
        }
      }
      this.breadcrumbFacade.selectCompleteBreadcrumbSyntax$
        .pipe(take(1))
        .subscribe((searchExpression) => {
          searchPayload = { ...searchPayload, searchExpression }
          this.searchFacade.search(searchPayload)
        })

      // Reset the current search input box value when the folder is selected from the tree list
      if (!eventFromAdvancedSearch) {
        this.#resetSelectionRelatedState()
      }
    }
  }

  public isButtonDisabled(
    icon: FolderActionIconModel,
    dataItem: FolderModel
  ): boolean {
    return (
      icon.isLoading &&
      this.selectedAction.get(dataItem.folderId) === icon.actionType &&
      this.selectedFolder?.folderId === dataItem.folderId
    )
  }

  public getButtonClass(
    icon: FolderActionIconModel,
    dataItem: FolderModel
  ): { '!t-pt-0 !t-pb-0': boolean } {
    return {
      '!t-pt-0 !t-pb-0':
        icon.isLoading &&
        this.selectedAction.get(dataItem.folderId) === icon.actionType &&
        this.selectedFolder?.folderId === dataItem.folderId,
    }
  }

  public getIconHoverColor(
    actionType: CommonActionTypes
  ): '#ED7425' | '#FFBB12' {
    return actionType === CommonActionTypes.DELETE ? '#ED7425' : '#FFBB12'
  }

  public tagActionClicked(
    actionType: CommonActionTypes,
    selected: FolderModel & DynamicFolderModel
  ): void {
    this.selectedFolder = selected

    this.selectedAction.set(selected.folderId, actionType)
    switch (actionType) {
      case CommonActionTypes.DELETE:
        this.#launchAndSetupConfirmationDialog()
        break
      case CommonActionTypes.EDIT:
        this.commandsFacade.dispatchCommand({
          type: CommandEventTypes.NotifyLaunchAdvancedSearch,
          data: {
            isLaunch: true,
            selectedFolder: this.selectedFolder,
          },
        })
        break
    }
  }

  public customFolderTrackByFn(index: number, item: TreeListItem): number {
    return item.data['id'] as number
  }

  /**
   * Validates a data item based on its type and the provided icon and the permission in the item.
   * This function checks if the item can be deleted or edited based on permission,
   * its properties and the action type of the icon.
   *
   * @param {Object} item - The data item to be validated.
   * This item must implement both FolderModel and DynamicFolderModel interfaces.
   * @param {FolderActionIconModel} icon - The icon associated with the action to be performed on the item.
   * @returns {boolean} - True if the item is valid for the given action (delete or edit), false otherwise.
   */
  public shouldAllowRenderAction(
    item: FolderModel & DynamicFolderModel,
    icon: FolderActionIconModel
  ): boolean {
    // If the action is for delete the item, then return true
    const isDeleteIcon = icon.actionType === CommonActionTypes.DELETE

    // If the action is for edit the item, some logic to be applied
    const shouldDisplayEditIcon = item.isDynamicFolder && !item.isPlaceholder

    // If the item is a dynamic folder, and it is global, it has no permission to dynamic local folders
    if (
      (item.isDynamicFolder &&
        !this.#hasFolderPermission(item, 'search and edit')) ||
      (!item.isDynamicFolder && !this.#hasFolderPermission(item, 'READ_WRITE'))
    ) {
      return false
    }

    return isDeleteIcon || shouldDisplayEditIcon
  }

  #resetSelectionRelatedState(): void {
    this.searchFacade.setSearchExpression(' ')
    this.iframeMessengerFacade.resetMessengerState(MessageType.SEARCH_CHANGE)
  }

  /**
   * Checks if the current user has a specified role permission on a given folder item.
   *
   * @param {object} item - The folder item to check permissions for.
   * @param {string} permissionValue - The permission string to validate against the user's role.
   * @returns {boolean} True if the permission matches, null if no group permissions are found on the item.
   */
  #hasFolderPermission(
    item: FolderModel & DynamicFolderModel,
    permissionValue: 'search and edit' | 'READ_WRITE'
  ): boolean {
    if (!item?.groupPermissions && !item?.userGroupPermissionFolder) return null

    const { globalRoleId } = this.currentUser()

    const globalPermission = item.groupPermissions?.find(
      (gp) => gp.globalRoleId === globalRoleId
    )
    const regex = new RegExp(permissionValue, 'gi')

    return item.isDynamicFolder
      ? globalPermission?.permission.match(regex) !== null
      : item.userGroupPermissionFolder.match(regex) !== null
  }

  #resetFolderDeletionState(): void {
    this.folderFacade.resetFolderState([
      'isFolderDeleting',
      'deleteFolderSuccessResponse',
      'deleteFolderErrorResponse',
    ])
  }

  #setDialogInput(instance: ConfirmationDialogComponent): void {
    instance.title = 'Confirm Folder Deletion'
    instance.message = `Are you sure you want to delete the folder "${this.selectedFolder.folderName}"?`
  }

  #performTaskAfterConfirmation(): void {
    this.confirmationDialogRef.result
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((result) => {
        const isConfirm = typeof result === 'boolean' && result === true
        this.selectedAction.clear()
        if (!isConfirm) return

        this.#updateIsLoadingStateForFolderControls(
          CommonActionTypes.DELETE,
          isConfirm
        )
        const params = {
          isDynamic: this.selectedFolder.isDynamicFolder,
          isGlobal: this.selectedFolder.isGlobal,
        }
        this.folderFacade.deleteFolder(
          this.projectId,
          this.selectedFolder.folderId,
          params
        )
      })
  }

  #launchAndSetupConfirmationDialog(): void {
    this.confirmationDialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-delete',
      width: '35rem',
    })

    this.confirmationDialogRef.dialog.onDestroy(() => {
      this.selectedAction.clear()
    })

    this.#setDialogInput(this.confirmationDialogRef.content.instance)
    this.#performTaskAfterConfirmation()
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 3500,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #selectFolderDeletionResponses(): void {
    combineLatest([
      this.folderFacade.selectDeleteFolderSuccessResponse$,
      this.folderFacade.selectDeleteFolderErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => Boolean(success) || Boolean(error)),
        debounceTime(200),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(([success, error]) => {
        if (!success && !error) return

        this.#showMessage(success ? success.message : error.message, {
          style: success ? 'success' : 'error',
        })

        this.selectedAction.clear()

        this.#updateIsLoadingStateForFolderControls(
          CommonActionTypes.DELETE,
          false
        )
        this.#resetFolderDeletionState()

        if (error) return

        // Once deletion is successful, fetch the folders again
        this.fetchCustomFolders()
      })
  }

  #updateIsLoadingStateForFolderControls(
    action: CommonActionTypes,
    isLoading: boolean
  ): void {
    const data = this.folderActionIcons()
    this.folderActionIcons.set(
      data.map((icon) => ({
        ...icon,
        isLoading: action === icon.actionType && isLoading,
      }))
    )
  }

  #initializeFolderSvgIcons(): Array<FolderActionIconModel> {
    const actions = [CommonActionTypes.EDIT, CommonActionTypes.DELETE]
    const iconPaths = [
      'assets/svg/icon-action-grid-pencil.svg',
      'assets/svg/icon-tagedit-delete.svg',
    ]

    return actions.map((action, index) => ({
      actionType: action,
      allowedPermission:
        action === CommonActionTypes.DELETE
          ? UserRights.ALLOW_TO_DELETE_FOLDER
          : action === CommonActionTypes.EDIT
          ? UserRights.ALLOW_TO_CREATE_NEW_FOLDER
          : null,
      iconPath: iconPaths[index],
      isLoading: false,
      selectedAction: null,
    }))
  }

  /**
   * Selects and handles changes in the dynamic folder search scope.
   * @returns {void}
   */
  #selectDynamicFolderSearchScope(): void {
    this.searchFacade.getDynamicFolderSearchScope$
      .pipe(
        filter((folder) => !!folder),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((folder) => {
        this.selectedFolderItems = [{ itemKey: folder.id, columnKey: 'id' }]
      })
  }

  /**
   * Selects and handles changes in the static folder search scope.
   * @returns {void}
   */
  #selectStaticFolderSearchScope(): void {
    this.searchFacade.getStaticFolderSearchScope$
      .pipe(
        filter((folder) => !!folder),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((folder: any) => {
        this.selectedFolderItems = [{ itemKey: folder.id, columnKey: 'id' }]
      })
  }

  #selectSelectedFolderTreeState(): void {
    this.folderFacade.getSelectedFolderTreeState$
      .pipe(
        filter(
          (selectedFolderTreeState) =>
            !!selectedFolderTreeState &&
            selectedFolderTreeState.actionType === FolderTabType.CUSTOM
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((selectedFolderTreeState: CustomFolderTabTreeState) => {
        this.cdr.markForCheck()
        this.expandedFolderIds = selectedFolderTreeState.expandedIds
        this.rowIndex = selectedFolderTreeState.rowIndex
      })
  }

  public onCellClick(e): void {
    this.viewFacade.resetView('isUserDefaultViewLoading')
    this.folderFacade.setSelectedFolderTreeState({
      actionType: FolderTabType.CUSTOM,
      expandedIds: this.expandedFolderIds,
      rowIndex: e.rowIndex,
    })
  }

  #scrollToSelectedNode(): void {
    this.customFolderTreeList?.scrollTo({ row: this.rowIndex })
  }

  /**
   * Updates the folder expression with the given updated expression.
   *
   * @param {string} updatedExpression - The new expression to update the folder with.
   * @returns {void}
   */
  #updateFolderExpression(updatedExpression: string): void {
    if (!updatedExpression || !this.selectedFolder?.isDynamicFolder) return

    const areSettingsEqual = isEqual(
      this.selectedFolder?.searchSettings,
      this.modifiedSelectedFolderFromParent?.searchSettings
    )

    if (areSettingsEqual) return

    this.folderFacade.updateFolder(this.projectId, {
      isDynamic: this.selectedFolder.isDynamicFolder,
      isGlobal: this.selectedFolder.isGlobal,
      folder: {
        ...this.selectedFolder,
        searchSettings: {
          ...this.selectedFolder.searchSettings,
          ...this.modifiedSelectedFolderFromParent?.searchSettings,
          searchExpression: {
            ...this.selectedFolder.searchSettings.searchExpression,
            expression: updatedExpression,
          },
        } as SearchSettings,
      },
    })
  }

  /**
   * Sets the selection to the given folder model.
   *
   * @param {Object} selected - The folder model to select.
   * @returns {void}
   */
  #setSelection(selected: FolderModel & DynamicFolderModel): void {
    this.onSelectionChange({
      items: [{ dataItem: selected, eventFromAdvancedSearch: true }],
      action: 'select',
    }).then(() => {
      this.cdr.markForCheck()
      this.selectedFolderItems = [
        {
          itemKey: selected.id,
          columnKey: 'id',
        },
      ]

      this.#updateFolderExpression(
        selected?.searchSettings?.searchExpression?.expression || ''
      )
    })
  }

  /**
   * Selects the advanced search command and sets the selection to the selected folder.
   * @returns {void}
   */
  #selectAdvancedSearchCommand(): void {
    this.iframeMessengerFacade
      .selectIframeMessengerContent$(MessageType.SEARCH_CHANGE)
      .pipe(
        map((mc) => mc as MessageContent),
        filter(
          (mc) =>
            Boolean(mc) &&
            Boolean(mc?.content?.['selectedCommandEvent']?.['selectedFolder'])
        ),
        map(
          (mc) =>
            mc?.content?.['selectedCommandEvent']?.[
              'selectedFolder'
            ] as DynamicFolderModel & FolderModel
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((selected) => {
        this.modifiedSelectedFolderFromParent = selected
        this.#setSelection(selected)
      })
  }

  /**
   * Subscribes to the `selectCurrentUserSuccessResponse$` observable to update the current user state.
   * Upon successful retrieval of the current user data,
   * it sets the `currentUser` model to the received data, or an empty object if undefined.
   * @returns {void}
   */
  #selectCurrentUser(): void {
    this.userFacade.selectCurrentUserSuccessResponse$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((success) => {
        this.currentUser.set(success?.data || {})
      })
  }

  #selectResetSelectedFolderCommand(): void {
    this.commandsFacade.selectResetSelectedCustomFolder$
      .pipe(
        filter((event) => Boolean(event?.['resetSelectedItemKeys'])),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(() => {
        this.cdr.markForCheck()
        this.selectedFolderItems = []
        // once we receive the payload event, we reset it to it's initial state for next time.
        this.commandsFacade.resetCommandState(
          CommandEventTypes.ResetSelectedCustomFolder
        )
        this.customFolderTreeList.updateView()
      })
  }
}
