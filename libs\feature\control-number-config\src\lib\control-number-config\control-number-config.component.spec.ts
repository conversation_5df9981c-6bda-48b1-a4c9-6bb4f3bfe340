import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ControlNumberConfigComponent } from './control-number-config.component'
import { of } from 'rxjs'
import {
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
  PLATFORM_ID,
} from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import {
  AppIdentitiesTypes,
  MESSAGE_SERVICE_CONFIG,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { environment } from '@venio/shared/environments'
import { ControlNumberConfigFacade } from '@venio/data-access/review'

describe('ControlNumberConfigComponent', () => {
  let component: ControlNumberConfigComponent
  let fixture: ComponentFixture<ControlNumberConfigComponent>

  const mockControlNumberConfigFacade: Partial<
    jest.Mocked<ControlNumberConfigFacade>
  > = {
    fetchCustodianMedia: jest.fn(),
    getCustodianMedia$: of([]),
    resetControlNumberConfigState: jest.fn(),
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ControlNumberConfigComponent],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
        {
          provide: MESSAGE_SERVICE_CONFIG,
          useValue: {
            origin: environment.allowedOrigin,
            iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
          },
        },
        {
          provide: ControlNumberConfigFacade,
          useValue: mockControlNumberConfigFacade,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ControlNumberConfigComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
