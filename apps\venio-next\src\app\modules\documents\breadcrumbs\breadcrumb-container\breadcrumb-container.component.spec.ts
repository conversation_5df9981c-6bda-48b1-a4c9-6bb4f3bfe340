import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BreadcrumbContainerComponent } from './breadcrumb-container.component'
import { WINDOW, windowFactory } from '@venio/data-access/iframe-messenger'
import { NO_ERRORS_SCHEMA, PLATFORM_ID } from '@angular/core'

import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideMockStore } from '@ngrx/store/testing'

describe('BreadcrumbContainerComponent', () => {
  let component: BreadcrumbContainerComponent
  let fixture: ComponentFixture<BreadcrumbContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BreadcrumbContainerComponent, NoopAnimationsModule],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        provideMockStore({}),
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(BreadcrumbContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  beforeEach(() => {
    // spy and mock setInterVal
    ;(global as any).setInterval = jest.fn()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
