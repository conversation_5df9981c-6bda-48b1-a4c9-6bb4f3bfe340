export const sunburstAnnotationColors = [
  '#ffffff',
  '#6305FF',
  '#0084FF',
  '#FF00FF',
  '#9600FF',
  '#1100FF',
  '#008CFF',
  '#00D0FF',
  '#0DE9B6',
  '#B940E5',
  '#FF00FF',
  '#9600FF',
  '#1100FF',
  '#008CFF',
  '#00D0FF',
  '#0DE9B6',
  '#B940E5',
]

// Color scheme specifically for relevance charts
export const relevanceChartColors = [
  'transparent', // Index 0 - used for spacing/transparent elements
  '#4A4B90', // Index 1 - Purple
  '#1DBADC', // Index 2 - Blue
  '#FFBC3E', // Index 3 - Yellow/Orange
  '#ED7425', // Index 4 - Orange
  '#9BD2A7', // Index 5 - Green
]

// Color scheme for other chart types (non-relevance)
// Based on the mockup design - vibrant and appealing colors
export const otherChartColors = [
  'transparent', // Index 0 - used for spacing/transparent elements
  '#8B5CF6', // Index 1 - Vibrant purple (from mockup)
  '#EC4899', // Index 2 - Bright pink/magenta (from mockup)
  '#3B82F6', // Index 3 - Bright blue (from mockup)
  '#06B6D4', // Index 4 - Cyan/teal (from mockup)
  '#10B981', // Index 5 - Emerald green (from mockup)
  '#F59E0B', // Index 6 - Amber/yellow (from mockup)
  '#EF4444', // Index 7 - Red (from mockup)
  '#A855F7', // Index 8 - Medium purple (from mockup)
  '#F97316', // Index 9 - Orange (from mockup)
  '#6366F1', // Index 10 - Indigo (from mockup)
  '#14B8A6', // Index 11 - Teal (from mockup)
  '#84CC16', // Index 12 - Lime green (from mockup)
  '#F472B6', // Index 13 - Light pink (from mockup)
  '#8B5CF6', // Index 14 - Purple variant (from mockup)
  '#06B6D4', // Index 15 - Cyan variant (from mockup)
]

// Legacy export for backward compatibility (defaults to other chart colors)
export const chartColors = otherChartColors
