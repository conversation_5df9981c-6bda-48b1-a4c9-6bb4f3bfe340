<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Jest tests" tests="50" failures="0" errors="0" time="27.181">
  <testsuite name="EdaiDialogComponent" errors="0" failures="0" skipped="0" timestamp="2025-07-30T11:54:19" time="26.685" tests="50">
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should successfully create the dialog component" time="0.421" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should have default form and signals when first showing the dialog" time="0.078" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should refresh the basic job info with the table name and file selections if they exist" time="0.06" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should let the user close this dialog window" time="0.061" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should move the user to the status section and reset form values when they navigate to the second tab" time="0.077" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should display a warning and not create anything when no documents are selected" time="0.052" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should create the job if documents are selected" time="0.059" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should keep the current job type but reset other fields if the user navigates back to AI job tab (index 0)" time="0.058" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should show a success message and switch to the status section when a job is created successfully" time="0.07" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should display an error message and stay in the current tab if the job creation fails" time="0.059" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should require a description in each issue if relevance is chosen as the task type" time="0.055" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should require attorney list and domains if privilege is chosen as the task type" time="0.056" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should clean up all active subscriptions when dialog is no longer used" time="0.044" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should remove Relevance validations and enable Privilege validations if the user changes from Relevance to Privilege tasks" time="0.056" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should remove Privilege validations and enable Relevance validations if the user changes from Privilege to Relevance tasks" time="0.052" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should limit the attorney list field to a maximum of 2000 characters for Privilege tasks" time="0.045" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should reflect the batch selection flag by setting document selection status accordingly" time="0.047" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should send only jobType and basic job model if user selects Relevance and submits" time="0.047" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should send only jobType and basic job model if user selects Privilege and submits" time="0.048" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should skip notifications if the message content is empty or only whitespace" time="0.052" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should require job name in the basic job info regardless of selected job type" time="0.05" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should mark documents as selected if selected documents emits a non-empty array while batch selection is off" time="0.053" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should store unselected file IDs in the form when getUnselectedDocuments$ emits data" time="0.046" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should reset form correctly when moving between tabs multiple times in a row" time="0.068" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should update the loading indicator when AI job is being create" time="0.056" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should maintain the form in a pristine state after consecutive resets" time="0.062" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should require a description for custom privilege types if the user types a name" time="0.046" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should not require a description for a custom privilege type if the user leaves the name empty in Privilege mode" time="0.037" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should keep the existing typed description for a custom privilege type if the user first provides a name, then clears it" time="0.048" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should correctly reset attorneyList and domains if the form is explicitly reset by switching tabs" time="0.048" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should keep the Relevance issues array intact if the user never switches tabs to force a form reset" time="0.05" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should allow users to remove an added issue from the Relevance array without throwing validation errors on the removed element" time="0.055" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should include only the selected file IDs in the final payload if the user is not in batch selection mode" time="0.049" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should respect the batch selection flag if the user toggles it after some files were individually selected" time="0.053" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should not require any description for default privilege types that use booleans as the name" time="0.053" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should not preserve typed domain when switching away from Privilege then switching back to Privilege again" time="0.039" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should not preserve the previously typed description for a custom privilege type if the user toggles away and back to Privilege mode" time="0.047" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should not alter any typed basic job info if the user switches from Relevance to Privilege and back to Relevance without resetting the form" time="0.054" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should not require a description when the user types a blank name for custom privilege types" time="0.052" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should require description for newly added issues in Relevance tasks" time="0.052" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should hide a displayed notification when the user clicks on it" time="0.054" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should restore the form to a pristine and untouched state after resetting via tab switch" time="0.04" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should allow additional Privilege types to remain intact if the user manually populates them before any form reset" time="0.047" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should ignore relevance validations if the user started in Relevance mode but then switched to Privilege mode before typing anything" time="0.049" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should preserve job type if the user resets the form multiple times in a row on the same tab navigation" time="0.054" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should handle no unselected documents gracefully if the user goes from having unselected documents to none" time="0.054" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should not submit the create job if job name remains empty, even if documents are selected" time="0.056" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should not build any specialized for info if job type is unrecognized, avoid form submission" time="0.115" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should handle a success object if an error is also present, prioritizing success scenario in #selectJobResponse" time="0.156" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
    <testcase classname="EdaiDialogComponent" name="EdaiDialogComponent should do nothing if both success and error objects are falsy, preventing any notification or tab change" time="0.04" file="src\app\modules\edai\edai-dialog\edai-dialog.component.spec.ts">
    </testcase>
  </testsuite>
</testsuites>