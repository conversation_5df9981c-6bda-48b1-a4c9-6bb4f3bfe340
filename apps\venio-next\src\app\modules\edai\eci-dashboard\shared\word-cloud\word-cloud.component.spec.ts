import { ComponentFixture, TestBed } from '@angular/core/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { AiFacade, WordCloudData } from '@venio/data-access/ai'
import { BehaviorSubject } from 'rxjs'

import { WordCloudComponent } from './word-cloud.component'

describe('WordCloudComponent', () => {
  let component: WordCloudComponent
  let fixture: ComponentFixture<WordCloudComponent>

  const mockAiFacade = {
    selectEciWordCloudData$: new BehaviorSubject<WordCloudData[]>([]),
    selectEcaTopicsRelevantSuccess$: new BehaviorSubject(null),
  } satisfies Partial<AiFacade>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [WordCloudComponent],
      providers: [
        provideMockStore({}),
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: AiFacade, useValue: mockAiFacade },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(WordCloudComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
