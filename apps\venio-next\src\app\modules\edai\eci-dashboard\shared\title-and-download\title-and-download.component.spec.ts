import { ComponentFixture, TestBed } from '@angular/core/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { ActivatedRoute } from '@angular/router'
import { BehaviorSubject } from 'rxjs'
import { By } from '@angular/platform-browser'
import { provideMockStore } from '@ngrx/store/testing'

import { TitleAndDownloadComponent } from './title-and-download.component'
import { AiFacade, ECADashboardType } from '@venio/data-access/ai'
import {
  DocumentsFacade,
  SearchFacade,
  SearchResponseModel,
} from '@venio/data-access/review'

describe('TitleAndDownloadComponent', () => {
  let component: TitleAndDownloadComponent
  let fixture: ComponentFixture<TitleAndDownloadComponent>

  const mockSearchResponse: SearchResponseModel = {
    tempTables: {
      searchResultTempTable: 'test-temp-table-123',
      baseGUID: 'test-guid',
      computedSearchTempTable: 'computed-table',
      savedSearchTempTable: 'saved-table',
      searchGuid: 'search-guid',
      searchId: 123,
      userTempTable: 'user-table',
    },
    error: {
      errorMessage: '',
      errorStatus: false,
    },
    searchResultIntialParameters: null,
  }

  const mockAiFacade = {
    setEciFocusedSectionOpened: jest.fn(),
    downloadCSV: jest.fn(),
  } satisfies Partial<AiFacade>

  const mockSearchFacade = {
    getSearchResponse$: new BehaviorSubject(mockSearchResponse),
  } satisfies Partial<SearchFacade>

  const mockDocumentsFacade = {
    getSelectedDocuments$: new BehaviorSubject([1, 2, 3]),
    getUnselectedDocuments$: new BehaviorSubject([4, 5]),
    getIsBatchSelected$: new BehaviorSubject(true),
  } satisfies Partial<DocumentsFacade>

  const mockActivatedRoute = {
    snapshot: {
      queryParams: {
        projectId: '123',
      },
    } as any,
  } satisfies Partial<ActivatedRoute>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TitleAndDownloadComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        { provide: AiFacade, useValue: mockAiFacade },
        { provide: SearchFacade, useValue: mockSearchFacade },
        { provide: DocumentsFacade, useValue: mockDocumentsFacade },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(TitleAndDownloadComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  describe('Component Initialization', () => {
    it('should initialize with default title', () => {
      expect(component.title).toBe('')
    })

    it('should have ECADashboardType available', () => {
      expect(component.ECADashboardType).toBe(ECADashboardType)
    })

    it('should have svgOpenNew icon defined', () => {
      expect(component.svgOpenNew).toBeDefined()
    })
  })

  describe('Input Properties', () => {
    it('should accept title input', () => {
      const testTitle = 'Test Dashboard Title'
      fixture.componentRef.setInput('title', testTitle)
      fixture.detectChanges()

      expect(component.title).toBe(testTitle)
    })

    it('should accept dashboardType input', () => {
      const testDashboardType = ECADashboardType.Inappropriate_Words
      fixture.componentRef.setInput('dashboardType', testDashboardType)
      fixture.detectChanges()

      expect(component.dashboardType).toBe(testDashboardType)
    })
  })

  describe('Template Rendering', () => {
    it('should display the title in the template', () => {
      const testTitle = 'My Dashboard Title'
      fixture.componentRef.setInput('title', testTitle)
      fixture.detectChanges()

      const titleElement = fixture.debugElement.query(By.css('h3'))
      expect(titleElement.nativeElement.textContent.trim()).toBe(testTitle)
    })

    it('should show "View Details" section when dashboardType is Inappropriate_Words', () => {
      fixture.componentRef.setInput(
        'dashboardType',
        ECADashboardType.Inappropriate_Words
      )
      fixture.detectChanges()

      const viewDetailsElement = fixture.debugElement.query(
        By.css('.t-cursor-pointer')
      )
      expect(viewDetailsElement).toBeTruthy()

      const viewDetailsText = fixture.debugElement.query(
        By.css('.t-cursor-pointer span')
      )
      expect(viewDetailsText.nativeElement.textContent.trim()).toBe(
        'View Details'
      )
    })

    it('should hide "View Details" section when dashboardType is not Inappropriate_Words', () => {
      fixture.componentRef.setInput('dashboardType', ECADashboardType.Relevance)
      fixture.detectChanges()

      const viewDetailsElement = fixture.debugElement.query(
        By.css('.t-cursor-pointer')
      )
      expect(viewDetailsElement).toBeFalsy()
    })

    it('should always show download button', () => {
      const downloadButton = fixture.debugElement.query(
        By.css('button[title="Download"]')
      )
      expect(downloadButton).toBeTruthy()
    })
  })

  describe('openFocusedSection method', () => {
    it('should call aiFacade.setEciFocusedSectionOpened with true', () => {
      // Act
      component.openFocusedSection()

      // Assert
      expect(mockAiFacade.setEciFocusedSectionOpened).toHaveBeenCalledWith(true)
    })

    it('should be called when "View Details" is clicked', () => {
      // Given
      fixture.componentRef.setInput(
        'dashboardType',
        ECADashboardType.Inappropriate_Words
      )
      fixture.detectChanges()
      const openFocusedSectionSpy = jest.spyOn(component, 'openFocusedSection')

      // When
      const viewDetailsDiv = fixture.debugElement.query(
        By.css('.t-cursor-pointer')
      )
      viewDetailsDiv.nativeElement.click()

      // Then
      expect(openFocusedSectionSpy).toHaveBeenCalled()
    })
  })

  describe('downloadCSV method', () => {
    beforeEach(() => {
      // Reset mocks before each test
      jest.clearAllMocks()

      // Reset observables to initial state
      mockSearchFacade.getSearchResponse$.next(mockSearchResponse)
      mockDocumentsFacade.getSelectedDocuments$.next([1, 2, 3])
      mockDocumentsFacade.getUnselectedDocuments$.next([4, 5])
      mockDocumentsFacade.getIsBatchSelected$.next(true)

      // Set up component with required inputs
      fixture.componentRef.setInput('dashboardType', ECADashboardType.Relevance)
      fixture.detectChanges()
    })

    it('should call downloadCSV with correct parameters when all observables emit values', () => {
      // Act
      component.downloadCSV()

      // Assert
      expect(mockAiFacade.downloadCSV).toHaveBeenCalledWith(123, {
        searchTempTable: 'test-temp-table-123',
        selectedFileIds: [1, 2, 3],
        unSelectedFileIds: [4, 5],
        isBatchSelection: true,
        dashboardType: ECADashboardType.Relevance,
      })
    })

    it('should not call downloadCSV when search response is null', () => {
      // Given
      jest.clearAllMocks()
      mockSearchFacade.getSearchResponse$.next(null as any)

      // Act
      component.downloadCSV()

      // Assert
      expect(mockAiFacade.downloadCSV).not.toHaveBeenCalled()
    })

    it('should not call downloadCSV when search response is falsy', () => {
      // Given
      jest.clearAllMocks()
      mockSearchFacade.getSearchResponse$.next(undefined as any)

      // Act
      component.downloadCSV()

      // Assert
      expect(mockAiFacade.downloadCSV).not.toHaveBeenCalled()
    })

    it('should handle different dashboard types correctly', () => {
      // Given
      fixture.componentRef.setInput(
        'dashboardType',
        ECADashboardType.DocumentType
      )
      fixture.detectChanges()

      // Act
      component.downloadCSV()

      // Assert
      expect(mockAiFacade.downloadCSV).toHaveBeenCalledWith(
        123,
        expect.objectContaining({
          dashboardType: ECADashboardType.DocumentType,
        })
      )
    })

    it('should be called when download button is clicked', () => {
      // Given
      const downloadCSVSpy = jest.spyOn(component, 'downloadCSV')

      // When
      const downloadButton = fixture.debugElement.query(
        By.css('button[title="Download"]')
      )
      downloadButton.nativeElement.click()

      // Then
      expect(downloadCSVSpy).toHaveBeenCalled()
    })

    it('should handle empty selected documents array', () => {
      // Given
      jest.clearAllMocks()
      mockDocumentsFacade.getSelectedDocuments$.next([])

      // Act
      component.downloadCSV()

      // Assert
      expect(mockAiFacade.downloadCSV).toHaveBeenCalledWith(
        123,
        expect.objectContaining({
          selectedFileIds: [],
        })
      )
    })

    it('should handle empty unselected documents array', () => {
      // Given
      jest.clearAllMocks()
      mockDocumentsFacade.getUnselectedDocuments$.next([])

      // Act
      component.downloadCSV()

      // Assert
      expect(mockAiFacade.downloadCSV).toHaveBeenCalledWith(
        123,
        expect.objectContaining({
          unSelectedFileIds: [],
        })
      )
    })

    it('should handle batch selection false', () => {
      // Given
      jest.clearAllMocks()
      mockDocumentsFacade.getIsBatchSelected$.next(false)

      // Act
      component.downloadCSV()

      // Assert
      expect(mockAiFacade.downloadCSV).toHaveBeenCalledWith(
        123,
        expect.objectContaining({
          isBatchSelection: false,
        })
      )
    })
  })

  describe('projectId getter', () => {
    it('should return correct project ID from route params', () => {
      // Act
      const projectId = component['projectId']

      // Assert
      expect(projectId).toBe(123)
    })

    it('should handle string project ID conversion', () => {
      // Act
      const projectId = component['projectId']

      // Assert
      expect(projectId).toBe(123)
      expect(typeof projectId).toBe('number')
    })
  })
})
