<form [formGroup]="printForm" class="t-h-full">
  <div class="t-flex t-flex-col">
    <div class="t-flex t-flex-row">
      <div class="t-w-1/4 t-pt-5 t-my-2">
        <kendo-textbox
          placeholder="Print Job Name"
          formControlName="printName"></kendo-textbox>
        <div
          class="t-text-error t-ml-1 t-mt-1"
          *ngIf="printForm.get('printName')?.errors">
          <ng-container
            *ngIf="printForm.get('printName')?.errors?.['required']">
            Please enter print job name
          </ng-container>
          <ng-container
            *ngIf="printForm.get('printName')?.errors?.['invalidFileName']">
            Print job name cannot contain any of the following characters: \ / :
            * ? " &lt; &gt; |
          </ng-container>
        </div>
      </div>

      <div class="t-flex t-pt-2 t-mt-5 t-mb-1">
        <div
          class="t-bg-green-200 t-w-20 custom-svg-line t-bg-no-repeat t-bg-center"></div>
      </div>

      <div class="t-w-3/4" formGroupName="tiffSourceOption">
        <kendo-label
          text="Image Source"
          class="t-font-semibold t-mb-2"></kendo-label>
        <div class="t-flex">
          <div class="t-flex-col t-p-1">
            <div class="t-flex t-mt-[0.5rem] t-m-1 t-items-center">
              <input
                id="printOriginal"
                size="small"
                type="radio"
                formControlName="printOriginal"
                value="PRINT_ORIGINAL"
                kendoRadioButton
                #printOriginalRadio />
              <kendo-label
                class="t-k-radio-label t-px-[5px] t-relative"
                for="printOriginal"
                [ngClass]="{
                  't-text-primary t-font-medium t-text-[14px]':
                    tiffSourceOption.get('printOriginal')?.value ===
                      'PRINT_ORIGINAL' && !printOriginalRadio.disabled
                }"
                text="Print Original Image"></kendo-label>
            </div>
            <div class="t-ml-6">
              <div class="t-flex t-mt-[0.5rem] t-m-1 t-items-center">
                <input
                  id="withRedaction"
                  size="small"
                  type="radio"
                  value="PRINT_ORIGINAL_TIFF_WITH_REDACTION"
                  formControlName="printOption"
                  [attr.disabled]="showButton === true ? 'disabled' : null"
                  kendoRadioButton
                  #withRedactionRadio />
                <kendo-label
                  class="t-k-radio-label t-px-[5px] t-relative"
                  for="withRedaction"
                  [ngClass]="{
                    't-text-primary t-font-medium t-text-[14px]':
                      tiffSourceOption.get('printOption')?.value ===
                        'PRINT_ORIGINAL_TIFF_WITH_REDACTION' &&
                      !withRedactionRadio.disabled,
                    't-text-[#D5D5D5] t-opacity-100':
                      withRedactionRadio.disabled
                  }"
                  text="With Redaction"></kendo-label>
              </div>
              <div class="t-flex t-mt-[0.5rem] t-m-1 t-items-center">
                <input
                  id="without-redaction"
                  size="small"
                  type="radio"
                  value="PRINT_ORIGINAL_TIFF_WITHOUT_REDACTION"
                  formControlName="printOption"
                  [attr.disabled]="showButton === true ? 'disabled' : null"
                  kendoRadioButton
                  #withoutRedactionRadio />
                <kendo-label
                  class="t-k-radio-label t-px-[5px] t-relative"
                  for="without-redaction"
                  [ngClass]="{
                    't-text-primary t-font-medium t-text-[14px]':
                      tiffSourceOption.get('printOption')?.value ===
                        'PRINT_ORIGINAL_TIFF_WITHOUT_REDACTION' &&
                      !withoutRedactionRadio.disabled,
                    't-text-[#D5D5D5] t-opacity-100':
                      withoutRedactionRadio.disabled
                  }"
                  text="Without Redaction"></kendo-label>
              </div>
            </div>
          </div>
          <div class="t-flex-col t-p-1">
            <div class="t-flex t-mt-[0.5rem] t-m-1 t-items-center">
              <input
                id="print-produced-tiff"
                size="small"
                type="radio"
                value="PRINT_PRODUCED_TIFF"
                formControlName="printOriginal"
                kendoRadioButton
                [ngClass]="{
                  't-pointer-events-none t-cursor-not-allowed t-opacity-50':
                    !IsExportAvailable()
                }"
                #printProducedTiffRadio />
              <kendo-label
                class="t-k-radio-label t-px-[5px] t-relative"
                for="print-produced-tiff"
                [ngClass]="{
                  't-text-primary t-font-medium t-text-[14px]':
                    tiffSourceOption.get('printOriginal')?.value ===
                      'PRINT_PRODUCED_TIFF' && !printProducedTiffRadio.disabled,
                  't-pointer-events-none t-cursor-not-allowed t-opacity-50':
                    !IsExportAvailable()
                }"
                text="Print Produced Image"></kendo-label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="" *ngIf="showButton">
      <div class="t-flex t-flex-col">
        <kendo-grid
          id="available-Exports"
          class="v-custom-grid-table"
          [kendoGridBinding]="availableExports"
          [rowReorderable]="true"
          [height]="240"
          [pageable]="false"
          (rowReorder)="onRowReorder($event)">
          <kendo-grid-rowreorder-column
            headerClass="!t-py-[0.40rem] !t-text-primary !t-tracking-tight !t-capitalize !t-align-middle t-bg-[#F7F7F7]"
            [width]="20"></kendo-grid-rowreorder-column>
          <kendo-grid-column
            headerClass="!t-py-[0.40rem] !t-text-primary !t-tracking-tight !t-capitalize !t-align-middle t-bg-[#F7F7F7]"
            field="ExportName"
            [width]="120"
            title="Production Name"></kendo-grid-column>
        </kendo-grid>
      </div>
    </div>
  </div>
</form>
