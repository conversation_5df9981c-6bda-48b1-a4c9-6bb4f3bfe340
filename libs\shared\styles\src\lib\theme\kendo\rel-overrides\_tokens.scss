// Compatible with @progress/kendo-theme-fluent v.6.4.0

:root {
  --tb-kendo-border-radius-md: 0.125rem;
  --tb-kendo-primary-10: #a9abd1;
  --tb-kendo-primary-20: #979ac9;
  --tb-kendo-primary-30: #8488c2;
  --tb-kendo-primary-40: #7175bc;
  --tb-kendo-primary-50: #5d62b6;
  --tb-kendo-primary-60: #4b50af;
  --tb-kendo-primary-70: #41449f;
  --tb-kendo-primary-80: #383b8f;
  --tb-kendo-primary-90: #2f317f;
  --tb-kendo-primary-100: #2f3080;
  --tb-kendo-primary-110: #2f2f7f;
  --tb-kendo-primary-120: #26266d;
  --tb-kendo-primary-130: #1f1f5c;
  --tb-kendo-primary-140: #18174a;
  --tb-kendo-primary-150: #111037;
  --tb-kendo-primary-160: #0b0a24;
  --tb-kendo-primary-170: #050410;
  --tb-kendo-primary-180: #000000;
  --tb-kendo-primary-190: #000000;
  --tb-kendo-primary-200: #000000;
  --tb-kendo-primary-210: #000000;
  --tb-kendo-primary-220: #000000;
  --tb-kendo-custom-secondary-10: #c8e6d1;
  --tb-kendo-custom-secondary-20: #b6dabf;
  --tb-kendo-custom-secondary-30: #a4cfad;
  --tb-kendo-custom-secondary-40: #92c49b;
  --tb-kendo-custom-secondary-50: #80b989;
  --tb-kendo-custom-secondary-60: #6eae77;
  --tb-kendo-custom-secondary-70: #5ca365;
  --tb-kendo-custom-secondary-80: #4a9953;
  --tb-kendo-custom-secondary-90: #388f41;
  --tb-kendo-custom-secondary-100: #9bd2a7;
  --tb-kendo-custom-secondary-110: #147b1d;
  --tb-kendo-custom-secondary-120: #02710b;
  --tb-kendo-custom-secondary-130: #006300;
  --tb-kendo-custom-secondary-140: #005500;
  --tb-kendo-custom-secondary-150: #004700;
  --tb-kendo-custom-secondary-160: #003900;
  --tb-kendo-custom-secondary-170: #002b00;
  --tb-kendo-custom-secondary-180: #001d00;
  --tb-kendo-custom-secondary-190: #000f00;
  --tb-kendo-custom-secondary-200: #000100;
  --tb-kendo-custom-secondary-210: #000000;
  --tb-kendo-custom-secondary-220: #000000;
  --tb-kendo-neutral-10: #fcfcfc;
  --tb-kendo-neutral-20: #f0f0f0;
  --tb-kendo-neutral-30: #e3e3e3;
  --tb-kendo-neutral-40: #d6d6d6;
  --tb-kendo-neutral-50: #c9c9c9;
  --tb-kendo-neutral-60: #bdbdbd;
  --tb-kendo-neutral-70: #b0b0b0;
  --tb-kendo-neutral-80: #a3a3a3;
  --tb-kendo-neutral-90: #969696;
  --tb-kendo-neutral-100: #979797;
  --tb-kendo-neutral-110: #969696;
  --tb-kendo-neutral-120: #8c8787;
  --tb-kendo-neutral-130: #827878;
  --tb-kendo-neutral-140: #776a69;
  --tb-kendo-neutral-150: #6b5c5b;
  --tb-kendo-neutral-160: #263238;
  --tb-kendo-neutral-170: #534241;
  --tb-kendo-neutral-180: #463535;
  --tb-kendo-neutral-190: #382929;
  --tb-kendo-neutral-200: #2a1e1d;
  --tb-kendo-neutral-210: #1c1312;
  --tb-kendo-neutral-220: #0c0808;

  --tb-kendo-info-10: #ffffff;
  --tb-kendo-info-20: #e8f8fc;
  --tb-kendo-info-30: #d2f1f8;
  --tb-kendo-info-40: #bbeaf4;
  --tb-kendo-info-50: #a5e3f1;
  --tb-kendo-info-60: #8edcee;
  --tb-kendo-info-70: #77d6ea;
  --tb-kendo-info-80: #61cfe6;
  --tb-kendo-info-90: #4ac8e3;
  --tb-kendo-info-100: #34c1e0;
  --tb-kendo-info-110: #1dbadc;
  --tb-kendo-info-120: #1aa7c6;
  --tb-kendo-info-130: #1795b0;
  --tb-kendo-info-140: #14829a;
  --tb-kendo-info-150: #117084;
  --tb-kendo-info-160: #0e5d6e;
  --tb-kendo-info-170: #0c4a58;
  --tb-kendo-info-180: #093842;
  --tb-kendo-info-190: #06252c;
  --tb-kendo-info-200: #031316;
  --tb-kendo-info-210: #000000;

  --tb-kendo-tertiary-100: #ffbb12;
  --tb-kendo-error-100: #ed7425;
  --tb-kendo-success-100: #9bd2a7;
  --tb-kendo-warning-100: #ffc80a;
  --tb-kendo-body-bg: #ffffff;
  --tb-kendo-series-a-100: #0099bc;
  --tb-kendo-series-b-100: #e74856;
  --tb-kendo-series-c-100: #ffb900;
  --tb-kendo-series-d-100: #0078d7;
  --tb-kendo-series-e-100: #8764b8;
  --tb-kendo-series-f-100: #00cc6a;
  --tb-kendo-box-shadow-depth-1: 0 1.6px 3.6px 0 #00000022,
    0 0.3px 0.9px 0 #0000001c;
  --tb-kendo-box-shadow-depth-2: 0 3.2px 7.2px 0 #00000022,
    0 0.6px 1.8px 0 #0000001c;
  --tb-kendo-box-shadow-depth-3: 0 6.4px 14.4px 0 #00000022,
    0 1.2px 3.6px 0 #0000001c;
  --tb-kendo-box-shadow-depth-4: 0 25.6px 57.6px 0 #00000038,
    0 4.8px 14.4px 0 #0000002e;

  --kendo-border-radius-md: var(--tb-kendo-border-radius-md);
  --kendo-primary-10: var(--tb-kendo-primary-10);
  --kendo-primary-20: var(--tb-kendo-primary-20);
  --kendo-primary-30: var(--tb-kendo-primary-30);
  --kendo-primary-40: var(--tb-kendo-primary-40);
  --kendo-primary-50: var(--tb-kendo-primary-50);
  --kendo-primary-60: var(--tb-kendo-primary-60);
  --kendo-primary-70: var(--tb-kendo-primary-70);
  --kendo-primary-80: var(--tb-kendo-primary-80);
  --kendo-primary-90: var(--tb-kendo-primary-90);
  --kendo-primary-100: var(--tb-kendo-primary-100);
  --kendo-primary-110: var(--tb-kendo-primary-110);
  --kendo-primary-120: var(--tb-kendo-primary-120);
  --kendo-primary-130: var(--tb-kendo-primary-130);
  --kendo-primary-140: var(--tb-kendo-primary-140);
  --kendo-primary-150: var(--tb-kendo-primary-150);
  --kendo-primary-160: var(--tb-kendo-primary-160);
  --kendo-primary-170: var(--tb-kendo-primary-170);
  --kendo-primary-180: var(--tb-kendo-primary-180);
  --kendo-primary-190: var(--tb-kendo-primary-190);
  --kendo-primary-200: var(--tb-kendo-primary-200);
  --kendo-primary-210: var(--tb-kendo-primary-210);
  --kendo-primary-220: var(--tb-kendo-primary-220);

  --kendo-custom-secondary-10: var(--tb-kendo-custom-secondary-10);
  --kendo-custom-secondary-20: var(--tb-kendo-custom-secondary-20);
  --kendo-custom-secondary-30: var(--tb-kendo-custom-secondary-30);
  --kendo-custom-secondary-40: var(--tb-kendo-custom-secondary-40);
  --kendo-custom-secondary-50: var(--tb-kendo-custom-secondary-50);
  --kendo-custom-secondary-60: var(--tb-kendo-custom-secondary-60);
  --kendo-custom-secondary-70: var(--tb-kendo-custom-secondary-70);
  --kendo-custom-secondary-80: var(--tb-kendo-custom-secondary-80);
  --kendo-custom-secondary-90: var(--tb-kendo-custom-secondary-90);
  --kendo-custom-secondary-100: var(--tb-kendo-custom-secondary-100);
  --kendo-custom-secondary-110: var(--tb-kendo-custom-secondary-110);
  --kendo-custom-secondary-120: var(--tb-kendo-custom-secondary-120);
  --kendo-custom-secondary-130: var(--tb-kendo-custom-secondary-130);
  --kendo-custom-secondary-140: var(--tb-kendo-custom-secondary-140);
  --kendo-custom-secondary-150: var(--tb-kendo-custom-secondary-150);
  --kendo-custom-secondary-160: var(--tb-kendo-custom-secondary-160);
  --kendo-custom-secondary-170: var(--tb-kendo-custom-secondary-170);
  --kendo-custom-secondary-180: var(--tb-kendo-custom-secondary-180);
  --kendo-custom-secondary-190: var(--tb-kendo-custom-secondary-190);
  --kendo-custom-secondary-200: var(--tb-kendo-custom-secondary-200);
  --kendo-custom-secondary-210: var(--tb-kendo-custom-secondary-210);
  --kendo-custom-secondary-220: var(--tb-kendo-custom-secondary-220);

  --kendo-info-10: var(--tb-kendo-info-10);
  --kendo-info-20: var(--tb-kendo-info-20);
  --kendo-info-30: var(--tb-kendo-info-30);
  --kendo-info-40: var(--tb-kendo-info-40);
  --kendo-info-50: var(--tb-kendo-info-50);
  --kendo-info-60: var(--tb-kendo-info-60);
  --kendo-info-70: var(--tb-kendo-info-70);
  --kendo-info-80: var(--tb-kendo-info-80);
  --kendo-info-90: var(--tb-kendo-info-90);
  --kendo-info-100: var(--tb-kendo-info-100);
  --kendo-info-110: var(--tb-kendo-info-110);
  --kendo-info-120: var(--tb-kendo-info-120);
  --kendo-info-130: var(--tb-kendo-info-130);
  --kendo-info-140: var(--tb-kendo-info-140);
  --kendo-info-150: var(--tb-kendo-info-150);
  --kendo-info-160: var(--tb-kendo-info-160);
  --kendo-info-170: var(--tb-kendo-info-170);
  --kendo-info-180: var(--tb-kendo-info-180);
  --kendo-info-190: var(--tb-kendo-info-190);
  --kendo-info-200: var(--tb-kendo-info-200);
  --kendo-info-210: var(--tb-kendo-info-210);
  --kendo-info-220: var(--tb-kendo-info-220);

  --kendo-neutral-10: var(--tb-kendo-neutral-10);
  --kendo-neutral-20: var(--tb-kendo-neutral-20);
  --kendo-neutral-30: var(--tb-kendo-neutral-30);
  --kendo-neutral-40: var(--tb-kendo-neutral-40);
  --kendo-neutral-50: var(--tb-kendo-neutral-50);
  --kendo-neutral-60: var(--tb-kendo-neutral-60);
  --kendo-neutral-70: var(--tb-kendo-neutral-70);
  --kendo-neutral-80: var(--tb-kendo-neutral-80);
  --kendo-neutral-90: var(--tb-kendo-neutral-90);
  --kendo-neutral-100: var(--tb-kendo-neutral-100);
  --kendo-neutral-110: var(--tb-kendo-neutral-110);
  --kendo-neutral-120: var(--tb-kendo-neutral-120);
  --kendo-neutral-130: var(--tb-kendo-neutral-130);
  --kendo-neutral-140: var(--tb-kendo-neutral-140);
  --kendo-neutral-150: var(--tb-kendo-neutral-150);
  --kendo-neutral-160: var(--tb-kendo-neutral-160);
  --kendo-neutral-170: var(--tb-kendo-neutral-170);
  --kendo-neutral-180: var(--tb-kendo-neutral-180);
  --kendo-neutral-190: var(--tb-kendo-neutral-190);
  --kendo-neutral-200: var(--tb-kendo-neutral-200);
  --kendo-neutral-210: var(--tb-kendo-neutral-210);
  --kendo-neutral-220: var(--tb-kendo-neutral-220);
  --kendo-tertiary-100: var(--tb-kendo-tertiary-100);
  --kendo-error-100: var(--tb-kendo-error-100);
  --kendo-success-100: var(--tb-kendo-success-100);
  --kendo-warning-100: var(--tb-kendo-warning-100);
  --kendo-body-bg: var(--tb-kendo-body-bg);
  --kendo-series-a-100: var(--tb-kendo-series-a-100);
  --kendo-series-b-100: var(--tb-kendo-series-b-100);
  --kendo-series-c-100: var(--tb-kendo-series-c-100);
  --kendo-series-d-100: var(--tb-kendo-series-d-100);
  --kendo-series-e-100: var(--tb-kendo-series-e-100);
  --kendo-series-f-100: var(--tb-kendo-series-f-100);
  --kendo-info-100: var(--tb-kendo-info-100);
  --kendo-box-shadow-depth-1: var(--tb-kendo-box-shadow-depth-1);
  --kendo-box-shadow-depth-2: var(--tb-kendo-box-shadow-depth-2);
  --kendo-box-shadow-depth-3: var(--tb-kendo-box-shadow-depth-3);
  --kendo-box-shadow-depth-4: var(--tb-kendo-box-shadow-depth-4);

  --kendo-font-family: Roboto;
  --kendo-font-size: 14px;
  --kendo-font-weight: 400;
  --kendo-line-height: 1.4285714286;
  --kendo-disabled-bg: transparent;
}

$tb-typography: (
  kendo-default-typography: (
    font-family: 'Roboto',
    font-size: 14px,
    font-weight: 400,
    line-height: 1.4285714286,
  ),
);

@mixin typography-classes($typography) {
  @each $selector, $property in $typography {
    &-#{$selector} {
      @each $propName, $propValue in $property {
        #{$propName}: #{$propValue};
      }
    }
  }
}

$tb-effects: (
  kendo-box-shadow-depth-1: (
    box-shadow: (
      0 1.6px 3.6px 0 #00000022,
      0 0.3px 0.9px 0 #0000001c,
    ),
  ),
  kendo-box-shadow-depth-2: (
    box-shadow: (
      0 3.2px 7.2px 0 #00000022,
      0 0.6px 1.8px 0 #0000001c,
    ),
  ),
  kendo-box-shadow-depth-3: (
    box-shadow: (
      0 6.4px 14.4px 0 #00000022,
      0 1.2px 3.6px 0 #0000001c,
    ),
  ),
  kendo-box-shadow-depth-4: (
    box-shadow: (
      0 25.6px 57.6px 0 #00000038,
      0 4.8px 14.4px 0 #0000002e,
    ),
  ),
  tb-internal-none-effects: (
    box-shadow: (
      none,
    ),
    filter: (
      none,
    ),
    backdrop-filter: (
      none,
    ),
  ),
);

@mixin effects-classes($effects) {
  @each $selector, $property in $effects {
    &-#{$selector} {
      @each $propName, $propValue in $property {
        #{$propName}: $propValue;
      }
    }
  }
}
