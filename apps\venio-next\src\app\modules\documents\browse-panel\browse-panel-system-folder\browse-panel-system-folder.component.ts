import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { TabStripModule } from '@progress/kendo-angular-layout'
import {
  FlatBindingDirective,
  TreeListComponent,
  TreeListModule,
} from '@progress/kendo-angular-treelist'

import { IconsModule } from '@progress/kendo-angular-icons'
import { BrowsePanelActionControlsComponent } from '../browse-panel-action-controls/browse-panel-action-controls.component'
import {
  FolderTabTreeState,
  DynamicFolderFacade,
  DynamicFolderModel,
  FolderFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { Subject, filter, take, takeUntil } from 'rxjs'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import { FolderTabType } from '@venio/shared/models/constants'
import { UuidGenerator } from '@venio/util/uuid'
import {
  ConditionElement,
  ConditionType,
  GroupStackType,
} from '@venio/shared/models/interfaces'

@Component({
  selector: 'venio-browse-panel-system-folder-tab',
  standalone: true,
  imports: [
    CommonModule,
    TabStripModule,
    TreeListModule,
    IconsModule,
    BrowsePanelActionControlsComponent,
    SvgLoaderDirective,
    InputsModule,
    LoaderModule,
  ],
  templateUrl: './browse-panel-system-folder.component.html',
  styleUrls: ['./browse-panel-system-folder.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BrowsePanelSystemFolderComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  private unsubscribed$ = new Subject<void>()

  public systemDynamicFolders: DynamicFolderModel[] = []

  @ViewChild(FlatBindingDirective)
  public dataBinding: FlatBindingDirective

  @ViewChild(TreeListComponent)
  public systemFolderTreeList: TreeListComponent

  private breadcrumbFacade = inject(BreadcrumbFacade)

  public expandedFolderIds: number[] = []

  public selected: { itemKey: number }[] = []

  public rowIndex: number

  constructor(
    private dynamicFolderFacade: DynamicFolderFacade,
    private cdr: ChangeDetectorRef,
    private searchFacade: SearchFacade,
    private folderFacade: FolderFacade
  ) {}

  public ngOnInit(): void {
    this.fetchData()
    this.initSlices()
    this.#selectDynamicFolderSearchScope()
    this.#selectSelectedFolderTreeState()
  }

  private fetchData(): void {
    this.dynamicFolderFacade.fetchSystemDynamicFolders()
  }

  private initSlices(): void {
    this.dynamicFolderFacade.getSystemDynamicFolders$
      .pipe(
        filter((res) => !!res),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((data) => {
        this.cdr.markForCheck()
        this.systemDynamicFolders = data.map((folder) => {
          if (folder.folderId === 1) {
            return {
              ...folder,
              folderName: 'All Documents',
              parentFolderId: null,
              isPlaceholder: true,
            }
          }
          return folder
        })
      })
  }

  /* when selection change, search documents in the selected folder and load in the search result
   */
  public async onSelectionChange(e): Promise<void> {
    const selectedFolder = e.items[0].dataItem

    if (e.action === 'select') {
      // this.searchFacade.setStaticFolderSearchScope(null)
      // this.searchFacade.setDynamicFolderSearchScope(
      //   selectedFolder.parentFolderId ? selectedFolder : null
      // )

      if (selectedFolder.parentFolderId) {
        const payload = {
          id: UuidGenerator.uuid,
          groupStackType: GroupStackType.SYSTEM_FOLDER,
          checked: true,
          conditionType: ConditionType.Group,
          conditions: [
            {
              conditionSyntax: selectedFolder.searchSettings.searchExpression
                .isSqlMode
                ? `DYNAMIC_FOLDER="${selectedFolder.lineage}"`
                : selectedFolder.searchSettings.searchExpression.expression,
            },
          ] as ConditionElement[],
        }
        this.breadcrumbFacade.resetBreadcrumbCurrentStates()
        this.breadcrumbFacade.storeBreadcrumbs([payload])
        this.breadcrumbFacade.selectCompleteBreadcrumbSyntax$
          .pipe(take(1))
          .subscribe((searchExpression) => {
            // If the search expression is empty, default to FileId>0
            searchExpression = searchExpression || 'FileId>0'
            this.searchFacade.search({
              searchExpression: searchExpression,
              includePC: selectedFolder.searchSettings.includeParentChild,
              searchDuplicateOption:
                selectedFolder.searchSettings.searchDuplicateOption,
              dynamicFolderId: selectedFolder.folderId,
              isDynamicFolderGlobal: selectedFolder.isGlobal,
              isResetBaseGuid: true,
            })
          })
      } else {
        this.searchFacade.search({
          searchExpression: 'FileId>0',
          isResetBaseGuid: true,
        })
        this.breadcrumbFacade.resetBreadcrumbCurrentStates()
      }

      // clear the term if there is any in the input control
      this.searchFacade.resetSearchInputControls()
    }
  }

  /*
  Filter the tree list by folder name
  */
  public onFilter(value: string): void {
    this.dataBinding.filter = {
      logic: 'or',
      filters: [
        {
          field: 'folderName',
          operator: 'contains',
          value: value,
        },
      ],
    }
    this.dataBinding.rebind()
  }

  public ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }

  public ngAfterViewInit(): void {
    this.#scrollToSelectedNode()
  }

  /**
   * Selects and handles changes in the dynamic folder search scope.
   * @returns {void}
   */
  #selectDynamicFolderSearchScope(): void {
    this.searchFacade.getDynamicFolderSearchScope$
      .pipe(
        filter((folder) => !!folder),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((folder) => {
        this.selected = [{ itemKey: folder.folderId }]
      })
  }

  #selectSelectedFolderTreeState(): void {
    this.folderFacade.getSelectedFolderTreeState$
      .pipe(
        filter(
          (selectedFolderTreeState) =>
            !!selectedFolderTreeState &&
            selectedFolderTreeState.actionType === FolderTabType.SYSTEM
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((selectedFolderTreeState: FolderTabTreeState) => {
        this.cdr.markForCheck()
        this.expandedFolderIds = selectedFolderTreeState.expandedIds
        this.rowIndex = selectedFolderTreeState.rowIndex
      })
  }

  public onCellClick(e): void {
    this.folderFacade.setSelectedFolderTreeState({
      actionType: FolderTabType.SYSTEM,
      expandedIds: this.expandedFolderIds,
      rowIndex: e.rowIndex,
    })
  }

  #scrollToSelectedNode(): void {
    this.systemFolderTreeList?.scrollTo({ row: this.rowIndex })
  }
}
