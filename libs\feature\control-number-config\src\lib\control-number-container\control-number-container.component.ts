import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnD<PERSON>roy,
  signal,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  TabContentDirective,
  TabStripComponent,
  TabStripTabComponent,
} from '@progress/kendo-angular-layout'
import {
  IndicatorsModule,
  SkeletonComponent,
} from '@progress/kendo-angular-indicators'
import { ControlNumberConfigComponent } from '../control-number-config/control-number-config.component'
import { ControlNumberStatusComponent } from '../control-number-status/control-number-status.component'
import {
  AppIdentitiesTypes,
  IframeMessengerService,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import { DialogModule } from '@progress/kendo-angular-dialog'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import { Subject } from 'rxjs'
import { ControlNumberConfigFacade } from '@venio/data-access/review'

@Component({
  selector: 'venio-control-number-container',
  standalone: true,
  imports: [
    CommonModule,
    TabContentDirective,
    TabStripComponent,
    TabStripTabComponent,
    SkeletonComponent,
    ControlNumberConfigComponent,
    ControlNumberStatusComponent,
    DialogModule,
    IndicatorsModule,
    ButtonModule,
  ],
  templateUrl: './control-number-container.component.html',
  styleUrl: './control-number-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ControlNumberContainerComponent
  implements AfterViewInit, OnDestroy
{
  public controlNumberFacade = inject(ControlNumberConfigFacade)

  public cdr = inject(ChangeDetectorRef)

  private readonly toDestroy$ = new Subject<void>()

  private readonly iframeMessengerService = inject(IframeMessengerService)

  public isControlNumberConfigEnabled = true

  public selectedTabIndex = 0

  public currentTabName = ''

  public showSpinner = signal<boolean>(false)

  public projectId = 1

  @ViewChild('tabstrip') public tabstrip: TabStripComponent

  @ViewChild('controlNumberConfig')
  private controlConfigComponent: ControlNumberConfigComponent

  public ngAfterViewInit(): void {
    this.setInitialTab()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public tabSelected(event: any): void {
    this.selectedTabIndex = event.index
    this.currentTabName = event.title
  }

  public onClose(): void {
    this.iframeMessengerService.sendMessage({
      payload: {
        type: MessageType.UI_STATE_CHANGE,
        content: {
          action: 'closeControlNumberDialog',
        },
      },
      eventTriggeredFor: 'PARENT_WINDOW',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
    })
  }

  private setInitialTab(): void {
    this.currentTabName = this.isControlNumberConfigEnabled
      ? 'Control Number'
      : 'Status'
    this.tabstrip.selectTab(0)
    this.cdr.detectChanges()
  }

  public generate(): void {
    const payload = this.controlConfigComponent.getControlNumberDetails()
    this.controlNumberFacade.generateControlNumber(this.projectId, payload)
  }
}
