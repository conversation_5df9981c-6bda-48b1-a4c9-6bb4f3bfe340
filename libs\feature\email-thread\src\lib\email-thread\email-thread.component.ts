import {
  AfterViewChecked,
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  Inject,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ViewChild,
  inject,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { Subject, combineLatest, filter, take, takeUntil } from 'rxjs'
import { isEqual } from 'lodash'
import {
  CompositeLayoutState,
  DocumentsFacade,
  FieldFacade,
  ReviewPanelType,
  ReviewViewType,
  SearchFacade,
  SearchFieldViewerType,
  SearchResultRequestData,
} from '@venio/data-access/review'
import {
  ColumnBase,
  SelectableSettings,
  TreeListComponent,
  TreeListItem,
  TreeListModule,
} from '@progress/kendo-angular-treelist'
import * as emailThreadColorConstant from '@venio/data-access/document-utility'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { PopoverModule, TooltipModule } from '@progress/kendo-angular-tooltip'
import { ActivatedRoute } from '@angular/router'
import {
  ReviewPanelFacade,
  ReviewPanelSelectedDocumentModel,
} from '@venio/data-access/document-utility'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import {
  AppIdentitiesTypes,
  MessageType,
  WINDOW,
} from '@venio/data-access/iframe-messenger'
import { PageControlActionType } from '@venio/shared/models/constants'
import { environment } from '@venio/shared/environments'
import { LocalStorage } from '@venio/shared/storage'

@Component({
  selector: 'venio-email',
  standalone: true,
  imports: [
    CommonModule,
    TreeListModule,
    SvgLoaderDirective,
    PopoverModule,
    IndicatorsModule,
    TooltipModule,
  ],
  templateUrl: './email-thread.component.html',
  styleUrl: './email-thread.component.scss',
})
export class EmailThreadComponent
  implements OnDestroy, AfterViewInit, AfterViewChecked
{
  public toDestory$: Subject<void> = new Subject<void>()

  public tempTables: any

  public selectedDocuments: number[]

  public currentDocument: number

  public currentUserDetails = signal(null)

  public readonly tableData = signal<any[]>([])

  public readonly headers = signal<string[]>([])

  public currentPageSelectedDocuments = []

  public emailThreadColorConstant = emailThreadColorConstant // constant for email thread view colors

  public idField = '__Id' // field to be used as id for the treelist

  public parentIdField = '__treelistParentId' // field to be used as parent id for the treelist

  public selectableSettings: SelectableSettings = {
    mode: 'row',
    multiple: false,
    drag: false,
  }

  public expandedKeys = [] //used to bind and clear the expanded rows (needs to be collapsed on initial load instead of restoring the previous state)

  public pageSize: number

  public allFileIds: number[]

  public venioFieldIds: number[]

  private customFieldIds: number[]

  public selectedFields: string[] = []

  private sortedMetadataFields: string[]

  private defaultFieldIds: number[]

  public isEmailThreadLoading$ =
    this.reviewPanelFacade.selectIsEmailThreadLoading$

  public isInclusiveEmailOnlyView = false

  public isFieldsExists = true

  public hasEmailSubjectColumnExists = true

  public noRecordsMessage = 'Email thread not available for document'

  @ViewChild(TreeListComponent)
  public treelist: TreeListComponent

  // store the last known width of the treelist
  public lastKnownWidth = 0

  public documentTrackByFn(index: number, item: TreeListItem): string {
    return `${item.data[this.idField]}_${index}`
  }

  public get isTagPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isTagPanelPopout')
  }

  public get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  private layoutState: CompositeLayoutState = inject(CompositeLayoutState)

  constructor(
    private reviewPanelFacade: ReviewPanelFacade,
    private documentsFacade: DocumentsFacade,
    private searchFacade: SearchFacade,
    private fieldFacade: FieldFacade,
    private activatedRoute: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    @Inject(WINDOW) private windowRef: Window,
    private ngZone: NgZone
  ) {}

  public ngAfterViewInit(): void {
    this.getEmailThreadData()
    this.#populateEmailThread()
    this.fieldFacade.notifyFieldChanges.next()
  }

  // Workaround solution for the issue where the treelist does not fit/resize columns and scrollbar glitch even after with ngAfterViewInit.
  public ngAfterViewChecked(): void {
    if (this.treelist && this.lastKnownWidth === 0) {
      const currentWidth = this.treelist.wrapper.nativeElement.offsetWidth
      //Call fitColumns when the last known width is less than 10.
      if (this.lastKnownWidth < 10) {
        this.fitColumns()
        this.lastKnownWidth = currentWidth
      }
    }
  }

  public getEmailThreadData(): void {
    combineLatest([
      this.documentsFacade.getSelectedDocuments$,
      this.searchFacade.getSearchTempTables$,
      this.fieldFacade.notifyFieldChanges,
    ])
      .pipe(
        filter(([selectedDocuments, fieldSelection]) =>
          Boolean(selectedDocuments?.length > 0 && fieldSelection)
        ),
        takeUntil(this.toDestory$)
      )
      .subscribe(([selectedDocuments, tempTables]) => {
        const hasFieldsChanged = this.#updateFields()
        const hasFileIdChanged = !isEqual(
          selectedDocuments,
          this.selectedDocuments
        )
        if (!hasFieldsChanged && !hasFileIdChanged) return
        this.tempTables = tempTables
        this.selectedDocuments = selectedDocuments
        this.currentDocument = selectedDocuments[0]

        this.currentPageSelectedDocuments.pop()
        this.currentPageSelectedDocuments.push({
          itemKey: this.currentDocument,
        })
        this.#fetchEmailThread()
      })
  }

  #fetchEmailThread(): void {
    const requestModel: SearchResultRequestData = {
      pageNumber: 1,
      pageSize: 1,
      viewSession: {
        computedSearchTempTable: this.tempTables.computedSearchTempTable,
        searchResultTempTable: this.tempTables.searchResultTempTable,
      },
      venioFieldIds: this.venioFieldIds,
      customFieldIds: this.customFieldIds,
      overrideGroupSecurity: false,
      searchFieldViewerType: SearchFieldViewerType.List_View,
      viewType: ReviewViewType.EmailThread,
    }

    this.reviewPanelFacade.fetchEmailThreads(
      this.projectId,
      this.currentDocument,
      requestModel
    )
  }

  #populateEmailThread(): void {
    this.reviewPanelFacade.selectEmailThread$
      .pipe(takeUntil(this.toDestory$))
      .subscribe((response: any[]) => {
        if (response?.length > 0) {
          this.#showHideFields(this.selectedFields)
          if (!this.isFieldsExists) return
          const data = response.map((item: any) => {
            const metadata = Object.fromEntries(
              item.metadata.map((m: any) => [m.key, m.value])
            )
            metadata['isFileExistsSearchScope'] = item.isFileExistsSearchScope
            return metadata
          })

          this.expandedKeys = data.map((item) => item.__Id)

          const datasource = response[0].metadata
          this.headers.set(
            this.layoutState
              .getFieldsSortedByLayoutOrder(
                datasource,
                'key',
                ReviewPanelType.EmailThread
              )
              .map((field: any) => field.key)
              .filter(
                (f) => this.selectedFields.includes(f) && f !== 'Email Subject'
              )
          )

          this.tableData.set(
            data.map((item) => {
              return {
                ...item,
                __treelistParentId:
                  item.__ParentId === -1 ? null : item.__ParentId,
                __emailCategory: (item['__emailCategory'] || '').toUpperCase(),
                __doctype: (item['__doctype'] || '').toUpperCase(),
                __childCount: +item['__childCount'] || 0, // number of children for the email
                __subjectTextColor: this.#getTextColor(item), // set the color of the email subject based on the email type
              }
            })
          )
        } else {
          this.#clearEmailThreadData()
        }
        this.fitColumns()
        this.cdr.detectChanges()
      })
  }

  public onDetailsClicked(dataItem): void {
    const fileId = dataItem?.__FileID

    if (!fileId || Number(fileId) <= 0) return

    if (this.currentDocument === Number(fileId)) return

    const selectReviewPanelDocument: ReviewPanelSelectedDocumentModel = {
      currentfileId: this.currentDocument,
      documentNo: fileId,
      isDocumentExistsInSearchScope: dataItem.isFileExistsSearchScope, //??
    }

    this.reviewPanelFacade.setSelectedReviewPanelDocument(
      selectReviewPanelDocument
    )
    // Send an action event from the popout window to update data in the parent window.
    if (this.isTagPanelPopout) {
      this.#sendEmailThreadActionEvent(selectReviewPanelDocument)
      return
    }
  }

  /**
   * Flag to enable view details option
   * @param {any} dataItem data item
   * @returns {boolean} - true if the email is not a dummy or generated email, false otherwise
   */
  public enableDetailView(dataItem: any): boolean {
    const docType = (dataItem['__doctype'] || '').toUpperCase()
    return docType !== 'DUMMY_EMAIL' && docType !== 'GENERATED_EMAIL'
  }

  /**
   * Sends a email thread action event to the parent window.
   * @param {ReviewPanelSelectedDocumentModel} selectReviewPanelDocument - The selected document to include in the event payload.
   * @returns {void} This method does not return anything.
   */
  #sendEmailThreadActionEvent(
    selectReviewPanelDocument: ReviewPanelSelectedDocumentModel
  ): void {
    // Popout window is not open then return
    if (!this.isTagPanelPopout) return

    this.windowRef.opener.postMessage(
      {
        type: 'MICRO_APP_DATA_CHANGE',
        payload: {
          type: MessageType.WINDOW_CHANGE,
          content: {
            selectReviewPanelDocument: selectReviewPanelDocument,
            pageControlActionType: PageControlActionType.EMAIL_THREAD,
          },
        },
        eventTriggeredBy: AppIdentitiesTypes.UTILITY_PANEL_ACTION,
        iframeIdentity: AppIdentitiesTypes.UTILITY_PANEL,
        eventTriggeredFor: 'ALL_WINDOW',
      },
      environment.allowedOrigin
    )
  }

  /**
   * Returns the color code for the email subject text based on whether it is a reply, forward or missing email (dummy email)
   * @param {any} dataItem data item
   * @returns {string} color code for the email subject text
   */
  #getTextColor(dataItem: any): string {
    const emailSubject = (dataItem['__emailSubject'] || '').toLowerCase()
    const fileId = +dataItem['__FileID']

    if (fileId > 0 && emailSubject.startsWith('re:'))
      return this.emailThreadColorConstant.replyEmailTextColor
    else if (fileId > 0 && emailSubject.startsWith('fw:'))
      return this.emailThreadColorConstant.forwardEmailTextColor
    else if (fileId < 0)
      return this.emailThreadColorConstant.missingEmailTextColor

    return ''
  }

  /**
   * Used to disable edit icon for generated or missing emails
   * @param {any} dataItem - The data item
   * @returns {boolean} - true if the email is generated or missing, false otherwise
   */
  public isGeneratedOrMissingEmail(dataItem: any): boolean {
    const docType = dataItem['__doctype']?.toUpperCase()
    return docType === 'DUMMY_EMAIL' || docType === 'GENERATED_EMAIL'
  }

  /**
   * Controls checkbox state in  case of email thread view
   * @param {any} dataItem - The data item
   * @param {ColumnBase} column - The column
   * @param {number} columnIndex - The column index
   * @returns {boolean} - true if the row is selected, false otherwise
   */
  public isSelected = (
    dataItem: any,
    column?: ColumnBase,
    columnIndex?: number
  ): boolean => {
    const isselected = this.#isRowSelected(dataItem)
    return !!isselected && +dataItem['__FileID'] > 0
  }

  /**
   * Checks if the row is selected for email thread view.
   * @param {any} dataItem - The data item
   * @returns {boolean} - true if the row is selected, false otherwise
   */
  #isRowSelected(dataItem: any): boolean {
    return this.currentPageSelectedDocuments.find(
      (row) => row.itemKey === +dataItem['__FileID'] && row.itemKey > 0
    )
      ? true
      : false
  }

  /**
   * Handles selection event of custom checkbox in email thread view
   * @param {any} e - The event
   * @param {any} dataItem - The data item
   * @returns {void}
   */
  public itemSelected(e: any, dataItem: any): void {
    const checked = e.currentTarget.checked
    if (checked) {
      this.currentPageSelectedDocuments.push({
        itemKey: +dataItem['__FileID'],
      })
    } else {
      this.currentPageSelectedDocuments =
        this.currentPageSelectedDocuments.filter(
          (row) => row.itemKey !== +dataItem['__FileID']
        )
    }
    this.cdr.detectChanges()
  }

  /**
   * Returns the icon color based on the email type
   * @param {any} dataItem - The data item
   * @returns {string} The color of email icon
   */
  public getIconColor(dataItem: any): string {
    const docType = (dataItem['__doctype'] || '').toUpperCase()

    switch (docType) {
      case 'INCLUSIVE_EMAIL':
        return this.emailThreadColorConstant.inclusiveEmailIconColor
      case 'GENERATED_EMAIL':
        return this.emailThreadColorConstant.generatedEmailIconColor
      case 'DUMMY_EMAIL':
        return this.emailThreadColorConstant.missingEmailIconColor
      default:
        return this.emailThreadColorConstant.defaultEmailIconColor
    }
  }

  /**
   * Returns the svg path for the email based on the category and child count
   * @param {any} dataItem - The data item
   * @returns {string} - The svg path
   */
  public getEmailIcon(dataItem: any): string {
    const category = dataItem['__emailCategory']
    const childCount = dataItem['__childCount']

    let iconPath = ''

    switch (category) {
      case 'INTERNAL':
      case 'EXTERNAL':
        iconPath =
          childCount === 0
            ? 'assets/svg/email.svg'
            : 'assets/svg/email-attachment.svg'
        break
      case 'INBOUND':
        iconPath =
          childCount === 0
            ? 'assets/svg/email-inbound.svg'
            : 'assets/svg/email-inbound-with-attachment.svg'
        break
      case 'OUTBOUND':
        iconPath =
          childCount === 0
            ? 'assets/svg/email-outbound.svg'
            : 'assets/svg/email-outbound-with-attachment.svg'
        break
    }

    return iconPath
  }

  /**
   *  Returns the title for the email icon
   * @param {any} dataItem - The data item
   * @returns {string} - The title of the icon
   */
  public getIconTitle(dataItem: any): string {
    const docType = dataItem['__doctype']

    switch (docType) {
      case 'INCLUSIVE_EMAIL':
        return 'Inclusive Email'

      case 'GENERATED_EMAIL':
        return 'Generated Email'

      case 'DUMMY_EMAIL':
        return 'Missing Email'

      default:
        return this.getDocTypeTooltip(
          dataItem['__emailCategory'],
          dataItem['__childCount']
        )
    }
  }

  /**
   * Returns the color code for the email subject text based on whether it is a reply, forward or missing email (dummy email)
   * Additionally, if the email thread is being filtered for viewing only inclusive email, the non-inclusive parent email will be shown in grey color
   * @param {any} dataItem current data item
   * @returns {string} color code for the email subject text
   */
  public getSubjectTextColor(dataItem: any): string {
    // in case of inclusive email only view, the non-inclusive parent email will be shown in grey color
    if (
      this.isInclusiveEmailOnlyView &&
      dataItem.__doctype !== 'INCLUSIVE_EMAIL'
    ) {
      return emailThreadColorConstant.nonInclusiveEmailTextColor
    }

    // else return the color based on the email type that is set earlier while binding the data
    return dataItem.__subjectTextColor as string
  }

  /**
   * Returns the tooltip for the email icon based on the category and child count
   * @param {string} category category of email
   * @param {number} childCount number of children for the email
   * @returns {string} tooltip for the email icon
   */
  private getDocTypeTooltip(category: string, childCount: number): string {
    let toolTip = ''

    switch (category) {
      case 'INTERNAL':
      case 'EXTERNAL':
        toolTip = childCount === 0 ? 'Email' : 'Email With Attachment'
        break
      case 'INBOUND':
        toolTip =
          childCount === 0 ? 'Inbound Email' : 'Inbound Email With Attachment'
        break
      case 'OUTBOUND':
        toolTip =
          childCount === 0 ? 'Outbound Email' : 'Outbound Email With Attachment'
        break
    }

    return toolTip
  }

  #clearEmailThreadData(): void {
    this.tableData.set([])
    this.headers.set([])
    this.fitColumns()
  }

  #resetEmailThreadData(): void {
    this.reviewPanelFacade.resetReviewPanelState(['emailThreadResponse'])
  }

  #updateFields(): boolean {
    const selectedFields = this.layoutState
      .userSelectedLayout()
      .layoutPanels.find((p) => p.panelName === ReviewPanelType.EmailThread)
      ?.fields.filter((f) => f.isSelected)
    const hasFieldsChanged = !isEqual(
      this.selectedFields,
      selectedFields.map((f) => f.fieldName)
    )
    this.selectedFields = selectedFields.map((f) => f.fieldName)
    this.hasEmailSubjectColumnExists =
      this.selectedFields.includes('Email Subject')
    this.venioFieldIds = selectedFields
      .filter((f) => !f.isCustomField)
      .map((f) => f.fieldId)
    this.customFieldIds = selectedFields
      .filter((f) => f.isCustomField)
      .map((f) => f.fieldId)
    if (this.customFieldIds?.length === 0) this.customFieldIds = [-1]
    return hasFieldsChanged
  }

  #showHideFields(visibleMetaDataFields: string[]): void {
    this.isFieldsExists = Boolean(visibleMetaDataFields?.[0])
    // clear the headers and tableData if no fields are visible
    if (!this.isFieldsExists) {
      this.#clearEmailThreadData()
    }
  }

  public fitColumns(): void {
    this.ngZone.onStable
      .asObservable()
      .pipe(take(1))
      .subscribe(() => {
        this.treelist.autoFitColumns()
      })
  }

  public ngOnDestroy(): void {
    this.#resetEmailThreadData()
    this.#clearEmailThreadData()
    this.toDestory$.next()
    this.toDestory$.complete()
  }
}
