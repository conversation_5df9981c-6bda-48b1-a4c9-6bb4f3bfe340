import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { environment } from '@venio/shared/environments'
import { Observable } from 'rxjs'
import { GenerateControlNumberRequestModel } from '../models/interfaces'

@Injectable({
  providedIn: 'root',
})
export class ControlNumberConfigService {
  constructor(private http: HttpClient) {}

  private get _apiUrl(): string {
    // remove trailing slash
    return environment.apiUrl.replace(/\/+$/, '')
  }

  //TODO: Update the API endpoints during API integration
  public fetchCustodianMedia$<T>(projectId: number): Observable<T> {
    return this.http.get<T>(
      `${this._apiUrl}/common/project/${projectId}/GetDummyMediaData`
    )
  }

  //TODO: Update the API endpoints during API integration
  public fetchCustodianMediaStatus$<T>(projectId: number): Observable<T> {
    return this.http.get<T>(
      `${this._apiUrl}/common/project/${projectId}/GetStatusData`
    )
  }

  //TODO:
  public generateControlNumber$<T>(
    projectId: number,
    payload: GenerateControlNumberRequestModel
  ): Observable<T> {
    return this.http.post<T>(
      `${this._apiUrl}/common/project/${projectId}/GenerateControlNumber`,
      payload
    )
  }
}
