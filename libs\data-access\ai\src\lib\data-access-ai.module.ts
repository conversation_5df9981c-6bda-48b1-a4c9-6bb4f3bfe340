import { NgModule } from '@angular/core'
import { StoreModule } from '@ngrx/store'
import { EffectsModule } from '@ngrx/effects'
import { AI_FEATURE_KEY, aiReducer, AiEffects, AiFacade } from './+state'
import { AiSearchService, EciDashboardService } from './services'

@NgModule({
  imports: [
    StoreModule.forFeature(AI_FEATURE_KEY, aiReducer),
    EffectsModule.forFeature([AiEffects]),
  ],
  providers: [AiFacade, AiSearchService, EciDashboardService],
})
export class DataAccessAiModule {}
